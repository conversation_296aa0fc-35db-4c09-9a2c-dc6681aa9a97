import './polyfills.server.mjs';
import{a as w,b as te,d as vf,i as $n}from"./chunk-2FGBTQRU.mjs";function M(e){return typeof e=="function"}function Za(e){return M(e==null?void 0:e.lift)}function P(e){return t=>{if(Za(t))return t.lift(function(r){try{return e(r,this)}catch(n){this.error(n)}});throw new TypeError("Unable to lift unknown Observable type")}}function Ht(e){return P((t,r)=>{try{t.subscribe(r)}finally{r.add(e)}})}function Df(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function a(l){try{c(n.next(l))}catch(d){s(d)}}function u(l){try{c(n.throw(l))}catch(d){s(d)}}function c(l){l.done?i(l.value):o(l.value).then(a,u)}c((n=n.apply(e,t||[])).next())})}function yf(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function hn(e){return this instanceof hn?(this.v=e,this):new hn(e)}function wf(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(h){return function(p){return Promise.resolve(p).then(h,d)}}function a(h,p){n[h]&&(o[h]=function(y){return new Promise(function(m,v){i.push([h,y,m,v])>1||u(h,y)})},p&&(o[h]=p(o[h])))}function u(h,p){try{c(n[h](p))}catch(y){f(i[0][3],y)}}function c(h){h.value instanceof hn?Promise.resolve(h.value.v).then(l,d):f(i[0][2],h)}function l(h){u("next",h)}function d(h){u("throw",h)}function f(h,p){h(p),i.shift(),i.length&&u(i[0][0],i[0][1])}}function Ef(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],r;return t?t.call(e):(e=typeof yf=="function"?yf(e):e[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(i){r[i]=e[i]&&function(s){return new Promise(function(a,u){s=e[i](s),o(a,u,s.done,s.value)})}}function o(i,s,a,u){Promise.resolve(u).then(function(c){i({value:c,done:a})},s)}}var Vn=e=>e&&typeof e.length=="number"&&typeof e!="function";function fi(e){return M(e==null?void 0:e.then)}function Un(e){let r=e(n=>{Error.call(n),n.stack=new Error().stack});return r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r}var hi=Un(e=>function(r){e(this),this.message=r?`${r.length} errors occurred during unsubscription:
${r.map((n,o)=>`${o+1}) ${n.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=r});function pn(e,t){if(e){let r=e.indexOf(t);0<=r&&e.splice(r,1)}}var J=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:r}=this;if(r)if(this._parentage=null,Array.isArray(r))for(let i of r)i.remove(this);else r.remove(this);let{initialTeardown:n}=this;if(M(n))try{n()}catch(i){t=i instanceof hi?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{If(i)}catch(s){t=t!=null?t:[],s instanceof hi?t=[...t,...s.errors]:t.push(s)}}if(t)throw new hi(t)}}add(t){var r;if(t&&t!==this)if(this.closed)If(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(r=this._finalizers)!==null&&r!==void 0?r:[]).push(t)}}_hasParent(t){let{_parentage:r}=this;return r===t||Array.isArray(r)&&r.includes(t)}_addParent(t){let{_parentage:r}=this;this._parentage=Array.isArray(r)?(r.push(t),r):r?[r,t]:t}_removeParent(t){let{_parentage:r}=this;r===t?this._parentage=null:Array.isArray(r)&&pn(r,t)}remove(t){let{_finalizers:r}=this;r&&pn(r,t),t instanceof e&&t._removeParent(this)}};J.EMPTY=(()=>{let e=new J;return e.closed=!0,e})();var Ya=J.EMPTY;function pi(e){return e instanceof J||e&&"closed"in e&&M(e.remove)&&M(e.add)&&M(e.unsubscribe)}function If(e){M(e)?e():e.unsubscribe()}var Qe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Bn={setTimeout(e,t,...r){let{delegate:n}=Bn;return n!=null&&n.setTimeout?n.setTimeout(e,t,...r):setTimeout(e,t,...r)},clearTimeout(e){let{delegate:t}=Bn;return((t==null?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function gi(e){Bn.setTimeout(()=>{let{onUnhandledError:t}=Qe;if(t)t(e);else throw e})}function Yr(){}var Cf=Qa("C",void 0,void 0);function bf(e){return Qa("E",void 0,e)}function Mf(e){return Qa("N",e,void 0)}function Qa(e,t,r){return{kind:e,value:t,error:r}}var gn=null;function Hn(e){if(Qe.useDeprecatedSynchronousErrorHandling){let t=!gn;if(t&&(gn={errorThrown:!1,error:null}),e(),t){let{errorThrown:r,error:n}=gn;if(gn=null,r)throw n}}else e()}function Tf(e){Qe.useDeprecatedSynchronousErrorHandling&&gn&&(gn.errorThrown=!0,gn.error=e)}var mn=class extends J{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,pi(t)&&t.add(this)):this.destination=uD}static create(t,r,n){return new Ke(t,r,n)}next(t){this.isStopped?Ja(Mf(t),this):this._next(t)}error(t){this.isStopped?Ja(bf(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Ja(Cf,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},sD=Function.prototype.bind;function Ka(e,t){return sD.call(e,t)}var Xa=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:r}=this;if(r.next)try{r.next(t)}catch(n){mi(n)}}error(t){let{partialObserver:r}=this;if(r.error)try{r.error(t)}catch(n){mi(n)}else mi(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(r){mi(r)}}},Ke=class extends mn{constructor(t,r,n){super();let o;if(M(t)||!t)o={next:t!=null?t:void 0,error:r!=null?r:void 0,complete:n!=null?n:void 0};else{let i;this&&Qe.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Ka(t.next,i),error:t.error&&Ka(t.error,i),complete:t.complete&&Ka(t.complete,i)}):o=t}this.destination=new Xa(o)}};function mi(e){Qe.useDeprecatedSynchronousErrorHandling?Tf(e):gi(e)}function aD(e){throw e}function Ja(e,t){let{onStoppedNotification:r}=Qe;r&&Bn.setTimeout(()=>r(e,t))}var uD={closed:!0,next:Yr,error:aD,complete:Yr};var zn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Pe(e){return e}function eu(...e){return tu(e)}function tu(e){return e.length===0?Pe:e.length===1?e[0]:function(r){return e.reduce((n,o)=>o(n),r)}}var k=(()=>{class e{constructor(r){r&&(this._subscribe=r)}lift(r){let n=new e;return n.source=this,n.operator=r,n}subscribe(r,n,o){let i=lD(r)?r:new Ke(r,n,o);return Hn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(r){try{return this._subscribe(r)}catch(n){r.error(n)}}forEach(r,n){return n=Sf(n),new n((o,i)=>{let s=new Ke({next:a=>{try{r(a)}catch(u){i(u),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(r){var n;return(n=this.source)===null||n===void 0?void 0:n.subscribe(r)}[zn](){return this}pipe(...r){return tu(r)(this)}toPromise(r){return r=Sf(r),new r((n,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>n(i))})}}return e.create=t=>new e(t),e})();function Sf(e){var t;return(t=e!=null?e:Qe.Promise)!==null&&t!==void 0?t:Promise}function cD(e){return e&&M(e.next)&&M(e.error)&&M(e.complete)}function lD(e){return e&&e instanceof mn||cD(e)&&pi(e)}function vi(e){return M(e[zn])}function yi(e){return Symbol.asyncIterator&&M(e==null?void 0:e[Symbol.asyncIterator])}function Di(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function dD(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var wi=dD();function Ei(e){return M(e==null?void 0:e[wi])}function Ii(e){return wf(this,arguments,function*(){let r=e.getReader();try{for(;;){let{value:n,done:o}=yield hn(r.read());if(o)return yield hn(void 0);yield yield hn(n)}}finally{r.releaseLock()}})}function Ci(e){return M(e==null?void 0:e.getReader)}function z(e){if(e instanceof k)return e;if(e!=null){if(vi(e))return fD(e);if(Vn(e))return hD(e);if(fi(e))return pD(e);if(yi(e))return xf(e);if(Ei(e))return gD(e);if(Ci(e))return mD(e)}throw Di(e)}function fD(e){return new k(t=>{let r=e[zn]();if(M(r.subscribe))return r.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function hD(e){return new k(t=>{for(let r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()})}function pD(e){return new k(t=>{e.then(r=>{t.closed||(t.next(r),t.complete())},r=>t.error(r)).then(null,gi)})}function gD(e){return new k(t=>{for(let r of e)if(t.next(r),t.closed)return;t.complete()})}function xf(e){return new k(t=>{vD(e,t).catch(r=>t.error(r))})}function mD(e){return xf(Ii(e))}function vD(e,t){var r,n,o,i;return Df(this,void 0,void 0,function*(){try{for(r=Ef(e);n=yield r.next(),!n.done;){let s=n.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{n&&!n.done&&(i=r.return)&&(yield i.call(r))}finally{if(o)throw o.error}}t.complete()})}function N(e,t,r,n,o){return new nu(e,t,r,n,o)}var nu=class extends mn{constructor(t,r,n,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=r?function(a){try{r(a)}catch(u){t.error(u)}}:super._next,this._error=o?function(a){try{o(a)}catch(u){t.error(u)}finally{this.unsubscribe()}}:super._error,this._complete=n?function(){try{n()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:r}=this;super.unsubscribe(),!r&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Af(e){return P((t,r)=>{let n=!1,o=null,i=null,s=!1,a=()=>{if(i==null||i.unsubscribe(),i=null,n){n=!1;let c=o;o=null,r.next(c)}s&&r.complete()},u=()=>{i=null,s&&r.complete()};t.subscribe(N(r,c=>{n=!0,o=c,i||z(e(c)).subscribe(i=N(r,a,u))},()=>{s=!0,(!n||!i||i.closed)&&r.complete()}))})}var bi=class extends J{constructor(t,r){super()}schedule(t,r=0){return this}};var Qr={setInterval(e,t,...r){let{delegate:n}=Qr;return n!=null&&n.setInterval?n.setInterval(e,t,...r):setInterval(e,t,...r)},clearInterval(e){let{delegate:t}=Qr;return((t==null?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0};var qn=class extends bi{constructor(t,r){super(t,r),this.scheduler=t,this.work=r,this.pending=!1}schedule(t,r=0){var n;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,r)),this.pending=!0,this.delay=r,this.id=(n=this.id)!==null&&n!==void 0?n:this.requestAsyncId(i,this.id,r),this}requestAsyncId(t,r,n=0){return Qr.setInterval(t.flush.bind(t,this),n)}recycleAsyncId(t,r,n=0){if(n!=null&&this.delay===n&&this.pending===!1)return r;r!=null&&Qr.clearInterval(r)}execute(t,r){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let n=this._execute(t,r);if(n)return n;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,r){let n=!1,o;try{this.work(t)}catch(i){n=!0,o=i||new Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:r}=this,{actions:n}=r;this.work=this.state=this.scheduler=null,this.pending=!1,pn(n,this),t!=null&&(this.id=this.recycleAsyncId(r,t,null)),this.delay=null,super.unsubscribe()}}};var Kr={now(){return(Kr.delegate||Date).now()},delegate:void 0};var Wn=class e{constructor(t,r=e.now){this.schedulerActionCtor=t,this.now=r}schedule(t,r=0,n){return new this.schedulerActionCtor(this,t).schedule(n,r)}};Wn.now=Kr.now;var Gn=class extends Wn{constructor(t,r=Wn.now){super(t,r),this.actions=[],this._active=!1}flush(t){let{actions:r}=this;if(this._active){r.push(t);return}let n;this._active=!0;do if(n=t.execute(t.state,t.delay))break;while(t=r.shift());if(this._active=!1,n){for(;t=r.shift();)t.unsubscribe();throw n}}};var vn=new Gn(qn),_f=vn;function Mi(e){return e&&M(e.schedule)}function Nf(e){return e instanceof Date&&!isNaN(e)}function Ti(e=0,t,r=_f){let n=-1;return t!=null&&(Mi(t)?r=t:n=t),new k(o=>{let i=Nf(e)?+e-r.now():e;i<0&&(i=0);let s=0;return r.schedule(function(){o.closed||(o.next(s++),0<=n?this.schedule(void 0,n):o.complete())},i)})}function yD(e,t=vn){return Af(()=>Ti(e,t))}function ru(e){return e[e.length-1]}function Si(e){return M(ru(e))?e.pop():void 0}function pt(e){return Mi(ru(e))?e.pop():void 0}function Rf(e,t){return typeof ru(e)=="number"?e.pop():t}function Se(e,t,r,n=0,o=!1){let i=t.schedule(function(){r(),o?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(i),!o)return i}function St(e){return P((t,r)=>{let n=null,o=!1,i;n=t.subscribe(N(r,void 0,void 0,s=>{i=z(e(s,St(e)(t))),n?(n.unsubscribe(),n=null,i.subscribe(r)):o=!0})),o&&(n.unsubscribe(),n=null,i.subscribe(r))})}var{isArray:DD}=Array,{getPrototypeOf:wD,prototype:ED,keys:ID}=Object;function xi(e){if(e.length===1){let t=e[0];if(DD(t))return{args:t,keys:null};if(CD(t)){let r=ID(t);return{args:r.map(n=>t[n]),keys:r}}}return{args:e,keys:null}}function CD(e){return e&&typeof e=="object"&&wD(e)===ED}function Ai(e,t=0){return P((r,n)=>{r.subscribe(N(n,o=>Se(n,e,()=>n.next(o),t),()=>Se(n,e,()=>n.complete(),t),o=>Se(n,e,()=>n.error(o),t)))})}function _i(e,t=0){return P((r,n)=>{n.add(e.schedule(()=>r.subscribe(n),t))})}function Of(e,t){return z(e).pipe(_i(t),Ai(t))}function Pf(e,t){return z(e).pipe(_i(t),Ai(t))}function Ff(e,t){return new k(r=>{let n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})})}function kf(e,t){return new k(r=>{let n;return Se(r,t,()=>{n=e[wi](),Se(r,t,()=>{let o,i;try{({value:o,done:i}=n.next())}catch(s){r.error(s);return}i?r.complete():r.next(o)},0,!0)}),()=>M(n==null?void 0:n.return)&&n.return()})}function Ni(e,t){if(!e)throw new Error("Iterable cannot be null");return new k(r=>{Se(r,t,()=>{let n=e[Symbol.asyncIterator]();Se(r,t,()=>{n.next().then(o=>{o.done?r.complete():r.next(o.value)})},0,!0)})})}function Lf(e,t){return Ni(Ii(e),t)}function jf(e,t){if(e!=null){if(vi(e))return Of(e,t);if(Vn(e))return Ff(e,t);if(fi(e))return Pf(e,t);if(yi(e))return Ni(e,t);if(Ei(e))return kf(e,t);if(Ci(e))return Lf(e,t)}throw Di(e)}function Q(e,t){return t?jf(e,t):z(e)}function F(e,t){return P((r,n)=>{let o=0;r.subscribe(N(n,i=>{n.next(e.call(t,i,o++))}))})}var{isArray:bD}=Array;function MD(e,t){return bD(t)?e(...t):e(t)}function Zn(e){return F(t=>MD(e,t))}function Ri(e,t){return e.reduce((r,n,o)=>(r[n]=t[o],r),{})}function Jr(...e){let t=pt(e),r=Si(e),{args:n,keys:o}=xi(e);if(n.length===0)return Q([],t);let i=new k(TD(n,t,o?s=>Ri(o,s):Pe));return r?i.pipe(Zn(r)):i}function TD(e,t,r=Pe){return n=>{$f(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let u=0;u<o;u++)$f(t,()=>{let c=Q(e[u],t),l=!1;c.subscribe(N(n,d=>{i[u]=d,l||(l=!0,a--),a||n.next(r(i.slice()))},()=>{--s||n.complete()}))},n)},n)}}function $f(e,t,r){e?Se(r,e,t):t()}function Vf(e,t,r,n,o,i,s,a){let u=[],c=0,l=0,d=!1,f=()=>{d&&!u.length&&!c&&t.complete()},h=y=>c<n?p(y):u.push(y),p=y=>{i&&t.next(y),c++;let m=!1;z(r(y,l++)).subscribe(N(t,v=>{o==null||o(v),i?h(v):t.next(v)},()=>{m=!0},void 0,()=>{if(m)try{for(c--;u.length&&c<n;){let v=u.shift();s?Se(t,s,()=>p(v)):p(v)}f()}catch(v){t.error(v)}}))};return e.subscribe(N(t,h,()=>{d=!0,f()})),()=>{a==null||a()}}function ne(e,t,r=1/0){return M(t)?ne((n,o)=>F((i,s)=>t(n,i,o,s))(z(e(n,o))),r):(typeof t=="number"&&(r=t),P((n,o)=>Vf(n,o,e,r)))}function Uf(e,t,r,n,o){return(i,s)=>{let a=r,u=t,c=0;i.subscribe(N(s,l=>{let d=c++;u=a?e(u,l,d):(a=!0,l),n&&s.next(u)},o&&(()=>{a&&s.next(u),s.complete()})))}}function gt(e=1/0){return ne(Pe,e)}function Bf(){return gt(1)}function xt(e,t){return M(t)?ne(e,t,1):ne(e,1)}var Hf=Un(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var se=(()=>{class e extends k{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(r){let n=new Oi(this,this);return n.operator=r,n}_throwIfClosed(){if(this.closed)throw new Hf}next(r){Hn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let n of this.currentObservers)n.next(r)}})}error(r){Hn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=r;let{observers:n}=this;for(;n.length;)n.shift().error(r)}})}complete(){Hn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:r}=this;for(;r.length;)r.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var r;return((r=this.observers)===null||r===void 0?void 0:r.length)>0}_trySubscribe(r){return this._throwIfClosed(),super._trySubscribe(r)}_subscribe(r){return this._throwIfClosed(),this._checkFinalizedStatuses(r),this._innerSubscribe(r)}_innerSubscribe(r){let{hasError:n,isStopped:o,observers:i}=this;return n||o?Ya:(this.currentObservers=null,i.push(r),new J(()=>{this.currentObservers=null,pn(i,r)}))}_checkFinalizedStatuses(r){let{hasError:n,thrownError:o,isStopped:i}=this;n?r.error(o):i&&r.complete()}asObservable(){let r=new k;return r.source=this,r}}return e.create=(t,r)=>new Oi(t,r),e})(),Oi=class extends se{constructor(t,r){super(),this.destination=t,this.source=r}next(t){var r,n;(n=(r=this.destination)===null||r===void 0?void 0:r.next)===null||n===void 0||n.call(r,t)}error(t){var r,n;(n=(r=this.destination)===null||r===void 0?void 0:r.error)===null||n===void 0||n.call(r,t)}complete(){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||r===void 0||r.call(t)}_subscribe(t){var r,n;return(n=(r=this.source)===null||r===void 0?void 0:r.subscribe(t))!==null&&n!==void 0?n:Ya}};function SD(e,t=vn){return P((r,n)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let c=i;i=null,n.next(c)}};function u(){let c=s+e,l=t.now();if(l<c){o=this.schedule(void 0,c-l),n.add(o);return}a()}r.subscribe(N(n,c=>{i=c,s=t.now(),o||(o=t.schedule(u,e),n.add(o))},()=>{a(),n.complete()},void 0,()=>{i=o=null}))})}function zt(e){return P((t,r)=>{let n=!1;t.subscribe(N(r,o=>{n=!0,r.next(o)},()=>{n||r.next(e),r.complete()}))})}function Yn(...e){return Bf()(Q(e,pt(e)))}var Ee=new k(e=>e.complete());function At(e){return e<=0?()=>Ee:P((t,r)=>{let n=0;t.subscribe(N(r,o=>{++n<=e&&(r.next(o),e<=n&&r.complete())}))})}function ou(e){return F(()=>e)}function b(...e){let t=pt(e);return Q(e,t)}function Qn(e,t){let r=M(e)?e:()=>e,n=o=>o.error(r());return new k(t?o=>t.schedule(n,0,o):n)}function xe(e,t){return P((r,n)=>{let o=0;r.subscribe(N(n,i=>e.call(t,i,o++)&&n.next(i)))})}var Je=Un(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Pi(e=xD){return P((t,r)=>{let n=!1;t.subscribe(N(r,o=>{n=!0,r.next(o)},()=>n?r.complete():r.error(e())))})}function xD(){return new Je}function Xe(e,t){let r=arguments.length>=2;return n=>n.pipe(e?xe((o,i)=>e(o,i,n)):Pe,At(1),r?zt(t):Pi(()=>new Je))}function Kn(e){return e<=0?()=>Ee:P((t,r)=>{let n=[];t.subscribe(N(r,o=>{n.push(o),e<n.length&&n.shift()},()=>{for(let o of n)r.next(o);r.complete()},void 0,()=>{n=null}))})}function iu(e,t){let r=arguments.length>=2;return n=>n.pipe(e?xe((o,i)=>e(o,i,n)):Pe,Kn(1),r?zt(t):Pi(()=>new Je))}function Jn(){return P((e,t)=>{let r=null;e._refCount++;let n=N(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){r=null;return}let o=e._connection,i=r;r=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(n),n.closed||(r=e.connect())})}var Xn=class extends k{constructor(t,r){super(),this.source=t,this.subjectFactory=r,this._subject=null,this._refCount=0,this._connection=null,Za(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t==null||t.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new J;let r=this.getSubject();t.add(this.source.subscribe(N(r,void 0,()=>{this._teardown(),r.complete()},n=>{this._teardown(),r.error(n)},()=>this._teardown()))),t.closed&&(this._connection=null,t=J.EMPTY)}return t}refCount(){return Jn()(this)}};var pe=class extends se{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let r=super._subscribe(t);return!r.closed&&t.next(this._value),r}getValue(){let{hasError:t,thrownError:r,_value:n}=this;if(t)throw r;return this._throwIfClosed(),n}next(t){super.next(this._value=t)}};var Fi=class extends se{constructor(t=1/0,r=1/0,n=Kr){super(),this._bufferSize=t,this._windowTime=r,this._timestampProvider=n,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=r===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,r)}next(t){let{isStopped:r,_buffer:n,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;r||(n.push(t),!o&&n.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let r=this._innerSubscribe(t),{_infiniteTimeWindow:n,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=n?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),r}_trimBuffer(){let{_bufferSize:t,_timestampProvider:r,_buffer:n,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<n.length&&n.splice(0,n.length-i),!o){let s=r.now(),a=0;for(let u=1;u<n.length&&n[u]<=s;u+=2)a=u;a&&n.splice(0,a+1)}}};function AD(e=0,t=vn){return e<0&&(e=0),Ti(e,e,t)}function su(e,t){return P(Uf(e,t,arguments.length>=2,!0))}function zf(e={}){let{connector:t=()=>new se,resetOnError:r=!0,resetOnComplete:n=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,u,c=0,l=!1,d=!1,f=()=>{a==null||a.unsubscribe(),a=void 0},h=()=>{f(),s=u=void 0,l=d=!1},p=()=>{let y=s;h(),y==null||y.unsubscribe()};return P((y,m)=>{c++,!d&&!l&&f();let v=u=u!=null?u:t();m.add(()=>{c--,c===0&&!d&&!l&&(a=au(p,o))}),v.subscribe(m),!s&&c>0&&(s=new Ke({next:A=>v.next(A),error:A=>{d=!0,f(),a=au(h,r,A),v.error(A)},complete:()=>{l=!0,f(),a=au(h,n),v.complete()}}),z(y).subscribe(s))})(i)}}function au(e,t,...r){if(t===!0){e();return}if(t===!1)return;let n=new Ke({next:()=>{n.unsubscribe(),e()}});return z(t(...r)).subscribe(n)}function qf(e,t,r){let n,o=!1;return e&&typeof e=="object"?{bufferSize:n=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:r}=e:n=e!=null?e:1/0,zf({connector:()=>new Fi(n,t,r),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function uu(...e){let t=pt(e);return P((r,n)=>{(t?Yn(e,r,t):Yn(e,r)).subscribe(n)})}function Ie(e,t){return P((r,n)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&n.complete();r.subscribe(N(n,u=>{o==null||o.unsubscribe();let c=0,l=i++;z(e(u,l)).subscribe(o=N(n,d=>n.next(t?t(u,d,l,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function cu(e){return P((t,r)=>{z(e).subscribe(N(r,()=>r.complete(),Yr)),!r.closed&&t.subscribe(r)})}function ae(e,t,r){let n=M(e)||t||r?{next:e,error:t,complete:r}:e;return n?P((o,i)=>{var s;(s=n.subscribe)===null||s===void 0||s.call(n);let a=!0;o.subscribe(N(i,u=>{var c;(c=n.next)===null||c===void 0||c.call(n,u),i.next(u)},()=>{var u;a=!1,(u=n.complete)===null||u===void 0||u.call(n),i.complete()},u=>{var c;a=!1,(c=n.error)===null||c===void 0||c.call(n,u),i.error(u)},()=>{var u,c;a&&((u=n.unsubscribe)===null||u===void 0||u.call(n)),(c=n.finalize)===null||c===void 0||c.call(n)}))}):Pe}function _D(e,t){return Object.is(e,t)}var ue=null,ki=!1,Li=1,qt=Symbol("SIGNAL");function j(e){let t=ue;return ue=e,t}var du={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function fu(e){if(ki)throw new Error("");if(ue===null)return;ue.consumerOnSignalRead(e);let t=ue.nextProducerIndex++;if(er(ue),t<ue.producerNode.length&&ue.producerNode[t]!==e&&Xr(ue)){let r=ue.producerNode[t];ji(r,ue.producerIndexOfThis[t])}ue.producerNode[t]!==e&&(ue.producerNode[t]=e,ue.producerIndexOfThis[t]=Xr(ue)?Kf(e,ue,t):0),ue.producerLastReadVersion[t]=e.version}function ND(){Li++}function RD(e){if(!(Xr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Li)){if(!e.producerMustRecompute(e)&&!hu(e)){e.dirty=!1,e.lastCleanEpoch=Li;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Li}}function Wf(e){if(e.liveConsumerNode===void 0)return;let t=ki;ki=!0;try{for(let r of e.liveConsumerNode)r.dirty||OD(r)}finally{ki=t}}function Gf(){return(ue==null?void 0:ue.consumerAllowSignalWrites)!==!1}function OD(e){var t;e.dirty=!0,Wf(e),(t=e.consumerMarkedDirty)==null||t.call(e,e)}function Zf(e){return e&&(e.nextProducerIndex=0),j(e)}function Yf(e,t){if(j(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Xr(e))for(let r=e.nextProducerIndex;r<e.producerNode.length;r++)ji(e.producerNode[r],e.producerIndexOfThis[r]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function hu(e){er(e);for(let t=0;t<e.producerNode.length;t++){let r=e.producerNode[t],n=e.producerLastReadVersion[t];if(n!==r.version||(RD(r),n!==r.version))return!0}return!1}function Qf(e){if(er(e),Xr(e))for(let t=0;t<e.producerNode.length;t++)ji(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Kf(e,t,r){if(Jf(e),er(e),e.liveConsumerNode.length===0)for(let n=0;n<e.producerNode.length;n++)e.producerIndexOfThis[n]=Kf(e.producerNode[n],e,n);return e.liveConsumerIndexOfThis.push(r),e.liveConsumerNode.push(t)-1}function ji(e,t){if(Jf(e),er(e),e.liveConsumerNode.length===1)for(let n=0;n<e.producerNode.length;n++)ji(e.producerNode[n],e.producerIndexOfThis[n]);let r=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[r],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[r],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let n=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];er(o),o.producerIndexOfThis[n]=t}}function Xr(e){var t,r;return e.consumerIsAlwaysLive||((r=(t=e==null?void 0:e.liveConsumerNode)==null?void 0:t.length)!=null?r:0)>0}function er(e){var t,r,n;(t=e.producerNode)!=null||(e.producerNode=[]),(r=e.producerIndexOfThis)!=null||(e.producerIndexOfThis=[]),(n=e.producerLastReadVersion)!=null||(e.producerLastReadVersion=[])}function Jf(e){var t,r;(t=e.liveConsumerNode)!=null||(e.liveConsumerNode=[]),(r=e.liveConsumerIndexOfThis)!=null||(e.liveConsumerIndexOfThis=[])}function PD(){throw new Error}var Xf=PD;function eh(){Xf()}function th(e){Xf=e}var lu=null;function nh(e){let t=Object.create(pu);t.value=e;let r=()=>(fu(t),t.value);return r[qt]=t,r}function $i(e,t){Gf()||eh(),e.equal(e.value,t)||(e.value=t,FD(e))}function rh(e,t){Gf()||eh(),$i(e,t(e.value))}var pu=te(w({},du),{equal:_D,value:void 0});function FD(e){e.version++,ND(),Wf(e),lu==null||lu()}var tr={schedule(e){let t=requestAnimationFrame,r=cancelAnimationFrame,{delegate:n}=tr;n&&(t=n.requestAnimationFrame,r=n.cancelAnimationFrame);let o=t(i=>{r=void 0,e(i)});return new J(()=>r==null?void 0:r(o))},requestAnimationFrame(...e){let{delegate:t}=tr;return((t==null?void 0:t.requestAnimationFrame)||requestAnimationFrame)(...e)},cancelAnimationFrame(...e){let{delegate:t}=tr;return((t==null?void 0:t.cancelAnimationFrame)||cancelAnimationFrame)(...e)},delegate:void 0};var Vi=class extends qn{constructor(t,r){super(t,r),this.scheduler=t,this.work=r}requestAsyncId(t,r,n=0){return n!==null&&n>0?super.requestAsyncId(t,r,n):(t.actions.push(this),t._scheduled||(t._scheduled=tr.requestAnimationFrame(()=>t.flush(void 0))))}recycleAsyncId(t,r,n=0){var o;if(n!=null?n>0:this.delay>0)return super.recycleAsyncId(t,r,n);let{actions:i}=t;r!=null&&r===t._scheduled&&((o=i[i.length-1])===null||o===void 0?void 0:o.id)!==r&&(tr.cancelAnimationFrame(r),t._scheduled=void 0)}};var Ui=class extends Gn{flush(t){this._active=!0;let r;t?r=t.id:(r=this._scheduled,this._scheduled=void 0);let{actions:n}=this,o;t=t||n.shift();do if(o=t.execute(t.state,t.delay))break;while((t=n[0])&&t.id===r&&n.shift());if(this._active=!1,o){for(;(t=n[0])&&t.id===r&&n.shift();)t.unsubscribe();throw o}}};var kD=new Ui(Vi);function gu(e){return!!e&&(e instanceof k||M(e.lift)&&M(e.subscribe))}function LD(e,t){let r=typeof t=="object";return new Promise((n,o)=>{let i=new Ke({next:s=>{n(s),i.unsubscribe()},error:o,complete:()=>{r?n(t.defaultValue):o(new Je)}});e.subscribe(i)})}function Bi(e){return new k(t=>{z(e()).subscribe(t)})}function jD(...e){let t=Si(e),{args:r,keys:n}=xi(e),o=new k(i=>{let{length:s}=r;if(!s){i.complete();return}let a=new Array(s),u=s,c=s;for(let l=0;l<s;l++){let d=!1;z(r[l]).subscribe(N(i,f=>{d||(d=!0,c--),a[l]=f},()=>u--,void 0,()=>{(!u||!d)&&(c||i.next(n?Ri(n,a):a),i.complete())}))}});return t?o.pipe(Zn(t)):o}var $D=["addListener","removeListener"],VD=["addEventListener","removeEventListener"],UD=["on","off"];function mu(e,t,r,n){if(M(r)&&(n=r,r=void 0),n)return mu(e,t,r).pipe(Zn(n));let[o,i]=zD(e)?VD.map(s=>a=>e[s](t,a,r)):BD(e)?$D.map(oh(e,t)):HD(e)?UD.map(oh(e,t)):[];if(!o&&Vn(e))return ne(s=>mu(s,t,r))(z(e));if(!o)throw new TypeError("Invalid event target");return new k(s=>{let a=(...u)=>s.next(1<u.length?u:u[0]);return o(a),()=>i(a)})}function oh(e,t){return r=>n=>e[r](t,n)}function BD(e){return M(e.addListener)&&M(e.removeListener)}function HD(e){return M(e.on)&&M(e.off)}function zD(e){return M(e.addEventListener)&&M(e.removeEventListener)}function qD(...e){let t=pt(e),r=Rf(e,1/0),n=e;return n.length?n.length===1?z(n[0]):gt(r)(Q(n,t)):Ee}var Qh="https://g.co/ng/security#xss",D=class extends Error{constructor(t,r){super(Ns(t,r)),this.code=t}};function Ns(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}var Kh=Symbol("InputSignalNode#UNSET"),WD=te(w({},pu),{transformFn:void 0,applyValueToInputSignal(e,t){$i(e,t)}});function Jh(e,t){let r=Object.create(WD);r.value=e,r.transformFn=t==null?void 0:t.transform;function n(){if(fu(r),r.value===Kh)throw new D(-950,!1);return r.value}return n[qt]=r,n}function bo(e){return{toString:e}.toString()}var Hi="__parameters__";function GD(e){return function(...r){if(e){let n=e(...r);for(let o in n)this[o]=n[o]}}}function Xh(e,t,r){return bo(()=>{let n=GD(t);function o(...i){if(this instanceof o)return n.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(u,c,l){let d=u.hasOwnProperty(Hi)?u[Hi]:Object.defineProperty(u,Hi,{value:[]})[Hi];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),u}}return r&&(o.prototype=Object.create(r.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var _e=globalThis;function Z(e){for(let t in e)if(e[t]===Z)return t;throw Error("Could not find renamed property on target object.")}function ZD(e,t){for(let r in t)t.hasOwnProperty(r)&&!e.hasOwnProperty(r)&&(e[r]=t[r])}function Me(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(Me).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r===-1?t:t.substring(0,r)}function Pu(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var YD=Z({__forward_ref__:Z});function ep(e){return e.__forward_ref__=ep,e.toString=function(){return Me(this())},e}function Ce(e){return tp(e)?e():e}function tp(e){return typeof e=="function"&&e.hasOwnProperty(YD)&&e.__forward_ref__===ep}function E(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Dr(e){return{providers:e.providers||[],imports:e.imports||[]}}function Rs(e){return ih(e,rp)||ih(e,op)}function np(e){return Rs(e)!==null}function ih(e,t){return e.hasOwnProperty(t)?e[t]:null}function QD(e){let t=e&&(e[rp]||e[op]);return t||null}function sh(e){return e&&(e.hasOwnProperty(ah)||e.hasOwnProperty(KD))?e[ah]:null}var rp=Z({\u0275prov:Z}),ah=Z({\u0275inj:Z}),op=Z({ngInjectableDef:Z}),KD=Z({ngInjectorDef:Z}),C=class{constructor(t,r){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof r=="number"?this.__NG_ELEMENT_ID__=r:r!==void 0&&(this.\u0275prov=E({token:this,providedIn:r.providedIn||"root",factory:r.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function ip(e){return e&&!!e.\u0275providers}var JD=Z({\u0275cmp:Z}),XD=Z({\u0275dir:Z}),ew=Z({\u0275pipe:Z}),tw=Z({\u0275mod:Z}),rs=Z({\u0275fac:Z}),oo=Z({__NG_ELEMENT_ID__:Z}),uh=Z({__NG_ENV_ID__:Z});function oe(e){return typeof e=="string"?e:e==null?"":String(e)}function nw(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():oe(e)}function rw(e,t){let r=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new D(-200,e)}function zc(e,t){throw new D(-201,!1)}var L=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(L||{}),Fu;function sp(){return Fu}function Ae(e){let t=Fu;return Fu=e,t}function ap(e,t,r){let n=Rs(e);if(n&&n.providedIn=="root")return n.value===void 0?n.value=n.factory():n.value;if(r&L.Optional)return null;if(t!==void 0)return t;zc(e,"Injector")}var ow={},uo=ow,ku="__NG_DI_FLAG__",os="ngTempTokenPath",iw="ngTokenPath",sw=/\n/gm,aw="\u0275",ch="__source",ur;function uw(){return ur}function Gt(e){let t=ur;return ur=e,t}function cw(e,t=L.Default){if(ur===void 0)throw new D(-203,!1);return ur===null?ap(e,void 0,t):ur.get(e,t&L.Optional?null:void 0,t)}function I(e,t=L.Default){return(sp()||cw)(Ce(e),t)}function g(e,t=L.Default){return I(e,Os(t))}function Os(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Lu(e){let t=[];for(let r=0;r<e.length;r++){let n=Ce(e[r]);if(Array.isArray(n)){if(n.length===0)throw new D(900,!1);let o,i=L.Default;for(let s=0;s<n.length;s++){let a=n[s],u=lw(a);typeof u=="number"?u===-1?o=a.token:i|=u:o=a}t.push(I(o,i))}else t.push(I(n))}return t}function up(e,t){return e[ku]=t,e.prototype[ku]=t,e}function lw(e){return e[ku]}function dw(e,t,r,n){let o=e[os];throw t[ch]&&o.unshift(t[ch]),e.message=fw(`
`+e.message,o,r,n),e[iw]=o,e[os]=null,e}function fw(e,t,r,n=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==aw?e.slice(2):e;let o=Me(t);if(Array.isArray(t))o=t.map(Me).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Me(a)))}o=`{${i.join(", ")}}`}return`${r}${n?"("+n+")":""}[${o}]: ${e.replace(sw,`
  `)}`}var Mo=up(Xh("Optional"),8);var Ps=up(Xh("SkipSelf"),4);function En(e,t){let r=e.hasOwnProperty(rs);return r?e[rs]:null}function hw(e,t,r){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++){let o=e[n],i=t[n];if(r&&(o=r(o),i=r(i)),i!==o)return!1}return!0}function pw(e){return e.flat(Number.POSITIVE_INFINITY)}function qc(e,t){e.forEach(r=>Array.isArray(r)?qc(r,t):t(r))}function cp(e,t,r){t>=e.length?e.push(r):e.splice(t,0,r)}function is(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function gw(e,t){let r=[];for(let n=0;n<e;n++)r.push(t);return r}function mw(e,t,r,n){let o=e.length;if(o==t)e.push(r,n);else if(o===1)e.push(n,e[0]),e[0]=r;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=r,e[t+1]=n}}function Wc(e,t,r){let n=To(e,t);return n>=0?e[n|1]=r:(n=~n,mw(e,n,t,r)),n}function vu(e,t){let r=To(e,t);if(r>=0)return e[r|1]}function To(e,t){return vw(e,t,1)}function vw(e,t,r){let n=0,o=e.length>>r;for(;o!==n;){let i=n+(o-n>>1),s=e[i<<r];if(t===s)return i<<r;s>t?o=i:n=i+1}return~(o<<r)}var lr={},be=[],In=new C(""),lp=new C("",-1),dp=new C(""),ss=class{get(t,r=uo){if(r===uo){let n=new Error(`NullInjectorError: No provider for ${Me(t)}!`);throw n.name="NullInjectorError",n}return r}},fp=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(fp||{}),ot=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(ot||{}),ze=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(ze||{});function yw(e,t,r){let n=e.length;for(;;){let o=e.indexOf(t,r);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===n||e.charCodeAt(o+i)<=32)return o}r=o+1}}function ju(e,t,r){let n=0;for(;n<r.length;){let o=r[n];if(typeof o=="number"){if(o!==0)break;n++;let i=r[n++],s=r[n++],a=r[n++];e.setAttribute(t,s,a,i)}else{let i=o,s=r[++n];Dw(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),n++}}return n}function hp(e){return e===3||e===4||e===6}function Dw(e){return e.charCodeAt(0)===64}function co(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let r=-1;for(let n=0;n<t.length;n++){let o=t[n];typeof o=="number"?r=o:r===0||(r===-1||r===2?lh(e,r,o,null,t[++n]):lh(e,r,o,null,null))}}return e}function lh(e,t,r,n,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===r){if(n===null){o!==null&&(e[i+1]=o);return}else if(n===e[i+1]){e[i+2]=o;return}}i++,n!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,r),n!==null&&e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}var pp="ng-template";function ww(e,t,r,n){let o=0;if(n){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&yw(t[o+1].toLowerCase(),r,0)!==-1)return!0}else if(Gc(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===r)return!0}return!1}function Gc(e){return e.type===4&&e.value!==pp}function Ew(e,t,r){let n=e.type===4&&!r?pp:e.value;return t===n}function Iw(e,t,r){let n=4,o=e.attrs,i=o!==null?Mw(o):0,s=!1;for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="number"){if(!s&&!et(n)&&!et(u))return!1;if(s&&et(u))continue;s=!1,n=u|n&1;continue}if(!s)if(n&4){if(n=2|n&1,u!==""&&!Ew(e,u,r)||u===""&&t.length===1){if(et(n))return!1;s=!0}}else if(n&8){if(o===null||!ww(e,o,u,r)){if(et(n))return!1;s=!0}}else{let c=t[++a],l=Cw(u,o,Gc(e),r);if(l===-1){if(et(n))return!1;s=!0;continue}if(c!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),n&2&&c!==d){if(et(n))return!1;s=!0}}}}return et(n)||s}function et(e){return(e&1)===0}function Cw(e,t,r,n){if(t===null)return-1;let o=0;if(n||!r){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Tw(t,e)}function gp(e,t,r=!1){for(let n=0;n<t.length;n++)if(Iw(e,t[n],r))return!0;return!1}function bw(e){let t=e.attrs;if(t!=null){let r=t.indexOf(5);if(!(r&1))return t[r+1]}return null}function Mw(e){for(let t=0;t<e.length;t++){let r=e[t];if(hp(r))return t}return e.length}function Tw(e,t){let r=e.indexOf(4);if(r>-1)for(r++;r<e.length;){let n=e[r];if(typeof n=="number")return-1;if(n===t)return r;r++}return-1}function Sw(e,t){e:for(let r=0;r<t.length;r++){let n=t[r];if(e.length===n.length){for(let o=0;o<e.length;o++)if(e[o]!==n[o])continue e;return!0}}return!1}function dh(e,t){return e?":not("+t.trim()+")":t}function xw(e){let t=e[0],r=1,n=2,o="",i=!1;for(;r<e.length;){let s=e[r];if(typeof s=="string")if(n&2){let a=e[++r];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else n&8?o+="."+s:n&4&&(o+=" "+s);else o!==""&&!et(s)&&(t+=dh(i,o),o=""),n=s,i=i||!et(n);r++}return o!==""&&(t+=dh(i,o)),t}function Aw(e){return e.map(xw).join(",")}function _w(e){let t=[],r=[],n=1,o=2;for(;n<e.length;){let i=e[n];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++n]):o===8&&r.push(i);else{if(!et(o))break;o=i}n++}return{attrs:t,classes:r}}function mp(e){return bo(()=>{var o;let t=Ep(e),r=te(w({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===fp.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:(o=e.signals)!=null?o:!1,data:e.data||{},encapsulation:e.encapsulation||ot.Emulated,styles:e.styles||be,_:null,schemas:e.schemas||null,tView:null,id:""});Ip(r);let n=e.dependencies;return r.directiveDefs=hh(n,!1),r.pipeDefs=hh(n,!0),r.id=Ow(r),r})}function Nw(e){return Dt(e)||vp(e)}function Rw(e){return e!==null}function wr(e){return bo(()=>({type:e.type,bootstrap:e.bootstrap||be,declarations:e.declarations||be,imports:e.imports||be,exports:e.exports||be,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function fh(e,t){var n;if(e==null)return lr;let r={};for(let o in e)if(e.hasOwnProperty(o)){let i=e[o],s,a,u=ze.None;Array.isArray(i)?(u=i[0],s=i[1],a=(n=i[2])!=null?n:s):(s=i,a=i),t?(r[s]=u!==ze.None?[o,u]:o,t[s]=a):r[s]=o}return r}function Ot(e){return bo(()=>{let t=Ep(e);return Ip(t),t})}function Nn(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Dt(e){return e[JD]||null}function vp(e){return e[XD]||null}function yp(e){return e[ew]||null}function Dp(e){let t=Dt(e)||vp(e)||yp(e);return t!==null?t.standalone:!1}function wp(e,t){let r=e[tw]||null;if(!r&&t===!0)throw new Error(`Type ${Me(e)} does not have '\u0275mod' property.`);return r}function Ep(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||lr,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||be,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:fh(e.inputs,t),outputs:fh(e.outputs),debugInfo:null}}function Ip(e){var t;(t=e.features)==null||t.forEach(r=>r(e))}function hh(e,t){if(!e)return null;let r=t?yp:Nw;return()=>(typeof e=="function"?e():e).map(n=>r(n)).filter(Rw)}function Ow(e){let t=0,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let o of r)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=**********,"c"+t}function Jt(e){return{\u0275providers:e}}function Pw(...e){return{\u0275providers:Cp(!0,e),\u0275fromNgModule:!0}}function Cp(e,...t){let r=[],n=new Set,o,i=s=>{r.push(s)};return qc(t,s=>{let a=s;$u(a,i,[],n)&&(o||(o=[]),o.push(a))}),o!==void 0&&bp(o,i),r}function bp(e,t){for(let r=0;r<e.length;r++){let{ngModule:n,providers:o}=e[r];Zc(o,i=>{t(i,n)})}}function $u(e,t,r,n){if(e=Ce(e),!e)return!1;let o=null,i=sh(e),s=!i&&Dt(e);if(!i&&!s){let u=e.ngModule;if(i=sh(u),i)o=u;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=n.has(o);if(s){if(a)return!1;if(n.add(o),s.dependencies){let u=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of u)$u(c,t,r,n)}}else if(i){if(i.imports!=null&&!a){n.add(o);let c;try{qc(i.imports,l=>{$u(l,t,r,n)&&(c||(c=[]),c.push(l))})}finally{}c!==void 0&&bp(c,t)}if(!a){let c=En(o)||(()=>new o);t({provide:o,useFactory:c,deps:be},o),t({provide:dp,useValue:o,multi:!0},o),t({provide:In,useValue:()=>I(o),multi:!0},o)}let u=i.providers;if(u!=null&&!a){let c=e;Zc(u,l=>{t(l,c)})}}else return!1;return o!==e&&e.providers!==void 0}function Zc(e,t){for(let r of e)ip(r)&&(r=r.\u0275providers),Array.isArray(r)?Zc(r,t):t(r)}var Fw=Z({provide:String,useValue:Z});function Mp(e){return e!==null&&typeof e=="object"&&Fw in e}function kw(e){return!!(e&&e.useExisting)}function Lw(e){return!!(e&&e.useFactory)}function dr(e){return typeof e=="function"}function jw(e){return!!e.useClass}var Fs=new C(""),Ki={},$w={},yu;function ks(){return yu===void 0&&(yu=new ss),yu}var De=class{},lo=class extends De{get destroyed(){return this._destroyed}constructor(t,r,n,o){super(),this.parent=r,this.source=n,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,Uu(t,s=>this.processProvider(s)),this.records.set(lp,rr(void 0,this)),o.has("environment")&&this.records.set(De,rr(void 0,this));let i=this.records.get(Fs);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(dp,be,L.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=j(null);try{for(let n of this._ngOnDestroyHooks)n.ngOnDestroy();let r=this._onDestroyHooks;this._onDestroyHooks=[];for(let n of r)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),j(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let r=Gt(this),n=Ae(void 0),o;try{return t()}finally{Gt(r),Ae(n)}}get(t,r=uo,n=L.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(uh))return t[uh](this);n=Os(n);let o,i=Gt(this),s=Ae(void 0);try{if(!(n&L.SkipSelf)){let u=this.records.get(t);if(u===void 0){let c=zw(t)&&Rs(t);c&&this.injectableDefInScope(c)?u=rr(Vu(t),Ki):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u)}let a=n&L.Self?ks():this.parent;return r=n&L.Optional&&r===uo?null:r,a.get(t,r)}catch(a){if(a.name==="NullInjectorError"){if((a[os]=a[os]||[]).unshift(Me(t)),i)throw a;return dw(a,t,"R3InjectorError",this.source)}else throw a}finally{Ae(s),Gt(i)}}resolveInjectorInitializers(){let t=j(null),r=Gt(this),n=Ae(void 0),o;try{let i=this.get(In,be,L.Self);for(let s of i)s()}finally{Gt(r),Ae(n),j(t)}}toString(){let t=[],r=this.records;for(let n of r.keys())t.push(Me(n));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new D(205,!1)}processProvider(t){t=Ce(t);let r=dr(t)?t:Ce(t&&t.provide),n=Uw(t);if(!dr(t)&&t.multi===!0){let o=this.records.get(r);o||(o=rr(void 0,Ki,!0),o.factory=()=>Lu(o.multi),this.records.set(r,o)),r=t,o.multi.push(t)}this.records.set(r,n)}hydrate(t,r){let n=j(null);try{return r.value===Ki&&(r.value=$w,r.value=r.factory()),typeof r.value=="object"&&r.value&&Hw(r.value)&&this._ngOnDestroyHooks.add(r.value),r.value}finally{j(n)}}injectableDefInScope(t){if(!t.providedIn)return!1;let r=Ce(t.providedIn);return typeof r=="string"?r==="any"||this.scopes.has(r):this.injectorDefTypes.has(r)}removeOnDestroy(t){let r=this._onDestroyHooks.indexOf(t);r!==-1&&this._onDestroyHooks.splice(r,1)}};function Vu(e){let t=Rs(e),r=t!==null?t.factory:En(e);if(r!==null)return r;if(e instanceof C)throw new D(204,!1);if(e instanceof Function)return Vw(e);throw new D(204,!1)}function Vw(e){if(e.length>0)throw new D(204,!1);let r=QD(e);return r!==null?()=>r.factory(e):()=>new e}function Uw(e){if(Mp(e))return rr(void 0,e.useValue);{let t=Tp(e);return rr(t,Ki)}}function Tp(e,t,r){let n;if(dr(e)){let o=Ce(e);return En(o)||Vu(o)}else if(Mp(e))n=()=>Ce(e.useValue);else if(Lw(e))n=()=>e.useFactory(...Lu(e.deps||[]));else if(kw(e))n=()=>I(Ce(e.useExisting));else{let o=Ce(e&&(e.useClass||e.provide));if(Bw(e))n=()=>new o(...Lu(e.deps));else return En(o)||Vu(o)}return n}function rr(e,t,r=!1){return{factory:e,value:t,multi:r?[]:void 0}}function Bw(e){return!!e.deps}function Hw(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function zw(e){return typeof e=="function"||typeof e=="object"&&e instanceof C}function Uu(e,t){for(let r of e)Array.isArray(r)?Uu(r,t):r&&ip(r)?Uu(r.\u0275providers,t):t(r)}function at(e,t){e instanceof lo&&e.assertNotDestroyed();let r,n=Gt(e),o=Ae(void 0);try{return t()}finally{Gt(n),Ae(o)}}function Sp(){return sp()!==void 0||uw()!=null}function qw(e){if(!Sp())throw new D(-203,!1)}function Ww(e){let t=_e.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function Gw(e){return typeof e=="function"}var ie=0,x=1,S=2,de=3,nt=4,Le=5,qe=6,fo=7,Fe=8,fr=9,it=10,V=11,ho=12,ph=13,Er=14,Ne=15,So=16,or=17,rt=18,Ls=19,xp=20,Zt=21,Du=22,Cn=23,G=25,Yc=1,po=6,Nt=7,as=8,hr=9,ye=10,Qc=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(Qc||{});function _t(e){return Array.isArray(e)&&typeof e[Yc]=="object"}function je(e){return Array.isArray(e)&&e[Yc]===!0}function Kc(e){return(e.flags&4)!==0}function Ir(e){return e.componentOffset>-1}function js(e){return(e.flags&1)===1}function Yt(e){return!!e.template}function Jc(e){return(e[S]&512)!==0}function Zw(e){return(e.type&16)===16}function Yw(e){return(e[S]&32)===32}var Bu=class{constructor(t,r,n){this.previousValue=t,this.currentValue=r,this.firstChange=n}isFirstChange(){return this.firstChange}};function Ap(e,t,r,n){t!==null?t.applyValueToInputSignal(t,n):e[r]=n}function Cr(){return _p}function _p(e){return e.type.prototype.ngOnChanges&&(e.setInput=Kw),Qw}Cr.ngInherit=!0;function Qw(){let e=Rp(this),t=e==null?void 0:e.current;if(t){let r=e.previous;if(r===lr)e.previous=t;else for(let n in t)r[n]=t[n];e.current=null,this.ngOnChanges(t)}}function Kw(e,t,r,n,o){let i=this.declaredInputs[n],s=Rp(e)||Jw(e,{previous:lr,current:null}),a=s.current||(s.current={}),u=s.previous,c=u[i];a[i]=new Bu(c&&c.currentValue,r,u===lr),Ap(e,t,o,r)}var Np="__ngSimpleChanges__";function Rp(e){return e[Np]||null}function Jw(e,t){return e[Np]=t}var gh=null;var mt=function(e,t,r){gh!=null&&gh(e,t,r)},Op="svg",Xw="math",eE=!1;function tE(){return eE}function le(e){for(;Array.isArray(e);)e=e[ie];return e}function Pp(e){for(;Array.isArray(e);){if(typeof e[Yc]=="object")return e;e=e[ie]}return null}function Fp(e,t){return le(t[e])}function $e(e,t){return le(t[e.index])}function Xc(e,t){return e.data[t]}function $s(e,t){return e[t]}function Xt(e,t){let r=t[e];return _t(r)?r:r[ie]}function nE(e){return(e[S]&4)===4}function el(e){return(e[S]&128)===128}function rE(e){return je(e[de])}function pr(e,t){return t==null?null:e[t]}function kp(e){e[or]=0}function oE(e){e[S]&1024||(e[S]|=1024,el(e)&&go(e))}function iE(e,t){for(;e>0;)t=t[Er],e--;return t}function tl(e){var t;return!!(e[S]&9216||(t=e[Cn])!=null&&t.dirty)}function Hu(e){var t,r;(t=e[it].changeDetectionScheduler)==null||t.notify(1),tl(e)?go(e):e[S]&64&&(tE()?(e[S]|=1024,go(e)):(r=e[it].changeDetectionScheduler)==null||r.notify())}function go(e){var r;(r=e[it].changeDetectionScheduler)==null||r.notify();let t=mo(e);for(;t!==null&&!(t[S]&8192||(t[S]|=8192,!el(t)));)t=mo(t)}function Lp(e,t){if((e[S]&256)===256)throw new D(911,!1);e[Zt]===null&&(e[Zt]=[]),e[Zt].push(t)}function sE(e,t){if(e[Zt]===null)return;let r=e[Zt].indexOf(t);r!==-1&&e[Zt].splice(r,1)}function mo(e){let t=e[de];return je(t)?t[de]:t}var _={lFrame:Hp(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function aE(){return _.lFrame.elementDepthCount}function uE(){_.lFrame.elementDepthCount++}function cE(){_.lFrame.elementDepthCount--}function jp(){return _.bindingsEnabled}function br(){return _.skipHydrationRootTNode!==null}function lE(e){return _.skipHydrationRootTNode===e}function dE(e){_.skipHydrationRootTNode=e}function fE(){_.skipHydrationRootTNode=null}function T(){return _.lFrame.lView}function X(){return _.lFrame.tView}function p1(e){return _.lFrame.contextLView=e,e[Fe]}function g1(e){return _.lFrame.contextLView=null,e}function we(){let e=$p();for(;e!==null&&e.type===64;)e=e.parent;return e}function $p(){return _.lFrame.currentTNode}function hE(){let e=_.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Rn(e,t){let r=_.lFrame;r.currentTNode=e,r.isParent=t}function nl(){return _.lFrame.isParent}function rl(){_.lFrame.isParent=!1}function pE(){return _.lFrame.contextLView}function Pt(){let e=_.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Vs(){return _.lFrame.bindingIndex}function gE(e){return _.lFrame.bindingIndex=e}function Mr(){return _.lFrame.bindingIndex++}function Tr(e){let t=_.lFrame,r=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,r}function mE(){return _.lFrame.inI18n}function vE(e,t){let r=_.lFrame;r.bindingIndex=r.bindingRootIndex=e,zu(t)}function yE(){return _.lFrame.currentDirectiveIndex}function zu(e){_.lFrame.currentDirectiveIndex=e}function DE(e){let t=_.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Vp(){return _.lFrame.currentQueryIndex}function ol(e){_.lFrame.currentQueryIndex=e}function wE(e){let t=e[x];return t.type===2?t.declTNode:t.type===1?e[Le]:null}function Up(e,t,r){if(r&L.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(r&L.Host);)if(o=wE(i),o===null||(i=i[Er],o.type&10))break;if(o===null)return!1;t=o,e=i}let n=_.lFrame=Bp();return n.currentTNode=t,n.lView=e,!0}function il(e){let t=Bp(),r=e[x];_.lFrame=t,t.currentTNode=r.firstChild,t.lView=e,t.tView=r,t.contextLView=e,t.bindingIndex=r.bindingStartIndex,t.inI18n=!1}function Bp(){let e=_.lFrame,t=e===null?null:e.child;return t===null?Hp(e):t}function Hp(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function zp(){let e=_.lFrame;return _.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var qp=zp;function sl(){let e=zp();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function EE(e){return(_.lFrame.contextLView=iE(e,_.lFrame.contextLView))[Fe]}function ut(){return _.lFrame.selectedIndex}function bn(e){_.lFrame.selectedIndex=e}function xo(){let e=_.lFrame;return Xc(e.tView,e.selectedIndex)}function m1(){_.lFrame.currentNamespace=Op}function v1(){IE()}function IE(){_.lFrame.currentNamespace=null}function Wp(){return _.lFrame.currentNamespace}var Gp=!0;function Us(){return Gp}function Et(e){Gp=e}function CE(e,t,r){var s,a,u,c,l;let{ngOnChanges:n,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(n){let d=_p(t);((s=r.preOrderHooks)!=null?s:r.preOrderHooks=[]).push(e,d),((a=r.preOrderCheckHooks)!=null?a:r.preOrderCheckHooks=[]).push(e,d)}o&&((u=r.preOrderHooks)!=null?u:r.preOrderHooks=[]).push(0-e,o),i&&(((c=r.preOrderHooks)!=null?c:r.preOrderHooks=[]).push(e,i),((l=r.preOrderCheckHooks)!=null?l:r.preOrderCheckHooks=[]).push(e,i))}function Bs(e,t){var r,n,o,i,s,a,u;for(let c=t.directiveStart,l=t.directiveEnd;c<l;c++){let f=e.data[c].type.prototype,{ngAfterContentInit:h,ngAfterContentChecked:p,ngAfterViewInit:y,ngAfterViewChecked:m,ngOnDestroy:v}=f;h&&((r=e.contentHooks)!=null?r:e.contentHooks=[]).push(-c,h),p&&(((n=e.contentHooks)!=null?n:e.contentHooks=[]).push(c,p),((o=e.contentCheckHooks)!=null?o:e.contentCheckHooks=[]).push(c,p)),y&&((i=e.viewHooks)!=null?i:e.viewHooks=[]).push(-c,y),m&&(((s=e.viewHooks)!=null?s:e.viewHooks=[]).push(c,m),((a=e.viewCheckHooks)!=null?a:e.viewCheckHooks=[]).push(c,m)),v!=null&&((u=e.destroyHooks)!=null?u:e.destroyHooks=[]).push(c,v)}}function Ji(e,t,r){Zp(e,t,3,r)}function Xi(e,t,r,n){(e[S]&3)===r&&Zp(e,t,r,n)}function wu(e,t){let r=e[S];(r&3)===t&&(r&=16383,r+=1,e[S]=r)}function Zp(e,t,r,n){let o=n!==void 0?e[or]&65535:0,i=n!=null?n:-1,s=t.length-1,a=0;for(let u=o;u<s;u++)if(typeof t[u+1]=="number"){if(a=t[u],n!=null&&a>=n)break}else t[u]<0&&(e[or]+=65536),(a<i||i==-1)&&(bE(e,r,t,u),e[or]=(e[or]&**********)+u+2),u++}function mh(e,t){mt(4,e,t);let r=j(null);try{t.call(e)}finally{j(r),mt(5,e,t)}}function bE(e,t,r,n){let o=r[n]<0,i=r[n+1],s=o?-r[n]:r[n],a=e[s];o?e[S]>>14<e[or]>>16&&(e[S]&3)===t&&(e[S]+=16384,mh(a,i)):mh(a,i)}var cr=-1,Mn=class{constructor(t,r,n){this.factory=t,this.resolving=!1,this.canSeeViewProviders=r,this.injectImpl=n}};function ME(e){return e instanceof Mn}function TE(e){return e!=null&&typeof e=="object"&&(e.insertBeforeIndex===null||typeof e.insertBeforeIndex=="number"||Array.isArray(e.insertBeforeIndex))}function SE(e){return(e.flags&8)!==0}function xE(e){return(e.flags&16)!==0}function Yp(e){return e!==cr}function us(e){return e&32767}function AE(e){return e>>16}function cs(e,t){let r=AE(e),n=t;for(;r>0;)n=n[Er],r--;return n}var qu=!0;function ls(e){let t=qu;return qu=e,t}var _E=256,Qp=_E-1,Kp=5,NE=0,vt={};function RE(e,t,r){let n;typeof r=="string"?n=r.charCodeAt(0)||0:r.hasOwnProperty(oo)&&(n=r[oo]),n==null&&(n=r[oo]=NE++);let o=n&Qp,i=1<<o;t.data[e+(o>>Kp)]|=i}function ds(e,t){let r=Jp(e,t);if(r!==-1)return r;let n=t[x];n.firstCreatePass&&(e.injectorIndex=t.length,Eu(n.data,e),Eu(t,null),Eu(n.blueprint,null));let o=al(e,t),i=e.injectorIndex;if(Yp(o)){let s=us(o),a=cs(o,t),u=a[x].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|u[s+c]}return t[i+8]=o,i}function Eu(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Jp(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function al(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let r=0,n=null,o=t;for(;o!==null;){if(n=rg(o),n===null)return cr;if(r++,o=o[Er],n.injectorIndex!==-1)return n.injectorIndex|r<<16}return cr}function Wu(e,t,r){RE(e,t,r)}function OE(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let r=e.attrs;if(r){let n=r.length,o=0;for(;o<n;){let i=r[o];if(hp(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<n&&typeof r[o]=="string";)o++;else{if(i===t)return r[o+1];o=o+2}}}return null}function Xp(e,t,r){if(r&L.Optional||e!==void 0)return e;zc(t,"NodeInjector")}function eg(e,t,r,n){if(r&L.Optional&&n===void 0&&(n=null),!(r&(L.Self|L.Host))){let o=e[fr],i=Ae(void 0);try{return o?o.get(t,n,r&L.Optional):ap(t,n,r&L.Optional)}finally{Ae(i)}}return Xp(n,t,r)}function tg(e,t,r,n=L.Default,o){if(e!==null){if(t[S]&2048&&!(n&L.Self)){let s=LE(e,t,r,n,vt);if(s!==vt)return s}let i=ng(e,t,r,n,vt);if(i!==vt)return i}return eg(t,r,n,o)}function ng(e,t,r,n,o){let i=FE(r);if(typeof i=="function"){if(!Up(t,e,n))return n&L.Host?Xp(o,r,n):eg(t,r,n,o);try{let s;if(s=i(n),s==null&&!(n&L.Optional))zc(r);else return s}finally{qp()}}else if(typeof i=="number"){let s=null,a=Jp(e,t),u=cr,c=n&L.Host?t[Ne][Le]:null;for((a===-1||n&L.SkipSelf)&&(u=a===-1?al(e,t):t[a+8],u===cr||!yh(n,!1)?a=-1:(s=t[x],a=us(u),t=cs(u,t)));a!==-1;){let l=t[x];if(vh(i,a,l.data)){let d=PE(a,t,r,s,n,c);if(d!==vt)return d}u=t[a+8],u!==cr&&yh(n,t[x].data[a+8]===c)&&vh(i,a,t)?(s=l,a=us(u),t=cs(u,t)):a=-1}}return o}function PE(e,t,r,n,o,i){let s=t[x],a=s.data[e+8],u=n==null?Ir(a)&&qu:n!=s&&(a.type&3)!==0,c=o&L.Host&&i===a,l=es(a,s,r,u,c);return l!==null?Tn(t,s,l,a):vt}function es(e,t,r,n,o){let i=e.providerIndexes,s=t.data,a=i&1048575,u=e.directiveStart,c=e.directiveEnd,l=i>>20,d=n?a:a+l,f=o?a+l:c;for(let h=d;h<f;h++){let p=s[h];if(h<u&&r===p||h>=u&&p.type===r)return h}if(o){let h=s[u];if(h&&Yt(h)&&h.type===r)return u}return null}function Tn(e,t,r,n){let o=e[r],i=t.data;if(ME(o)){let s=o;s.resolving&&rw(nw(i[r]));let a=ls(s.canSeeViewProviders);s.resolving=!0;let u,c=s.injectImpl?Ae(s.injectImpl):null,l=Up(e,n,L.Default);try{o=e[r]=s.factory(void 0,i,e,n),t.firstCreatePass&&r>=n.directiveStart&&CE(r,i[r],t)}finally{c!==null&&Ae(c),ls(a),s.resolving=!1,qp()}}return o}function FE(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(oo)?e[oo]:void 0;return typeof t=="number"?t>=0?t&Qp:kE:t}function vh(e,t,r){let n=1<<e;return!!(r[t+(e>>Kp)]&n)}function yh(e,t){return!(e&L.Self)&&!(e&L.Host&&t)}var wn=class{constructor(t,r){this._tNode=t,this._lView=r}get(t,r,n){return tg(this._tNode,this._lView,t,Os(n),r)}};function kE(){return new wn(we(),T())}function ul(e){return bo(()=>{let t=e.prototype.constructor,r=t[rs]||Gu(t),n=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==n;){let i=o[rs]||Gu(o);if(i&&i!==r)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Gu(e){return tp(e)?()=>{let t=Gu(Ce(e));return t&&t()}:En(e)}function LE(e,t,r,n,o){let i=e,s=t;for(;i!==null&&s!==null&&s[S]&2048&&!(s[S]&512);){let a=ng(i,s,r,n|L.Self,vt);if(a!==vt)return a;let u=i.parent;if(!u){let c=s[xp];if(c){let l=c.get(r,vt,n);if(l!==vt)return l}u=rg(s),s=s[Er]}i=u}return o}function rg(e){let t=e[x],r=t.type;return r===2?t.declTNode:r===1?e[Le]:null}function cl(e){return OE(we(),e)}function Dh(e,t=null,r=null,n){let o=og(e,t,r,n);return o.resolveInjectorInitializers(),o}function og(e,t=null,r=null,n,o=new Set){let i=[r||be,Pw(e)];return n=n||(typeof e=="object"?void 0:Me(e)),new lo(i,t||ks(),n||null,o)}var ct=(()=>{let t=class t{static create(n,o){var i;if(Array.isArray(n))return Dh({name:""},o,n,"");{let s=(i=n.name)!=null?i:"";return Dh({name:s},n.parent,n.providers,s)}}};t.THROW_IF_NOT_FOUND=uo,t.NULL=new ss,t.\u0275prov=E({token:t,providedIn:"any",factory:()=>I(lp)}),t.__NG_ELEMENT_ID__=-1;let e=t;return e})();var jE="ngOriginalError";function Iu(e){return e[jE]}var st=class{constructor(){this._console=console}handleError(t){let r=this._findOriginalError(t);this._console.error("ERROR",t),r&&this._console.error("ORIGINAL ERROR",r)}_findOriginalError(t){let r=t&&Iu(t);for(;r&&Iu(r);)r=Iu(r);return r||null}},ig=new C("",{providedIn:"root",factory:()=>g(st).handleError.bind(void 0)}),ll=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=$E,t.__NG_ENV_ID__=n=>n;let e=t;return e})(),Zu=class extends ll{constructor(t){super(),this._lView=t}onDestroy(t){return Lp(this._lView,t),()=>sE(this._lView,t)}};function $E(){return new Zu(T())}function wh(e,t){return Jh(e,t)}function VE(e){return Jh(Kh,e)}var y1=(wh.required=VE,wh);function UE(){return Sr(we(),T())}function Sr(e,t){return new lt($e(e,t))}var lt=(()=>{let t=class t{constructor(n){this.nativeElement=n}};t.__NG_ELEMENT_ID__=UE;let e=t;return e})();function BE(e){return e instanceof lt?e.nativeElement:e}var Yu=class extends se{constructor(t=!1){var r;super(),this.destroyRef=void 0,this.__isAsync=t,Sp()&&(this.destroyRef=(r=g(ll,{optional:!0}))!=null?r:void 0)}emit(t){let r=j(null);try{super.next(t)}finally{j(r)}}subscribe(t,r,n){var u,c,l;let o=t,i=r||(()=>null),s=n;if(t&&typeof t=="object"){let d=t;o=(u=d.next)==null?void 0:u.bind(d),i=(c=d.error)==null?void 0:c.bind(d),s=(l=d.complete)==null?void 0:l.bind(d)}this.__isAsync&&(i=Cu(i),o&&(o=Cu(o)),s&&(s=Cu(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof J&&t.add(a),a}};function Cu(e){return t=>{setTimeout(e,void 0,t)}}var ge=Yu;function HE(){return this._results[Symbol.iterator]()}var Qu=class e{get changes(){var t;return(t=this._changes)!=null?t:this._changes=new ge}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let r=e.prototype;r[Symbol.iterator]||(r[Symbol.iterator]=HE)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,r){return this._results.reduce(t,r)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,r){this.dirty=!1;let n=pw(t);(this._changesDetected=!hw(this._results,n,r))&&(this._results=n,this.length=n.length,this.last=n[this.length-1],this.first=n[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(t){this._onDirty=t}setDirty(){var t;this.dirty=!0,(t=this._onDirty)==null||t.call(this)}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}},vo="ngSkipHydration",zE="ngskiphydration";function sg(e){let t=e.mergedAttrs;if(t===null)return!1;for(let r=0;r<t.length;r+=2){let n=t[r];if(typeof n=="number")return!1;if(typeof n=="string"&&n.toLowerCase()===zE)return!0}return!1}function ag(e){return e.hasAttribute(vo)}function fs(e){return(e.flags&128)===128}function hs(e){if(fs(e))return!0;let t=e.parent;for(;t;){if(fs(e)||sg(t))return!0;t=t.parent}return!1}var ug=new Map,qE=0;function WE(){return qE++}function GE(e){ug.set(e[Ls],e)}function ZE(e){ug.delete(e[Ls])}var Eh="__ngContext__";function Qt(e,t){_t(t)?(e[Eh]=t[Ls],GE(t)):e[Eh]=t}function cg(e){return dg(e[ho])}function lg(e){return dg(e[nt])}function dg(e){for(;e!==null&&!je(e);)e=e[nt];return e}var Ku;function fg(e){Ku=e}function Ao(){if(Ku!==void 0)return Ku;if(typeof document<"u")return document;throw new D(210,!1)}var Hs=new C("",{providedIn:"root",factory:()=>YE}),YE="ng",dl=new C(""),We=new C("",{providedIn:"platform",factory:()=>"unknown"});var D1=new C(""),fl=new C("",{providedIn:"root",factory:()=>{var e,t;return((t=(e=Ao().body)==null?void 0:e.querySelector("[ngCspNonce]"))==null?void 0:t.getAttribute("ngCspNonce"))||null}});function QE(){let e=new en;return g(We)==="browser"&&(e.store=KE(Ao(),g(Hs))),e}var en=(()=>{let t=class t{constructor(){this.store={},this.onSerializeCallbacks={}}get(n,o){return this.store[n]!==void 0?this.store[n]:o}set(n,o){this.store[n]=o}remove(n){delete this.store[n]}hasKey(n){return this.store.hasOwnProperty(n)}get isEmpty(){return Object.keys(this.store).length===0}onSerialize(n,o){this.onSerializeCallbacks[n]=o}toJson(){for(let n in this.onSerializeCallbacks)if(this.onSerializeCallbacks.hasOwnProperty(n))try{this.store[n]=this.onSerializeCallbacks[n]()}catch(o){console.warn("Exception in onSerialize callback: ",o)}return JSON.stringify(this.store).replace(/</g,"\\u003C")}};t.\u0275prov=E({token:t,providedIn:"root",factory:QE});let e=t;return e})();function KE(e,t){let r=e.getElementById(t+"-state");if(r!=null&&r.textContent)try{return JSON.parse(r.textContent)}catch(n){console.warn("Exception while restoring TransferState for app "+t,n)}return{}}var hl="h",pl="b",yo=function(e){return e.FirstChild="f",e.NextSibling="n",e}(yo||{}),eo="e",to="t",Dn="c",ir="x",gr="r",Ju="i",no="n",nr="d",JE="__nghData__",gl=JE,io="ngh",XE="nghm",hg=()=>null;function eI(e,t,r=!1){var l;let n=e.getAttribute(io);if(n==null)return null;let[o,i]=n.split("|");if(n=r?i:o,!n)return null;let s=i?`|${i}`:"",a=r?o:s,u={};if(n!==""){let d=t.get(en,null,{optional:!0});d!==null&&(u=d.get(gl,[])[Number(n)])}let c={data:u,firstChild:(l=e.firstChild)!=null?l:null};return r&&(c.firstChild=e,zs(c,0,e.nextSibling)),a?e.setAttribute(io,a):e.removeAttribute(io),c}function tI(){hg=eI}function ml(e,t,r=!1){return hg(e,t,r)}function pg(e){let t=e._lView;return t[x].type===2?null:(Jc(t)&&(t=t[G]),t)}function nI(e){var t;return(t=e.textContent)==null?void 0:t.replace(/\s/gm,"")}function rI(e){let t=Ao(),r=t.createNodeIterator(e,NodeFilter.SHOW_COMMENT,{acceptNode(i){let s=nI(i);return s==="ngetn"||s==="ngtns"?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}}),n,o=[];for(;n=r.nextNode();)o.push(n);for(let i of o)i.textContent==="ngetn"?i.replaceWith(t.createTextNode("")):i.remove()}function zs(e,t,r){var n;(n=e.segmentHeads)!=null||(e.segmentHeads={}),e.segmentHeads[t]=r}function Xu(e,t){var r,n;return(n=(r=e.segmentHeads)==null?void 0:r[t])!=null?n:null}function oI(e,t){var o,i,s;let r=e.data,n=(i=(o=r[eo])==null?void 0:o[t])!=null?i:null;return n===null&&((s=r[Dn])!=null&&s[t])&&(n=vl(e,t)),n}function gg(e,t){var r,n;return(n=(r=e.data[Dn])==null?void 0:r[t])!=null?n:null}function vl(e,t){var o,i;let r=(o=gg(e,t))!=null?o:[],n=0;for(let s of r)n+=s[gr]*((i=s[ir])!=null?i:1);return n}function qs(e,t){var r;if(typeof e.disconnectedNodes>"u"){let n=e.data[nr];e.disconnectedNodes=n?new Set(n):null}return!!((r=e.disconnectedNodes)!=null&&r.has(t))}var zi=new C(""),mg=!1,vg=new C("",{providedIn:"root",factory:()=>mg}),iI=new C(""),qi;function sI(){if(qi===void 0&&(qi=null,_e.trustedTypes))try{qi=_e.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return qi}function Ws(e){var t;return((t=sI())==null?void 0:t.createHTML(e))||e}var Wi;function yg(){if(Wi===void 0&&(Wi=null,_e.trustedTypes))try{Wi=_e.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Wi}function Ih(e){var t;return((t=yg())==null?void 0:t.createHTML(e))||e}function Ch(e){var t;return((t=yg())==null?void 0:t.createScriptURL(e))||e}var Rt=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Qh})`}},ec=class extends Rt{getTypeName(){return"HTML"}},tc=class extends Rt{getTypeName(){return"Style"}},nc=class extends Rt{getTypeName(){return"Script"}},rc=class extends Rt{getTypeName(){return"URL"}},oc=class extends Rt{getTypeName(){return"ResourceURL"}};function Ge(e){return e instanceof Rt?e.changingThisBreaksApplicationSecurity:e}function Ft(e,t){let r=aI(e);if(r!=null&&r!==t){if(r==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${r} (see ${Qh})`)}return r===t}function aI(e){return e instanceof Rt&&e.getTypeName()||null}function Dg(e){return new ec(e)}function wg(e){return new tc(e)}function Eg(e){return new nc(e)}function Ig(e){return new rc(e)}function Cg(e){return new oc(e)}function uI(e){let t=new sc(e);return cI()?new ic(t):t}var ic=class{constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let r=new window.DOMParser().parseFromString(Ws(t),"text/html").body;return r===null?this.inertDocumentHelper.getInertBodyElement(t):(r.removeChild(r.firstChild),r)}catch{return null}}},sc=class{constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let r=this.inertDocument.createElement("template");return r.innerHTML=Ws(t),r}};function cI(){try{return!!new window.DOMParser().parseFromString(Ws(""),"text/html")}catch{return!1}}var lI=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Gs(e){return e=String(e),e.match(lI)?e:"unsafe:"+e}function kt(e){let t={};for(let r of e.split(","))t[r]=!0;return t}function _o(...e){let t={};for(let r of e)for(let n in r)r.hasOwnProperty(n)&&(t[n]=!0);return t}var bg=kt("area,br,col,hr,img,wbr"),Mg=kt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Tg=kt("rp,rt"),dI=_o(Tg,Mg),fI=_o(Mg,kt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),hI=_o(Tg,kt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),bh=_o(bg,fI,hI,dI),Sg=kt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),pI=kt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),gI=kt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),mI=_o(Sg,pI,gI),vI=kt("script,style,template"),ac=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(t){let r=t.firstChild,n=!0,o=[];for(;r;){if(r.nodeType===Node.ELEMENT_NODE?n=this.startElement(r):r.nodeType===Node.TEXT_NODE?this.chars(r.nodeValue):this.sanitizedSomething=!0,n&&r.firstChild){o.push(r),r=wI(r);continue}for(;r;){r.nodeType===Node.ELEMENT_NODE&&this.endElement(r);let i=DI(r);if(i){r=i;break}r=o.pop()}}return this.buf.join("")}startElement(t){let r=Mh(t).toLowerCase();if(!bh.hasOwnProperty(r))return this.sanitizedSomething=!0,!vI.hasOwnProperty(r);this.buf.push("<"),this.buf.push(r);let n=t.attributes;for(let o=0;o<n.length;o++){let i=n.item(o),s=i.name,a=s.toLowerCase();if(!mI.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let u=i.value;Sg[a]&&(u=Gs(u)),this.buf.push(" ",s,'="',Th(u),'"')}return this.buf.push(">"),!0}endElement(t){let r=Mh(t).toLowerCase();bh.hasOwnProperty(r)&&!bg.hasOwnProperty(r)&&(this.buf.push("</"),this.buf.push(r),this.buf.push(">"))}chars(t){this.buf.push(Th(t))}};function yI(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function DI(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw xg(t);return t}function wI(e){let t=e.firstChild;if(t&&yI(e,t))throw xg(t);return t}function Mh(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function xg(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var EI=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,II=/([^\#-~ |!])/g;function Th(e){return e.replace(/&/g,"&amp;").replace(EI,function(t){let r=t.charCodeAt(0),n=t.charCodeAt(1);return"&#"+((r-55296)*1024+(n-56320)+65536)+";"}).replace(II,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var Gi;function yl(e,t){let r=null;try{Gi=Gi||uI(e);let n=t?String(t):"";r=Gi.getInertBodyElement(n);let o=5,i=n;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,n=i,i=r.innerHTML,r=Gi.getInertBodyElement(n)}while(n!==i);let a=new ac().sanitizeChildren(Sh(r)||r);return Ws(a)}finally{if(r){let n=Sh(r)||r;for(;n.firstChild;)n.removeChild(n.firstChild)}}}function Sh(e){return"content"in e&&CI(e)?e.content:null}function CI(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var dt=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(dt||{});function w1(e){let t=Dl();return t?Ih(t.sanitize(dt.HTML,e)||""):Ft(e,"HTML")?Ih(Ge(e)):yl(Ao(),oe(e))}function bI(e){let t=Dl();return t?t.sanitize(dt.URL,e)||"":Ft(e,"URL")?Ge(e):Gs(oe(e))}function MI(e){let t=Dl();if(t)return Ch(t.sanitize(dt.RESOURCE_URL,e)||"");if(Ft(e,"ResourceURL"))return Ch(Ge(e));throw new D(904,!1)}function TI(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?MI:bI}function Ag(e,t,r){return TI(t,r)(e)}function Dl(){let e=T();return e&&e[it].sanitizer}var SI=/^>|^->|<!--|-->|--!>|<!-$/g,xI=/(<|>)/g,AI="\u200B$1\u200B";function _I(e){return e.replace(SI,t=>t.replace(xI,AI))}function E1(e){return e.ownerDocument.defaultView}function I1(e){return e.ownerDocument}function NI(e){return e.ownerDocument.body}function _g(e){return e instanceof Function?e():e}function ro(e){return(e!=null?e:g(ct)).get(We)==="browser"}var wt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(wt||{}),RI;function wl(e,t){return RI(e,t)}function sr(e,t,r,n,o){if(n!=null){let i,s=!1;je(n)?i=n:_t(n)&&(s=!0,n=n[ie]);let a=le(n);e===0&&r!==null?o==null?Og(t,r,a):gs(t,r,a,o||null,!0):e===1&&r!==null?gs(t,r,a,o||null,!0):e===2?Tl(t,a,s):e===3&&t.destroyNode(a),i!=null&&GI(t,e,i,r,o)}}function El(e,t){return e.createText(t)}function OI(e,t,r){e.setValue(t,r)}function Il(e,t){return e.createComment(_I(t))}function Zs(e,t,r){return e.createElement(t,r)}function PI(e,t){Ng(e,t),t[ie]=null,t[Le]=null}function FI(e,t,r,n,o,i){n[ie]=o,n[Le]=t,Qs(e,n,r,1,o,i)}function Ng(e,t){var r;(r=t[it].changeDetectionScheduler)==null||r.notify(1),Qs(e,t,t[V],2,null,null)}function kI(e){let t=e[ho];if(!t)return bu(e[x],e);for(;t;){let r=null;if(_t(t))r=t[ho];else{let n=t[ye];n&&(r=n)}if(!r){for(;t&&!t[nt]&&t!==e;)_t(t)&&bu(t[x],t),t=t[de];t===null&&(t=e),_t(t)&&bu(t[x],t),r=t&&t[nt]}t=r}}function LI(e,t,r,n){let o=ye+n,i=r.length;n>0&&(r[o-1][nt]=t),n<i-ye?(t[nt]=r[o],cp(r,ye+n,t)):(r.push(t),t[nt]=null),t[de]=r;let s=t[So];s!==null&&r!==s&&jI(s,t);let a=t[rt];a!==null&&a.insertView(e),Hu(t),t[S]|=128}function jI(e,t){let r=e[hr],o=t[de][de][Ne];t[Ne]!==o&&(e[S]|=Qc.HasTransplantedViews),r===null?e[hr]=[t]:r.push(t)}function Rg(e,t){let r=e[hr],n=r.indexOf(t);r.splice(n,1)}function ps(e,t){if(e.length<=ye)return;let r=ye+t,n=e[r];if(n){let o=n[So];o!==null&&o!==e&&Rg(o,n),t>0&&(e[r-1][nt]=n[nt]);let i=is(e,ye+t);PI(n[x],n);let s=i[rt];s!==null&&s.detachView(i[x]),n[de]=null,n[nt]=null,n[S]&=-129}return n}function Cl(e,t){if(!(t[S]&256)){let r=t[V];r.destroyNode&&Qs(e,t,r,3,null,null),kI(t)}}function bu(e,t){if(t[S]&256)return;let r=j(null);try{t[S]&=-129,t[S]|=256,t[Cn]&&Qf(t[Cn]),VI(e,t),$I(e,t),t[x].type===1&&t[V].destroy();let n=t[So];if(n!==null&&je(t[de])){n!==t[de]&&Rg(n,t);let o=t[rt];o!==null&&o.detachView(e)}ZE(t)}finally{j(r)}}function $I(e,t){let r=e.cleanup,n=t[fo];if(r!==null)for(let i=0;i<r.length-1;i+=2)if(typeof r[i]=="string"){let s=r[i+3];s>=0?n[s]():n[-s].unsubscribe(),i+=2}else{let s=n[r[i+1]];r[i].call(s)}n!==null&&(t[fo]=null);let o=t[Zt];if(o!==null){t[Zt]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function VI(e,t){let r;if(e!=null&&(r=e.destroyHooks)!=null)for(let n=0;n<r.length;n+=2){let o=t[r[n]];if(!(o instanceof Mn)){let i=r[n+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],u=i[s+1];mt(4,a,u);try{u.call(a)}finally{mt(5,a,u)}}else{mt(4,o,i);try{i.call(o)}finally{mt(5,o,i)}}}}}function bl(e,t,r){return UI(e,t.parent,r)}function UI(e,t,r){let n=t;for(;n!==null&&n.type&40;)t=n,n=t.parent;if(n===null)return r[ie];{let{componentOffset:o}=n;if(o>-1){let{encapsulation:i}=e.data[n.directiveStart+o];if(i===ot.None||i===ot.Emulated)return null}return $e(n,r)}}function gs(e,t,r,n,o){e.insertBefore(t,r,n,o)}function Og(e,t,r){e.appendChild(t,r)}function xh(e,t,r,n,o){n!==null?gs(e,t,r,n,o):Og(e,t,r)}function BI(e,t,r,n){e.removeChild(t,r,n)}function Ml(e,t){return e.parentNode(t)}function HI(e,t){return e.nextSibling(t)}function Pg(e,t,r){return qI(e,t,r)}function zI(e,t,r){return e.type&40?$e(e,r):null}var qI=zI,Ah;function Ys(e,t,r,n){let o=bl(e,n,t),i=t[V],s=n.parent||t[Le],a=Pg(s,n,t);if(o!=null)if(Array.isArray(r))for(let u=0;u<r.length;u++)xh(i,o,r[u],a,!1);else xh(i,o,r,a,!1);Ah!==void 0&&Ah(i,n,t,r,o)}function so(e,t){if(t!==null){let r=t.type;if(r&3)return $e(t,e);if(r&4)return uc(-1,e[t.index]);if(r&8){let n=t.child;if(n!==null)return so(e,n);{let o=e[t.index];return je(o)?uc(-1,o):le(o)}}else{if(r&32)return wl(t,e)()||le(e[t.index]);{let n=Fg(e,t);if(n!==null){if(Array.isArray(n))return n[0];let o=mo(e[Ne]);return so(o,n)}else return so(e,t.next)}}}return null}function Fg(e,t){if(t!==null){let n=e[Ne][Le],o=t.projection;return n.projection[o]}return null}function uc(e,t){let r=ye+e+1;if(r<t.length){let n=t[r],o=n[x].firstChild;if(o!==null)return so(n,o)}return t[Nt]}function Tl(e,t,r){let n=Ml(e,t);n&&BI(e,n,t,r)}function kg(e){e.textContent=""}function Sl(e,t,r,n,o,i,s){for(;r!=null;){let a=n[r.index],u=r.type;if(s&&t===0&&(a&&Qt(le(a),n),r.flags|=2),(r.flags&32)!==32)if(u&8)Sl(e,t,r.child,n,o,i,!1),sr(t,e,o,a,i);else if(u&32){let c=wl(r,n),l;for(;l=c();)sr(t,e,o,l,i);sr(t,e,o,a,i)}else u&16?Lg(e,t,n,r,o,i):sr(t,e,o,a,i);r=s?r.projectionNext:r.next}}function Qs(e,t,r,n,o,i){Sl(r,n,e.firstChild,t,o,i,!1)}function WI(e,t,r){let n=t[V],o=bl(e,r,t),i=r.parent||t[Le],s=Pg(i,r,t);Lg(n,0,t,r,o,s)}function Lg(e,t,r,n,o,i){let s=r[Ne],u=s[Le].projection[n.projection];if(Array.isArray(u))for(let c=0;c<u.length;c++){let l=u[c];sr(t,e,o,l,i)}else{let c=u,l=s[de];fs(n)&&(c.flags|=128),Sl(e,t,c,l,o,i,!0)}}function GI(e,t,r,n,o){let i=r[Nt],s=le(r);i!==s&&sr(t,e,n,i,o);for(let a=ye;a<r.length;a++){let u=r[a];Qs(u[x],u,e,t,n,i)}}function ZI(e,t,r,n,o){if(t)o?e.addClass(r,n):e.removeClass(r,n);else{let i=n.indexOf("-")===-1?void 0:wt.DashCase;o==null?e.removeStyle(r,n,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=wt.Important),e.setStyle(r,n,o,i))}}function YI(e,t,r){e.setAttribute(t,"style",r)}function jg(e,t,r){r===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",r)}function $g(e,t,r){let{mergedAttrs:n,classes:o,styles:i}=r;n!==null&&ju(e,t,n),o!==null&&jg(e,t,o),i!==null&&YI(e,t,i)}var fe={};function C1(e=1){Vg(X(),T(),ut()+e,!1)}function Vg(e,t,r,n){if(!n)if((t[S]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Ji(t,i,r)}else{let i=e.preOrderHooks;i!==null&&Xi(t,i,0,r)}bn(r)}function $(e,t=L.Default){let r=T();if(r===null)return I(e,t);let n=we();return tg(n,r,Ce(e),t)}function Ug(){let e="invalid";throw new Error(e)}function Bg(e,t,r,n,o,i){let s=j(null);try{let a=null;o&ze.SignalBased&&(a=t[n][qt]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&ze.HasDecoratorInputTransform&&(i=e.inputTransforms[n].call(t,i)),e.setInput!==null?e.setInput(t,a,i,r,n):Ap(t,a,n,i)}finally{j(s)}}function QI(e,t){let r=e.hostBindingOpCodes;if(r!==null)try{for(let n=0;n<r.length;n++){let o=r[n];if(o<0)bn(~o);else{let i=o,s=r[++n],a=r[++n];vE(s,i);let u=t[i];a(2,u)}}}finally{bn(-1)}}function Ks(e,t,r,n,o,i,s,a,u,c,l){let d=t.blueprint.slice();return d[ie]=o,d[S]=n|4|128|8|64,(c!==null||e&&e[S]&2048)&&(d[S]|=2048),kp(d),d[de]=d[Er]=e,d[Fe]=r,d[it]=s||e&&e[it],d[V]=a||e&&e[V],d[fr]=u||e&&e[fr]||null,d[Le]=i,d[Ls]=WE(),d[qe]=l,d[xp]=c,d[Ne]=t.type==2?e[Ne]:d,d}function xr(e,t,r,n,o){let i=e.data[t];if(i===null)i=KI(e,t,r,n,o),mE()&&(i.flags|=32);else if(i.type&64){i.type=r,i.value=n,i.attrs=o;let s=hE();i.injectorIndex=s===null?-1:s.injectorIndex}return Rn(i,!0),i}function KI(e,t,r,n,o){let i=$p(),s=nl(),a=s?i:i&&i.parent,u=e.data[t]=oC(e,a,r,t,n,o);return e.firstChild===null&&(e.firstChild=u),i!==null&&(s?i.child==null&&u.parent!==null&&(i.child=u):i.next===null&&(i.next=u,u.prev=i)),u}function Hg(e,t,r,n){if(r===0)return-1;let o=t.length;for(let i=0;i<r;i++)t.push(n),e.blueprint.push(n),e.data.push(null);return o}function zg(e,t,r,n,o){let i=ut(),s=n&2;try{bn(-1),s&&t.length>G&&Vg(e,t,G,!1),mt(s?2:0,o),r(n,o)}finally{bn(i),mt(s?3:1,o)}}function xl(e,t,r){if(Kc(t)){let n=j(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let u=r[s];a.contentQueries(1,u,s)}}}finally{j(n)}}}function Al(e,t,r){jp()&&(lC(e,t,r,$e(r,t)),(r.flags&64)===64&&Zg(e,t,r))}function _l(e,t,r=$e){let n=t.localNames;if(n!==null){let o=t.index+1;for(let i=0;i<n.length;i+=2){let s=n[i+1],a=s===-1?r(t,e):e[s];e[o++]=a}}}function qg(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Nl(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Nl(e,t,r,n,o,i,s,a,u,c,l){let d=G+n,f=d+o,h=JI(d,f),p=typeof c=="function"?c():c;return h[x]={type:e,blueprint:h,template:r,queries:null,viewQuery:a,declTNode:t,data:h.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:f,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:u,consts:p,incompleteFirstPass:!1,ssrId:l}}function JI(e,t){let r=[];for(let n=0;n<t;n++)r.push(n<e?null:fe);return r}function XI(e,t,r,n){let i=n.get(vg,mg)||r===ot.ShadowDom,s=e.selectRootElement(t,i);return eC(s),s}function eC(e){Wg(e)}var Wg=()=>null;function tC(e){ag(e)?kg(e):rI(e)}function nC(){Wg=tC}function rC(e,t,r,n){let o=Kg(t);o.push(r),e.firstCreatePass&&Jg(e).push(n,o.length-1)}function oC(e,t,r,n,o,i){let s=t?t.injectorIndex:-1,a=0;return br()&&(a|=128),{type:r,index:n,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function _h(e,t,r,n,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;n!=null||(n={});let a,u=ze.None;Array.isArray(s)?(a=s[0],u=s[1]):a=s;let c=i;if(o!==null){if(!o.hasOwnProperty(i))continue;c=o[i]}e===0?Nh(n,r,c,a,u):Nh(n,r,c,a)}return n}function Nh(e,t,r,n,o){let i;e.hasOwnProperty(r)?(i=e[r]).push(t,n):i=e[r]=[t,n],o!==void 0&&i.push(o)}function iC(e,t,r){let n=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],u=null,c=null;for(let l=n;l<o;l++){let d=i[l],f=r?r.get(d):null,h=f?f.inputs:null,p=f?f.outputs:null;u=_h(0,d.inputs,l,u,h),c=_h(1,d.outputs,l,c,p);let y=u!==null&&s!==null&&!Gc(t)?EC(u,l,s):null;a.push(y)}u!==null&&(u.hasOwnProperty("class")&&(t.flags|=8),u.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=u,t.outputs=c}function sC(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Js(e,t,r,n,o,i,s,a){let u=$e(t,r),c=t.inputs,l;!a&&c!=null&&(l=c[n])?(Ol(e,r,l,n,o),Ir(t)&&aC(r,t.index)):t.type&3?(n=sC(n),o=s!=null?s(o,t.value||"",n):o,i.setProperty(u,n,o)):t.type&12}function aC(e,t){let r=Xt(t,e);r[S]&16||(r[S]|=64)}function Rl(e,t,r,n){if(jp()){let o=n===null?null:{"":-1},i=fC(e,r),s,a;i===null?s=a=null:[s,a]=i,s!==null&&Gg(e,t,r,s,o,a),o&&hC(r,n,o)}r.mergedAttrs=co(r.mergedAttrs,r.attrs)}function Gg(e,t,r,n,o,i){var c,l;for(let d=0;d<n.length;d++)Wu(ds(r,t),e,n[d].type);gC(r,e.data.length,n.length);for(let d=0;d<n.length;d++){let f=n[d];f.providersResolver&&f.providersResolver(f)}let s=!1,a=!1,u=Hg(e,t,n.length,null);for(let d=0;d<n.length;d++){let f=n[d];r.mergedAttrs=co(r.mergedAttrs,f.hostAttrs),mC(e,r,t,u,f),pC(u,f,o),f.contentQueries!==null&&(r.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(r.flags|=64);let h=f.type.prototype;!s&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&(((c=e.preOrderHooks)!=null?c:e.preOrderHooks=[]).push(r.index),s=!0),!a&&(h.ngOnChanges||h.ngDoCheck)&&(((l=e.preOrderCheckHooks)!=null?l:e.preOrderCheckHooks=[]).push(r.index),a=!0),u++}iC(e,r,i)}function uC(e,t,r,n,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;cC(s)!=a&&s.push(a),s.push(r,n,i)}}function cC(e){let t=e.length;for(;t>0;){let r=e[--t];if(typeof r=="number"&&r<0)return r}return 0}function lC(e,t,r,n){let o=r.directiveStart,i=r.directiveEnd;Ir(r)&&vC(t,r,e.data[o+r.componentOffset]),e.firstCreatePass||ds(r,t),Qt(n,t);let s=r.initialInputs;for(let a=o;a<i;a++){let u=e.data[a],c=Tn(t,e,a,r);if(Qt(c,t),s!==null&&wC(t,a-o,c,u,r,s),Yt(u)){let l=Xt(r.index,t);l[Fe]=Tn(t,e,a,r)}}}function Zg(e,t,r){let n=r.directiveStart,o=r.directiveEnd,i=r.index,s=yE();try{bn(i);for(let a=n;a<o;a++){let u=e.data[a],c=t[a];zu(a),(u.hostBindings!==null||u.hostVars!==0||u.hostAttrs!==null)&&dC(u,c)}}finally{bn(-1),zu(s)}}function dC(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function fC(e,t){var i;let r=e.directiveRegistry,n=null,o=null;if(r)for(let s=0;s<r.length;s++){let a=r[s];if(gp(t,a.selectors,!1))if(n||(n=[]),Yt(a))if(a.findHostDirectiveDefs!==null){let u=[];o=o||new Map,a.findHostDirectiveDefs(a,u,o),n.unshift(...u,a);let c=u.length;cc(e,t,c)}else n.unshift(a),cc(e,t,0);else o=o||new Map,(i=a.findHostDirectiveDefs)==null||i.call(a,a,n,o),n.push(a)}return n===null?null:[n,o]}function cc(e,t,r){var n;t.componentOffset=r,((n=e.components)!=null?n:e.components=[]).push(t.index)}function hC(e,t,r){if(t){let n=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=r[t[o+1]];if(i==null)throw new D(-301,!1);n.push(t[o],i)}}}function pC(e,t,r){if(r){if(t.exportAs)for(let n=0;n<t.exportAs.length;n++)r[t.exportAs[n]]=e;Yt(t)&&(r[""]=e)}}function gC(e,t,r){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+r,e.providerIndexes=t}function mC(e,t,r,n,o){e.data[n]=o;let i=o.factory||(o.factory=En(o.type,!0)),s=new Mn(i,Yt(o),$);e.blueprint[n]=s,r[n]=s,uC(e,t,n,Hg(e,r,o.hostVars,fe),o)}function vC(e,t,r){let n=$e(t,e),o=qg(r),i=e[it].rendererFactory,s=16;r.signals?s=4096:r.onPush&&(s=64);let a=Xs(e,Ks(e,o,null,s,n,t,null,i.createRenderer(n,r),null,null,null));e[t.index]=a}function yC(e,t,r,n,o,i){let s=$e(e,t);DC(t[V],s,i,e.value,r,n,o)}function DC(e,t,r,n,o,i,s){if(i==null)e.removeAttribute(t,o,r);else{let a=s==null?oe(i):s(i,n||"",o);e.setAttribute(t,o,a,r)}}function wC(e,t,r,n,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let u=s[a++],c=s[a++],l=s[a++],d=s[a++];Bg(n,r,u,c,l,d)}}function EC(e,t,r){let n=null,o=0;for(;o<r.length;){let i=r[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){n===null&&(n=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){n.push(i,s[a+1],s[a+2],r[o+1]);break}}o+=2}return n}function Yg(e,t,r,n){return[e,!0,0,t,null,n,null,r,null,null]}function Qg(e,t){let r=e.contentQueries;if(r!==null){let n=j(null);try{for(let o=0;o<r.length;o+=2){let i=r[o],s=r[o+1];if(s!==-1){let a=e.data[s];ol(i),a.contentQueries(2,t[s],s)}}}finally{j(n)}}}function Xs(e,t){return e[ho]?e[ph][nt]=t:e[ho]=t,e[ph]=t,t}function lc(e,t,r){ol(0);let n=j(null);try{t(e,r)}finally{j(n)}}function Kg(e){return e[fo]||(e[fo]=[])}function Jg(e){return e.cleanup||(e.cleanup=[])}function Xg(e,t){let r=e[fr],n=r?r.get(st,null):null;n&&n.handleError(t)}function Ol(e,t,r,n,o){for(let i=0;i<r.length;){let s=r[i++],a=r[i++],u=r[i++],c=t[s],l=e.data[s];Bg(l,c,n,a,u,o)}}function No(e,t,r){let n=Fp(t,e);OI(e[V],n,r)}function IC(e,t){let r=Xt(t,e),n=r[x];CC(n,r);let o=r[ie];o!==null&&r[qe]===null&&(r[qe]=ml(o,r[fr])),Pl(n,r,r[Fe])}function CC(e,t){for(let r=t.length;r<e.blueprint.length;r++)t.push(e.blueprint[r])}function Pl(e,t,r){var n;il(t);try{let o=e.viewQuery;o!==null&&lc(1,o,r);let i=e.template;i!==null&&zg(e,t,i,1,r),e.firstCreatePass&&(e.firstCreatePass=!1),(n=t[rt])==null||n.finishViewCreation(e),e.staticContentQueries&&Qg(e,t),e.staticViewQueries&&lc(2,e.viewQuery,r);let s=e.components;s!==null&&bC(t,s)}catch(o){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),o}finally{t[S]&=-5,sl()}}function bC(e,t){for(let r=0;r<t.length;r++)IC(e,t[r])}function em(e,t,r,n){var i,s,a;let o=j(null);try{let u=t.tView,l=e[S]&4096?4096:16,d=Ks(e,u,r,l,null,t,null,null,(i=n==null?void 0:n.injector)!=null?i:null,(s=n==null?void 0:n.embeddedViewInjector)!=null?s:null,(a=n==null?void 0:n.dehydratedView)!=null?a:null),f=e[t.index];d[So]=f;let h=e[rt];return h!==null&&(d[rt]=h.createEmbeddedView(u)),Pl(u,d,r),d}finally{j(o)}}function MC(e,t){let r=ye+t;if(r<e.length)return e[r]}function dc(e,t){return!t||t.firstChild===null||fs(e)}function tm(e,t,r,n=!0){let o=t[x];if(LI(o,t,e,r),n){let s=uc(r,e),a=t[V],u=Ml(a,e[Nt]);u!==null&&FI(o,e[Le],a,t,u,s)}let i=t[qe];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function TC(e,t){let r=ps(e,t);return r!==void 0&&Cl(r[x],r),r}function Do(e,t,r,n,o=!1){for(;r!==null;){let i=t[r.index];i!==null&&n.push(le(i)),je(i)&&nm(i,n);let s=r.type;if(s&8)Do(e,t,r.child,n);else if(s&32){let a=wl(r,t),u;for(;u=a();)n.push(u)}else if(s&16){let a=Fg(t,r);if(Array.isArray(a))n.push(...a);else{let u=mo(t[Ne]);Do(u[x],u,a,n,!0)}}r=o?r.projectionNext:r.next}return n}function nm(e,t){for(let r=ye;r<e.length;r++){let n=e[r],o=n[x].firstChild;o!==null&&Do(n[x],n,o,t)}e[Nt]!==e[ie]&&t.push(e[Nt])}var rm=[];function SC(e){var t;return(t=e[Cn])!=null?t:xC(e)}function xC(e){var r;let t=(r=rm.pop())!=null?r:Object.create(_C);return t.lView=e,t}function AC(e){e.lView[Cn]!==e&&(e.lView=null,rm.push(e))}var _C=te(w({},du),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{go(e.lView)},consumerOnSignalRead(){this.lView[Cn]=this}}),om=100;function im(e,t=!0,r=0){var s,a,u;let n=e[it],o=n.rendererFactory,i=!1;i||(s=o.begin)==null||s.call(o);try{NC(e,r)}catch(c){throw t&&Xg(e,c),c}finally{i||((a=o.end)==null||a.call(o),(u=n.inlineEffectRunner)==null||u.flush())}}function NC(e,t){fc(e,t);let r=0;for(;tl(e);){if(r===om)throw new D(103,!1);r++,fc(e,1)}}function RC(e,t,r,n){var u;let o=t[S];if((o&256)===256)return;let i=!1;!i&&((u=t[it].inlineEffectRunner)==null||u.flush()),il(t);let s=null,a=null;!i&&OC(e)&&(a=SC(t),s=Zf(a));try{kp(t),gE(e.bindingStartIndex),r!==null&&zg(e,t,r,2,n);let c=(o&3)===3;if(!i)if(c){let f=e.preOrderCheckHooks;f!==null&&Ji(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Xi(t,f,0,null),wu(t,0)}if(PC(t),sm(t,0),e.contentQueries!==null&&Qg(e,t),!i)if(c){let f=e.contentCheckHooks;f!==null&&Ji(t,f)}else{let f=e.contentHooks;f!==null&&Xi(t,f,1),wu(t,1)}QI(e,t);let l=e.components;l!==null&&um(t,l,0);let d=e.viewQuery;if(d!==null&&lc(2,d,n),!i)if(c){let f=e.viewCheckHooks;f!==null&&Ji(t,f)}else{let f=e.viewHooks;f!==null&&Xi(t,f,2),wu(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Du]){for(let f of t[Du])f();t[Du]=null}i||(t[S]&=-73)}catch(c){throw go(t),c}finally{a!==null&&(Yf(a,s),AC(a)),sl()}}function OC(e){return e.type!==2}function sm(e,t){for(let r=cg(e);r!==null;r=lg(r))for(let n=ye;n<r.length;n++){let o=r[n];am(o,t)}}function PC(e){for(let t=cg(e);t!==null;t=lg(t)){if(!(t[S]&Qc.HasTransplantedViews))continue;let r=t[hr];for(let n=0;n<r.length;n++){let o=r[n],i=o[de];oE(o)}}}function FC(e,t,r){let n=Xt(t,e);am(n,r)}function am(e,t){el(e)&&fc(e,t)}function fc(e,t){let n=e[x],o=e[S],i=e[Cn],s=!!(t===0&&o&16);if(s||(s=!!(o&64&&t===0)),s||(s=!!(o&1024)),s||(s=!!(i!=null&&i.dirty&&hu(i))),i&&(i.dirty=!1),e[S]&=-9217,s)RC(n,e,n.template,e[Fe]);else if(o&8192){sm(e,1);let a=n.components;a!==null&&um(e,a,1)}}function um(e,t,r){for(let n=0;n<t.length;n++)FC(e,t[n],r)}function Fl(e){var t;for((t=e[it].changeDetectionScheduler)==null||t.notify();e;){e[S]|=64;let r=mo(e);if(Jc(e)&&!r)return e;e=r}return null}var Sn=class{get rootNodes(){let t=this._lView,r=t[x];return Do(r,t,r.firstChild,[])}constructor(t,r,n=!0){this._lView=t,this._cdRefInjectingView=r,this.notifyErrorHandler=n,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[Fe]}set context(t){this._lView[Fe]=t}get destroyed(){return(this._lView[S]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[de];if(je(t)){let r=t[as],n=r?r.indexOf(this):-1;n>-1&&(ps(t,n),is(r,n))}this._attachedToViewContainer=!1}Cl(this._lView[x],this._lView)}onDestroy(t){Lp(this._lView,t)}markForCheck(){Fl(this._cdRefInjectingView||this._lView)}detach(){this._lView[S]&=-129}reattach(){Hu(this._lView),this._lView[S]|=128}detectChanges(){this._lView[S]|=1024,im(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new D(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,Ng(this._lView[x],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new D(902,!1);this._appRef=t,Hu(this._lView)}},xn=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=jC;let e=t;return e})(),kC=xn,LC=class extends kC{constructor(t,r,n){super(),this._declarationLView=t,this._declarationTContainer=r,this.elementRef=n}get ssrId(){var t;return((t=this._declarationTContainer.tView)==null?void 0:t.ssrId)||null}createEmbeddedView(t,r){return this.createEmbeddedViewImpl(t,r)}createEmbeddedViewImpl(t,r,n){let o=em(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:r,dehydratedView:n});return new Sn(o)}};function jC(){return ea(we(),T())}function ea(e,t){return e.type&4?new LC(t,e,Sr(e,t)):null}var hc="<-- AT THIS LOCATION";function $C(e){switch(e){case 4:return"view container";case 2:return"element";case 8:return"ng-container";case 32:return"icu";case 64:return"i18n";case 16:return"projection";case 1:return"text";default:return"<unknown>"}}function VC(e,t){let r=`During serialization, Angular was unable to find an element in the DOM:

`,n=`${qC(e,t,!1)}

`,o=GC();throw new D(-502,r+n+o)}function UC(e){let t="During serialization, Angular detected DOM nodes that were created outside of Angular context and provided as projectable nodes (likely via `ViewContainerRef.createComponent` or `createComponent` APIs). Hydration is not supported for such cases, consider refactoring the code to avoid this pattern or using `ngSkipHydration` on the host element of the component.\n\n",r=`${WC(e)}

`,n=t+r+ZC();return new D(-503,n)}function BC(e){let t=[];if(e.attrs)for(let r=0;r<e.attrs.length;){let n=e.attrs[r++];if(typeof n=="number")break;let o=e.attrs[r++];t.push(`${n}="${ms(o)}"`)}return t.join(" ")}var HC=new Set(["ngh","ng-version","ng-server-context"]);function zC(e){let t=[];for(let r=0;r<e.attributes.length;r++){let n=e.attributes[r];HC.has(n.name)||t.push(`${n.name}="${ms(n.value)}"`)}return t.join(" ")}function Mu(e,t="\u2026"){switch(e.type){case 1:return`#text${e.value?`(${e.value})`:""}`;case 2:let n=BC(e),o=e.value.toLowerCase();return`<${o}${n?" "+n:""}>${t}</${o}>`;case 8:return"<!-- ng-container -->";case 4:return"<!-- container -->";default:return`#node(${$C(e.type)})`}}function ts(e,t="\u2026"){var n;let r=e;switch(r.nodeType){case Node.ELEMENT_NODE:let o=r.tagName.toLowerCase(),i=zC(r);return`<${o}${i?" "+i:""}>${t}</${o}>`;case Node.TEXT_NODE:let s=r.textContent?ms(r.textContent):"";return`#text${s?`(${s})`:""}`;case Node.COMMENT_NODE:return`<!-- ${ms((n=r.textContent)!=null?n:"")} -->`;default:return`#node(${r.nodeType})`}}function qC(e,t,r){let n="  ",o="";t.prev?(o+=n+`\u2026
`,o+=n+Mu(t.prev)+`
`):t.type&&t.type&12&&(o+=n+`\u2026
`),r?(o+=n+Mu(t)+`
`,o+=n+`<!-- container -->  ${hc}
`):o+=n+Mu(t)+`  ${hc}
`,o+=n+`\u2026
`;let i=t.type?bl(e[x],t,e):null;return i&&(o=ts(i,`
`+o)),o}function WC(e){let t="  ",r="",n=e;return n.previousSibling&&(r+=t+`\u2026
`,r+=t+ts(n.previousSibling)+`
`),r+=t+ts(n)+`  ${hc}
`,e.nextSibling&&(r+=t+`\u2026
`),e.parentNode&&(r=ts(n.parentNode,`
`+r)),r}function GC(e){return`To fix this problem:
  * check ${e?`the "${e}"`:"corresponding"} component for hydration-related issues
  * check to see if your template has valid HTML structure
  * or skip hydration by adding the \`ngSkipHydration\` attribute to its host node in a template

`}function ZC(){return`Note: attributes are only displayed to better represent the DOM but have no effect on hydration mismatches.

`}function YC(e){return e.replace(/\s+/gm,"")}function ms(e,t=50){return e?(e=YC(e),e.length>t?`${e.substring(0,t-1)}\u2026`:e):""}function cm(e){var o;let t=(o=e[po])!=null?o:[],n=e[de][V];for(let i of t)QC(i,n);e[po]=be}function QC(e,t){let r=0,n=e.firstChild;if(n){let o=e.data[gr];for(;r<o;){let i=n.nextSibling;Tl(t,n,!1),n=i,r++}}}function lm(e){cm(e);for(let t=ye;t<e.length;t++)vs(e[t])}function KC(e){var r;let t=(r=e[qe])==null?void 0:r.i18nNodes;if(t){let n=e[V];for(let o of t.values())Tl(n,o,!1);e[qe].i18nNodes=void 0}}function vs(e){KC(e);let t=e[x];for(let r=G;r<t.bindingStartIndex;r++)if(je(e[r])){let n=e[r];lm(n)}else _t(e[r])&&vs(e[r])}function JC(e){let t=e._views;for(let r of t){let n=pg(r);if(n!==null&&n[ie]!==null)if(_t(n))vs(n);else{let o=n[ie];vs(o),lm(n)}}}var XC=new RegExp(`^(\\d+)*(${pl}|${hl})*(.*)`);function eb(e,t){let r=[e];for(let n of t){let o=r.length-1;if(o>0&&r[o-1]===n){let i=r[o]||1;r[o]=i+1}else r.push(n,"")}return r.join("")}function tb(e){let t=e.match(XC),[r,n,o,i]=t,s=n?parseInt(n,10):o,a=[];for(let[u,c,l]of i.matchAll(/(f|n)(\d*)/g)){let d=parseInt(l,10)||1;a.push(c,d)}return[s,...a]}function nb(e){var t;return!e.prev&&((t=e.parent)==null?void 0:t.type)===8}function Tu(e){return e.index-G}function wo(e,t){var r;return!(e.type&16)&&!!t[e.index]&&!((r=le(t[e.index]))!=null&&r.isConnected)}function rb(e,t){let r=e.i18nNodes;if(r){let n=r.get(t);return n&&r.delete(t),n}return null}function ta(e,t,r,n){var s;let o=Tu(n),i=rb(e,o);if(!i){let a=e.data[no];if(a!=null&&a[o])i=ib(a[o],r);else if(t.firstChild===n)i=e.firstChild;else{let u=n.prev===null,c=(s=n.prev)!=null?s:n.parent;if(nb(n)){let l=Tu(n.parent);i=Xu(e,l)}else{let l=$e(c,r);if(u)i=l.firstChild;else{let d=Tu(c),f=Xu(e,d);if(c.type===2&&f){let p=vl(e,d)+1;i=na(p,f)}else i=l.nextSibling}}}}return i}function na(e,t){let r=t;for(let n=0;n<e;n++)r=r.nextSibling;return r}function ob(e,t){let r=e;for(let n=0;n<t.length;n+=2){let o=t[n],i=t[n+1];for(let s=0;s<i;s++)switch(o){case yo.FirstChild:r=r.firstChild;break;case yo.NextSibling:r=r.nextSibling;break}}return r}function ib(e,t){let[r,...n]=tb(e),o;if(r===hl)o=t[Ne][ie];else if(r===pl)o=NI(t[Ne][ie]);else{let i=Number(r);o=le(t[i+G])}return ob(o,n)}function pc(e,t){if(e===t)return[];if(e.parentElement==null||t.parentElement==null)return null;if(e.parentElement===t.parentElement)return sb(e,t);{let r=t.parentElement,n=pc(e,r),o=pc(r.firstChild,t);return!n||!o?null:[...n,yo.FirstChild,...o]}}function sb(e,t){let r=[],n=null;for(n=e;n!=null&&n!==t;n=n.nextSibling)r.push(yo.NextSibling);return n==null?null:r}function Rh(e,t,r){let n=pc(e,t);return n===null?null:eb(r,n)}function ab(e,t){let r=e.parent,n,o,i;for(;r!==null&&wo(r,t);)r=r.parent;r===null||!(r.type&3)?(n=i=hl,o=t[Ne][ie]):(n=r.index,o=le(t[n]),i=oe(n-G));let s=le(t[e.index]);if(e.type&12){let u=so(t,e);u&&(s=u)}let a=Rh(o,s,i);if(a===null&&o!==s){let u=o.ownerDocument.body;if(a=Rh(u,s,pl),a===null)throw VC(t,e)}return a}function ub(e,t){var n;let r=[];for(let o of t)for(let i=0;i<((n=o[ir])!=null?n:1);i++){let s={data:o,firstChild:null};o[gr]>0&&(s.firstChild=e,e=na(o[gr],e)),r.push(s)}return[e,r]}var dm=()=>null;function cb(e,t){let r=e[po];return!t||r===null||r.length===0?null:r[0].data[Ju]===t?r.shift():(cm(e),null)}function lb(){dm=cb}function gc(e,t){return dm(e,t)}var ys=class{},mc=class{},Ds=class{};function db(e){let t=Error(`No component factory found for ${Me(e)}.`);return t[fb]=e,t}var fb="ngComponent";var vc=class{resolveComponentFactory(t){throw db(t)}},ra=(()=>{let t=class t{};t.NULL=new vc;let e=t;return e})(),Eo=class{},On=(()=>{let t=class t{constructor(){this.destroyNode=null}};t.__NG_ELEMENT_ID__=()=>hb();let e=t;return e})();function hb(){let e=T(),t=we(),r=Xt(t.index,e);return(_t(r)?r:e)[V]}var pb=(()=>{let t=class t{};t.\u0275prov=E({token:t,providedIn:"root",factory:()=>null});let e=t;return e})(),Su={};var Oh=new Set;function tn(e){var t;Oh.has(e)||(Oh.add(e),(t=performance==null?void 0:performance.mark)==null||t.call(performance,"mark_feature_usage",{detail:{feature:e}}))}function Ph(...e){}function gb(){let e=typeof _e.requestAnimationFrame=="function",t=_e[e?"requestAnimationFrame":"setTimeout"],r=_e[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&r){let n=t[Zone.__symbol__("OriginalDelegate")];n&&(t=n);let o=r[Zone.__symbol__("OriginalDelegate")];o&&(r=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:r}}var W=class e{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:n=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new ge(!1),this.onMicrotaskEmpty=new ge(!1),this.onStable=new ge(!1),this.onError=new ge(!1),typeof Zone>"u")throw new D(908,!1);Zone.assertZonePatched();let o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!n&&r,o.shouldCoalesceRunChangeDetection=n,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=gb().nativeRequestAnimationFrame,yb(o)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new D(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new D(909,!1)}run(t,r,n){return this._inner.run(t,r,n)}runTask(t,r,n,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,mb,Ph,Ph);try{return i.runTask(s,r,n)}finally{i.cancelTask(s)}}runGuarded(t,r,n){return this._inner.runGuarded(t,r,n)}runOutsideAngular(t){return this._outer.run(t)}},mb={};function kl(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function vb(e){e.isCheckStableRunning||e.lastRequestAnimationFrameId!==-1||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(_e,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,yc(e),e.isCheckStableRunning=!0,kl(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),yc(e))}function yb(e){let t=()=>{vb(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(r,n,o,i,s,a)=>{if(Db(a))return r.invokeTask(o,i,s,a);try{return Fh(e),r.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&i.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),kh(e)}},onInvoke:(r,n,o,i,s,a,u)=>{try{return Fh(e),r.invoke(o,i,s,a,u)}finally{e.shouldCoalesceRunChangeDetection&&t(),kh(e)}},onHasTask:(r,n,o,i)=>{r.hasTask(o,i),n===o&&(i.change=="microTask"?(e._hasPendingMicrotasks=i.microTask,yc(e),kl(e)):i.change=="macroTask"&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(r,n,o,i)=>(r.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}function yc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.lastRequestAnimationFrameId!==-1?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Fh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function kh(e){e._nesting--,kl(e)}var Dc=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new ge,this.onMicrotaskEmpty=new ge,this.onStable=new ge,this.onError=new ge}run(t,r,n){return t.apply(r,n)}runGuarded(t,r,n){return t.apply(r,n)}runOutsideAngular(t){return t()}runTask(t,r,n,o){return t.apply(r,n)}};function Db(e){var t;return!Array.isArray(e)||e.length!==1?!1:((t=e[0].data)==null?void 0:t.__ignore_ng_zone__)===!0}function wb(e="zone.js",t){return e==="noop"?new Dc:e==="zone.js"?new W(t):e}var ar=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(ar||{}),Eb={destroy(){}};function oa(e,t){var c,l,d;!t&&qw(oa);let r=(c=t==null?void 0:t.injector)!=null?c:g(ct);if(!ro(r))return Eb;tn("NgAfterNextRender");let n=r.get(Ll),o=(l=n.handler)!=null?l:n.handler=new Ec,i=(d=t==null?void 0:t.phase)!=null?d:ar.MixedReadWrite,s=()=>{o.unregister(u),a()},a=r.get(ll).onDestroy(s),u=at(r,()=>new wc(i,()=>{s(),e()}));return o.register(u),{destroy:s}}var wc=class{constructor(t,r){var n;this.phase=t,this.callbackFn=r,this.zone=g(W),this.errorHandler=g(st,{optional:!0}),(n=g(ys,{optional:!0}))==null||n.notify(1)}invoke(){var t;try{this.zone.runOutsideAngular(this.callbackFn)}catch(r){(t=this.errorHandler)==null||t.handleError(r)}}},Ec=class{constructor(){this.executingCallbacks=!1,this.buckets={[ar.EarlyRead]:new Set,[ar.Write]:new Set,[ar.MixedReadWrite]:new Set,[ar.Read]:new Set},this.deferredCallbacks=new Set}register(t){(this.executingCallbacks?this.deferredCallbacks:this.buckets[t.phase]).add(t)}unregister(t){this.buckets[t.phase].delete(t),this.deferredCallbacks.delete(t)}execute(){this.executingCallbacks=!0;for(let t of Object.values(this.buckets))for(let r of t)r.invoke();this.executingCallbacks=!1;for(let t of this.deferredCallbacks)this.buckets[t.phase].add(t);this.deferredCallbacks.clear()}destroy(){for(let t of Object.values(this.buckets))t.clear();this.deferredCallbacks.clear()}},Ll=(()=>{let t=class t{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){var n;this.executeInternalCallbacks(),(n=this.handler)==null||n.execute()}executeInternalCallbacks(){let n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let o of n)o()}ngOnDestroy(){var n;(n=this.handler)==null||n.destroy(),this.handler=null,this.internalCallbacks.length=0}};t.\u0275prov=E({token:t,providedIn:"root",factory:()=>new t});let e=t;return e})();function ws(e,t,r){let n=r?e.styles:null,o=r?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Pu(o,a);else if(i==2){let u=a,c=t[++s];n=Pu(n,u+": "+c+";")}}r?e.styles=n:e.stylesWithoutHost=n,r?e.classes=o:e.classesWithoutHost=o}var Es=class extends ra{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let r=Dt(t);return new An(r,this.ngModule)}};function Lh(e){let t=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let n=e[r];n!==void 0&&t.push({propName:Array.isArray(n)?n[0]:n,templateName:r})}return t}function Ib(e){let t=e.toLowerCase();return t==="svg"?Op:t==="math"?Xw:null}var Ic=class{constructor(t,r){this.injector=t,this.parentInjector=r}get(t,r,n){n=Os(n);let o=this.injector.get(t,Su,n);return o!==Su||r===Su?o:this.parentInjector.get(t,r,n)}},An=class extends Ds{get inputs(){let t=this.componentDef,r=t.inputTransforms,n=Lh(t.inputs);if(r!==null)for(let o of n)r.hasOwnProperty(o.propName)&&(o.transform=r[o.propName]);return n}get outputs(){return Lh(this.componentDef.outputs)}constructor(t,r){super(),this.componentDef=t,this.ngModule=r,this.componentType=t.type,this.selector=Aw(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!r}create(t,r,n,o){let i=j(null);try{o=o||this.ngModule;let a=o instanceof De?o:o==null?void 0:o.injector;a&&this.componentDef.getStandaloneInjector!==null&&(a=this.componentDef.getStandaloneInjector(a)||a);let u=a?new Ic(t,a):t,c=u.get(Eo,null);if(c===null)throw new D(407,!1);let l=u.get(pb,null),d=u.get(Ll,null),f=u.get(ys,null),h={rendererFactory:c,sanitizer:l,inlineEffectRunner:null,afterRenderEventManager:d,changeDetectionScheduler:f},p=c.createRenderer(null,this.componentDef),y=this.componentDef.selectors[0][0]||"div",m=n?XI(p,n,this.componentDef.encapsulation,u):Zs(p,y,Ib(y)),v=512;this.componentDef.signals?v|=4096:this.componentDef.onPush||(v|=16);let A=null;m!==null&&(A=ml(m,u,!0));let q=Nl(0,null,null,1,0,null,null,null,null,null,null),O=Ks(null,q,null,v,null,null,h,p,u,null,A);il(O);let re,Oe;try{let ve=this.componentDef,Tt,Gr=null;ve.findHostDirectiveDefs?(Tt=[],Gr=new Map,ve.findHostDirectiveDefs(ve,Tt,Gr),Tt.push(ve)):Tt=[ve];let fn=Cb(O,m),Zr=bb(fn,m,ve,Tt,O,h,p);Oe=Xc(q,G),m&&Sb(p,ve,m,n),r!==void 0&&xb(Oe,this.ngContentSelectors,r),re=Tb(Zr,ve,Tt,Gr,O,[Ab]),Pl(q,O,null)}finally{sl()}return new Cc(this.componentType,re,Sr(Oe,O),O,Oe)}finally{j(i)}}},Cc=class extends mc{constructor(t,r,n,o,i){super(),this.location=n,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=r,this.hostView=this.changeDetectorRef=new Sn(o,void 0,!1),this.componentType=t}setInput(t,r){var i;let n=this._tNode.inputs,o;if(n!==null&&(o=n[t])){if((i=this.previousInputValues)!=null||(this.previousInputValues=new Map),this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),r))return;let s=this._rootLView;Ol(s[x],s,o,t,r),this.previousInputValues.set(t,r);let a=Xt(this._tNode.index,s);Fl(a)}}get injector(){return new wn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Cb(e,t){let r=e[x],n=G;return e[n]=t,xr(r,n,2,"#host",null)}function bb(e,t,r,n,o,i,s){let a=o[x];Mb(n,e,t,s);let u=null;t!==null&&(u=ml(t,o[fr]));let c=i.rendererFactory.createRenderer(t,r),l=16;r.signals?l=4096:r.onPush&&(l=64);let d=Ks(o,qg(r),null,l,o[e.index],e,i,c,null,null,u);return a.firstCreatePass&&cc(a,e,n.length-1),Xs(o,d),o[e.index]=d}function Mb(e,t,r,n){for(let o of e)t.mergedAttrs=co(t.mergedAttrs,o.hostAttrs);t.mergedAttrs!==null&&(ws(t,t.mergedAttrs,!0),r!==null&&$g(n,r,t))}function Tb(e,t,r,n,o,i){let s=we(),a=o[x],u=$e(s,o);Gg(a,o,s,r,null,n);for(let l=0;l<r.length;l++){let d=s.directiveStart+l,f=Tn(o,a,d,s);Qt(f,o)}Zg(a,o,s),u&&Qt(u,o);let c=Tn(o,a,s.directiveStart+s.componentOffset,s);if(e[Fe]=o[Fe]=c,i!==null)for(let l of i)l(c,t);return xl(a,s,o),c}function Sb(e,t,r,n){if(n)ju(e,r,["ng-version","17.3.12"]);else{let{attrs:o,classes:i}=_w(t.selectors[0]);o&&ju(e,r,o),i&&i.length>0&&jg(e,r,i.join(" "))}}function xb(e,t,r){let n=e.projection=[];for(let o=0;o<t.length;o++){let i=r[o];n.push(i!=null?Array.from(i):null)}}function Ab(){let e=we();Bs(T()[x],e)}var nn=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=_b;let e=t;return e})();function _b(){let e=we();return hm(e,T())}var Nb=nn,fm=class extends Nb{constructor(t,r,n){super(),this._lContainer=t,this._hostTNode=r,this._hostLView=n}get element(){return Sr(this._hostTNode,this._hostLView)}get injector(){return new wn(this._hostTNode,this._hostLView)}get parentInjector(){let t=al(this._hostTNode,this._hostLView);if(Yp(t)){let r=cs(t,this._hostLView),n=us(t),o=r[x].data[n+8];return new wn(o,r)}else return new wn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let r=jh(this._lContainer);return r!==null&&r[t]||null}get length(){return this._lContainer.length-ye}createEmbeddedView(t,r,n){let o,i;typeof n=="number"?o=n:n!=null&&(o=n.index,i=n.injector);let s=gc(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(r||{},i,s);return this.insertImpl(a,o,dc(this._hostTNode,s)),a}createComponent(t,r,n,o,i){var p,y,m;let s=t&&!Gw(t),a;if(s)a=r;else{let v=r||{};a=v.index,n=v.injector,o=v.projectableNodes,i=v.environmentInjector||v.ngModuleRef}let u=s?t:new An(Dt(t)),c=n||this.parentInjector;if(!i&&u.ngModule==null){let A=(s?c:this.parentInjector).get(De,null);A&&(i=A)}let l=Dt((p=u.componentType)!=null?p:{}),d=gc(this._lContainer,(y=l==null?void 0:l.id)!=null?y:null),f=(m=d==null?void 0:d.firstChild)!=null?m:null,h=u.create(c,o,f,i);return this.insertImpl(h.hostView,a,dc(this._hostTNode,d)),h}insert(t,r){return this.insertImpl(t,r,!0)}insertImpl(t,r,n){let o=t._lView;if(rE(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let u=o[de],c=new fm(u,u[Le],u[de]);c.detach(c.indexOf(t))}}let i=this._adjustIndex(r),s=this._lContainer;return tm(s,o,i,n),t.attachToViewContainerRef(),cp(xu(s),i,t),t}move(t,r){return this.insert(t,r)}indexOf(t){let r=jh(this._lContainer);return r!==null?r.indexOf(t):-1}remove(t){let r=this._adjustIndex(t,-1),n=ps(this._lContainer,r);n&&(is(xu(this._lContainer),r),Cl(n[x],n))}detach(t){let r=this._adjustIndex(t,-1),n=ps(this._lContainer,r);return n&&is(xu(this._lContainer),r)!=null?new Sn(n):null}_adjustIndex(t,r=0){return t==null?this.length+r:t}};function jh(e){return e[as]}function xu(e){return e[as]||(e[as]=[])}function hm(e,t){let r,n=t[e.index];return je(n)?r=n:(r=Yg(n,t,null,e),t[e.index]=r,Xs(t,r)),pm(r,t,e,n),new fm(r,e,t)}function Rb(e,t){let r=e[V],n=r.createComment(""),o=$e(t,e),i=Ml(r,o);return gs(r,i,n,HI(r,o),!1),n}var pm=gm,jl=()=>!1;function Ob(e,t,r){return jl(e,t,r)}function gm(e,t,r,n){if(e[Nt])return;let o;r.type&8?o=le(n):o=Rb(t,r),e[Nt]=o}function Pb(e,t,r){var l;if(e[Nt]&&e[po])return!0;let n=r[qe],o=t.index-G;if(!n||hs(t)||qs(n,o))return!1;let s=Xu(n,o),a=(l=n.data[Dn])==null?void 0:l[o],[u,c]=ub(s,a);return e[Nt]=u,e[po]=c,!0}function Fb(e,t,r,n){jl(e,r,t)||gm(e,t,r,n)}function kb(){pm=Fb,jl=Pb}var bc=class e{constructor(t){this.queryList=t,this.matches=null}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Mc=class e{constructor(t=[]){this.queries=t}createEmbeddedView(t){let r=t.queries;if(r!==null){let n=t.contentQueries!==null?t.contentQueries[0]:r.length,o=[];for(let i=0;i<n;i++){let s=r.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let r=0;r<this.queries.length;r++)$l(t,r).matches!==null&&this.queries[r].setDirty()}},Is=class{constructor(t,r,n=null){this.flags=r,this.read=n,typeof t=="string"?this.predicate=zb(t):this.predicate=t}},Tc=class e{constructor(t=[]){this.queries=t}elementStart(t,r){for(let n=0;n<this.queries.length;n++)this.queries[n].elementStart(t,r)}elementEnd(t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementEnd(t)}embeddedTView(t){let r=null;for(let n=0;n<this.length;n++){let o=r!==null?r.length:0,i=this.getByIndex(n).embeddedTView(t,o);i&&(i.indexInDeclarationView=n,r!==null?r.push(i):r=[i])}return r!==null?new e(r):null}template(t,r){for(let n=0;n<this.queries.length;n++)this.queries[n].template(t,r)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Sc=class e{constructor(t,r=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=r}elementStart(t,r){this.isApplyingToNode(r)&&this.matchTNode(t,r)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,r){this.elementStart(t,r)}embeddedTView(t,r){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,r),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let r=this._declarationNodeIndex,n=t.parent;for(;n!==null&&n.type&8&&n.index!==r;)n=n.parent;return r===(n!==null?n.index:-1)}return this._appliesToNextNode}matchTNode(t,r){let n=this.metadata.predicate;if(Array.isArray(n))for(let o=0;o<n.length;o++){let i=n[o];this.matchTNodeWithReadOption(t,r,Lb(r,i)),this.matchTNodeWithReadOption(t,r,es(r,t,i,!1,!1))}else n===xn?r.type&4&&this.matchTNodeWithReadOption(t,r,-1):this.matchTNodeWithReadOption(t,r,es(r,t,n,!1,!1))}matchTNodeWithReadOption(t,r,n){if(n!==null){let o=this.metadata.read;if(o!==null)if(o===lt||o===nn||o===xn&&r.type&4)this.addMatch(r.index,-2);else{let i=es(r,t,o,!1,!1);i!==null&&this.addMatch(r.index,i)}else this.addMatch(r.index,n)}}addMatch(t,r){this.matches===null?this.matches=[t,r]:this.matches.push(t,r)}};function Lb(e,t){let r=e.localNames;if(r!==null){for(let n=0;n<r.length;n+=2)if(r[n]===t)return r[n+1]}return null}function jb(e,t){return e.type&11?Sr(e,t):e.type&4?ea(e,t):null}function $b(e,t,r,n){return r===-1?jb(t,e):r===-2?Vb(e,t,n):Tn(e,e[x],r,t)}function Vb(e,t,r){if(r===lt)return Sr(t,e);if(r===xn)return ea(t,e);if(r===nn)return hm(t,e)}function mm(e,t,r,n){let o=t[rt].queries[n];if(o.matches===null){let i=e.data,s=r.matches,a=[];for(let u=0;s!==null&&u<s.length;u+=2){let c=s[u];if(c<0)a.push(null);else{let l=i[c];a.push($b(t,l,s[u+1],r.metadata.read))}}o.matches=a}return o.matches}function xc(e,t,r,n){let o=e.queries.getByIndex(r),i=o.matches;if(i!==null){let s=mm(e,t,o,r);for(let a=0;a<i.length;a+=2){let u=i[a];if(u>0)n.push(s[a/2]);else{let c=i[a+1],l=t[-u];for(let d=ye;d<l.length;d++){let f=l[d];f[So]===f[de]&&xc(f[x],f,c,n)}if(l[hr]!==null){let d=l[hr];for(let f=0;f<d.length;f++){let h=d[f];xc(h[x],h,c,n)}}}}}return n}function Ub(e,t){return e[rt].queries[t].queryList}function vm(e,t,r){var i;let n=new Qu((r&4)===4);return rC(e,t,n,n.destroy),((i=t[rt])!=null?i:t[rt]=new Mc).queries.push(new bc(n))-1}function Bb(e,t,r){let n=X();return n.firstCreatePass&&(ym(n,new Is(e,t,r),-1),(t&2)===2&&(n.staticViewQueries=!0)),vm(n,T(),t)}function Hb(e,t,r,n){let o=X();if(o.firstCreatePass){let i=we();ym(o,new Is(t,r,n),i.index),qb(o,e),(r&2)===2&&(o.staticContentQueries=!0)}return vm(o,T(),r)}function zb(e){return e.split(",").map(t=>t.trim())}function ym(e,t,r){e.queries===null&&(e.queries=new Tc),e.queries.track(new Sc(t,r))}function qb(e,t){let r=e.contentQueries||(e.contentQueries=[]),n=r.length?r[r.length-1]:-1;t!==n&&r.push(e.queries.length-1,t)}function $l(e,t){return e.queries.getByIndex(t)}function Wb(e,t){let r=e[x],n=$l(r,t);return n.crossesNgTemplate?xc(r,e,t,[]):mm(r,e,n,t)}function Gb(e){return typeof e=="function"&&e[qt]!==void 0}function T1(e,t){tn("NgSignals");let r=nh(e),n=r[qt];return t!=null&&t.equal&&(n.equal=t.equal),r.set=o=>$i(n,o),r.update=o=>rh(n,o),r.asReadonly=Zb.bind(r),r}function Zb(){let e=this[qt];if(e.readonlyFn===void 0){let t=()=>this();t[qt]=e,e.readonlyFn=t}return e.readonlyFn}function Dm(e){return Gb(e)&&typeof e.set=="function"}function Yb(e){let t=[],r=new Map;function n(o){let i=r.get(o);if(!i){let s=e(o);r.set(o,i=s.then(Xb))}return i}return Cs.forEach((o,i)=>{var c,l;let s=[];o.templateUrl&&s.push(n(o.templateUrl).then(d=>{o.template=d}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&((c=o.styleUrls)!=null&&c.length))throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if((l=o.styleUrls)!=null&&l.length){let d=o.styles.length,f=o.styleUrls;o.styleUrls.forEach((h,p)=>{a.push(""),s.push(n(h).then(y=>{a[d+p]=y,f.splice(f.indexOf(h),1),f.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(n(o.styleUrl).then(d=>{a.push(d),o.styleUrl=void 0}));let u=Promise.all(s).then(()=>e0(i));t.push(u)}),Kb(),Promise.all(t).then(()=>{})}var Cs=new Map,Qb=new Set;function Kb(){let e=Cs;return Cs=new Map,e}function Jb(){return Cs.size===0}function Xb(e){return typeof e=="string"?e:e.text()}function e0(e){Qb.delete(e)}function t0(e){return Object.getPrototypeOf(e.prototype).constructor}function n0(e){let t=t0(e.type),r=!0,n=[e];for(;t;){let o;if(Yt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new D(903,!1);o=t.\u0275dir}if(o){if(r){n.push(o);let s=e;s.inputs=Zi(e.inputs),s.inputTransforms=Zi(e.inputTransforms),s.declaredInputs=Zi(e.declaredInputs),s.outputs=Zi(e.outputs);let a=o.hostBindings;a&&a0(e,a);let u=o.viewQuery,c=o.contentQueries;if(u&&i0(e,u),c&&s0(e,c),r0(e,o),ZD(e.outputs,o.outputs),Yt(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===n0&&(r=!1)}}t=Object.getPrototypeOf(t)}o0(n)}function r0(e,t){var r;for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let o=t.inputs[n];if(o!==void 0&&(e.inputs[n]=o,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let i=Array.isArray(o)?o[0]:o;if(!t.inputTransforms.hasOwnProperty(i))continue;(r=e.inputTransforms)!=null||(e.inputTransforms={}),e.inputTransforms[i]=t.inputTransforms[i]}}}function o0(e){let t=0,r=null;for(let n=e.length-1;n>=0;n--){let o=e[n];o.hostVars=t+=o.hostVars,o.hostAttrs=co(o.hostAttrs,r=co(r,o.hostAttrs))}}function Zi(e){return e===lr?{}:e===be?[]:e}function i0(e,t){let r=e.viewQuery;r?e.viewQuery=(n,o)=>{t(n,o),r(n,o)}:e.viewQuery=t}function s0(e,t){let r=e.contentQueries;r?e.contentQueries=(n,o,i)=>{t(n,o,i),r(n,o,i)}:e.contentQueries=t}function a0(e,t){let r=e.hostBindings;r?e.hostBindings=(n,o)=>{t(n,o),r(n,o)}:e.hostBindings=t}function Vl(e){let t=e.inputConfig,r={};for(let n in t)if(t.hasOwnProperty(n)){let o=t[n];Array.isArray(o)&&o[3]&&(r[n]=o[3])}e.inputTransforms=r}var Kt=class{},Io=class{};var bs=class extends Kt{constructor(t,r,n){super(),this._parent=r,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Es(this);let o=wp(t);this._bootstrapComponents=_g(o.bootstrap),this._r3Injector=og(t,r,[{provide:Kt,useValue:this},{provide:ra,useValue:this.componentFactoryResolver},...n],Me(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(r=>r()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Ms=class extends Io{constructor(t){super(),this.moduleType=t}create(t){return new bs(this.moduleType,t,[])}};function u0(e,t,r){return new bs(e,t,r)}var Ts=class extends Kt{constructor(t){super(),this.componentFactoryResolver=new Es(this),this.instance=null;let r=new lo([...t.providers,{provide:Kt,useValue:this},{provide:ra,useValue:this.componentFactoryResolver}],t.parent||ks(),t.debugName,new Set(["environment"]));this.injector=r,t.runEnvironmentInitializers&&r.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function ia(e,t,r=null){return new Ts({providers:e,parent:t,debugName:r,runEnvironmentInitializers:!0}).injector}var Ar=(()=>{let t=class t{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new pe(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function wm(e){return Ul(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function c0(e,t){if(Array.isArray(e))for(let r=0;r<e.length;r++)t(e[r]);else{let r=e[Symbol.iterator](),n;for(;!(n=r.next()).done;)t(n.value)}}function Ul(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function _r(e,t,r){return e[t]=r}function Em(e,t){return e[t]}function ke(e,t,r){let n=e[t];return Object.is(n,r)?!1:(e[t]=r,!0)}function mr(e,t,r,n){let o=ke(e,t,r);return ke(e,t+1,n)||o}function Im(e,t,r,n,o){let i=mr(e,t,r,n);return ke(e,t+2,o)||i}function sa(e,t,r,n,o,i){let s=mr(e,t,r,n);return mr(e,t+2,o,i)||s}function Ro(e){return(e.flags&32)===32}function l0(e,t,r,n,o,i,s,a,u){let c=t.consts,l=xr(t,e,4,s||null,pr(c,a));Rl(t,r,l,pr(c,u)),Bs(t,l);let d=l.tView=Nl(2,l,n,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function d0(e,t,r,n,o,i,s,a){let u=T(),c=X(),l=e+G,d=c.firstCreatePass?l0(l,c,u,t,r,n,o,i,s):c.data[l];Rn(d,!1);let f=Cm(c,u,d,e);Us()&&Ys(c,u,f,d),Qt(f,u);let h=Yg(f,u,f,d);return u[l]=h,Xs(u,h),Ob(h,d,u),js(d)&&Al(c,u,d),s!=null&&_l(u,d,a),d0}var Cm=bm;function bm(e,t,r,n){return Et(!0),t[V].createComment("")}function f0(e,t,r,n){var l,d;let o=t[qe],i=!o||br()||Ro(r)||qs(o,n);if(Et(i),i)return bm(e,t,r,n);let s=(d=(l=o.data[to])==null?void 0:l[n])!=null?d:null;s!==null&&r.tView!==null&&r.tView.ssrId===null&&(r.tView.ssrId=s);let a=ta(o,e,t,r);zs(o,n,a);let u=vl(o,n);return na(u,a)}function h0(){Cm=f0}function Bl(e,t,r,n){let o=T(),i=Mr();if(ke(o,i,t)){let s=X(),a=xo();yC(a,o,e,t,r,n)}return Bl}function Mm(e,t,r,n){return ke(e,Mr(),r)?t+oe(r)+n:fe}function p0(e,t,r,n,o,i){let s=Vs(),a=mr(e,s,r,o);return Tr(2),a?t+oe(r)+n+oe(o)+i:fe}function g0(e,t,r,n,o,i,s,a){let u=Vs(),c=Im(e,u,r,o,s);return Tr(3),c?t+oe(r)+n+oe(o)+i+oe(s)+a:fe}function m0(e,t,r,n,o,i,s,a,u,c){let l=Vs(),d=sa(e,l,r,o,s,u);return Tr(4),d?t+oe(r)+n+oe(o)+i+oe(s)+a+oe(u)+c:fe}function v0(e,t,r,n,o,i,s,a,u,c,l,d,f,h){let p=Vs(),y=sa(e,p,r,o,s,u);return y=mr(e,p+4,l,f)||y,Tr(6),y?t+oe(r)+n+oe(o)+i+oe(s)+a+oe(u)+c+oe(l)+d+oe(f)+h:fe}function Yi(e,t){return e<<17|t<<2}function _n(e){return e>>17&32767}function y0(e){return(e&2)==2}function D0(e,t){return e&131071|t<<17}function Ac(e){return e|2}function vr(e){return(e&131068)>>2}function Au(e,t){return e&-131069|t<<2}function w0(e){return(e&1)===1}function _c(e){return e|1}function E0(e,t,r,n,o,i){let s=i?t.classBindings:t.styleBindings,a=_n(s),u=vr(s);e[n]=r;let c=!1,l;if(Array.isArray(r)){let d=r;l=d[1],(l===null||To(d,l)>0)&&(c=!0)}else l=r;if(o)if(u!==0){let f=_n(e[a+1]);e[n+1]=Yi(f,a),f!==0&&(e[f+1]=Au(e[f+1],n)),e[a+1]=D0(e[a+1],n)}else e[n+1]=Yi(a,0),a!==0&&(e[a+1]=Au(e[a+1],n)),a=n;else e[n+1]=Yi(u,0),a===0?a=n:e[u+1]=Au(e[u+1],n),u=n;c&&(e[n+1]=Ac(e[n+1])),$h(e,l,n,!0),$h(e,l,n,!1),I0(t,l,e,n,i),s=Yi(a,u),i?t.classBindings=s:t.styleBindings=s}function I0(e,t,r,n,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&To(i,t)>=0&&(r[n+1]=_c(r[n+1]))}function $h(e,t,r,n){let o=e[r+1],i=t===null,s=n?_n(o):vr(o),a=!1;for(;s!==0&&(a===!1||i);){let u=e[s],c=e[s+1];C0(u,t)&&(a=!0,e[s+1]=n?_c(c):Ac(c)),s=n?_n(c):vr(c)}a&&(e[r+1]=n?Ac(o):_c(o))}function C0(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?To(e,t)>=0:!1}var tt={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function b0(e){return e.substring(tt.key,tt.keyEnd)}function M0(e){return T0(e),Tm(e,Sm(e,0,tt.textEnd))}function Tm(e,t){let r=tt.textEnd;return r===t?-1:(t=tt.keyEnd=S0(e,tt.key=t,r),Sm(e,t,r))}function T0(e){tt.key=0,tt.keyEnd=0,tt.value=0,tt.valueEnd=0,tt.textEnd=e.length}function Sm(e,t,r){for(;t<r&&e.charCodeAt(t)<=32;)t++;return t}function S0(e,t,r){for(;t<r&&e.charCodeAt(t)>32;)t++;return t}function x0(e,t,r){let n=T(),o=Mr();if(ke(n,o,t)){let i=X(),s=xo();Js(i,s,n,e,t,n[V],r,!1)}return x0}function Nc(e,t,r,n,o){let i=t.inputs,s=o?"class":"style";Ol(e,r,i[s],s,n)}function xm(e,t,r){return Am(e,t,r,!1),xm}function A0(e,t){return Am(e,t,null,!0),A0}function S1(e){N0(L0,_0,e,!0)}function _0(e,t){for(let r=M0(t);r>=0;r=Tm(t,r))Wc(e,b0(t),!0)}function Am(e,t,r,n){let o=T(),i=X(),s=Tr(2);if(i.firstUpdatePass&&Nm(i,e,s,n),t!==fe&&ke(o,s,t)){let a=i.data[ut()];Rm(i,a,o,o[V],e,o[s+1]=$0(t,r),n,s)}}function N0(e,t,r,n){let o=X(),i=Tr(2);o.firstUpdatePass&&Nm(o,null,i,n);let s=T();if(r!==fe&&ke(s,i,r)){let a=o.data[ut()];if(Om(a,n)&&!_m(o,i)){let u=n?a.classesWithoutHost:a.stylesWithoutHost;u!==null&&(r=Pu(u,r||"")),Nc(o,a,s,r,n)}else j0(o,a,s,s[V],s[i+1],s[i+1]=k0(e,t,r),n,i)}}function _m(e,t){return t>=e.expandoStartIndex}function Nm(e,t,r,n){let o=e.data;if(o[r+1]===null){let i=o[ut()],s=_m(e,r);Om(i,n)&&t===null&&!s&&(t=!1),t=R0(o,i,t,n),E0(o,i,t,r,s,n)}}function R0(e,t,r,n){let o=DE(e),i=n?t.residualClasses:t.residualStyles;if(o===null)(n?t.classBindings:t.styleBindings)===0&&(r=_u(null,e,t,r,n),r=Co(r,t.attrs,n),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(r=_u(o,e,t,r,n),i===null){let u=O0(e,t,n);u!==void 0&&Array.isArray(u)&&(u=_u(null,e,t,u[1],n),u=Co(u,t.attrs,n),P0(e,t,n,u))}else i=F0(e,t,n)}return i!==void 0&&(n?t.residualClasses=i:t.residualStyles=i),r}function O0(e,t,r){let n=r?t.classBindings:t.styleBindings;if(vr(n)!==0)return e[_n(n)]}function P0(e,t,r,n){let o=r?t.classBindings:t.styleBindings;e[_n(o)]=n}function F0(e,t,r){let n,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;n=Co(n,s,r)}return Co(n,t.attrs,r)}function _u(e,t,r,n,o){let i=null,s=r.directiveEnd,a=r.directiveStylingLast;for(a===-1?a=r.directiveStart:a++;a<s&&(i=t[a],n=Co(n,i.hostAttrs,o),i!==e);)a++;return e!==null&&(r.directiveStylingLast=a),n}function Co(e,t,r){let n=r?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===n&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Wc(e,s,r?!0:t[++i]))}return e===void 0?null:e}function k0(e,t,r){if(r==null||r==="")return be;let n=[],o=Ge(r);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(n,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(n,i,o[i]);else typeof o=="string"&&t(n,o);return n}function L0(e,t,r){let n=String(t);n!==""&&!n.includes(" ")&&Wc(e,n,r)}function j0(e,t,r,n,o,i,s,a){o===fe&&(o=be);let u=0,c=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let f=u<o.length?o[u+1]:void 0,h=c<i.length?i[c+1]:void 0,p=null,y;l===d?(u+=2,c+=2,f!==h&&(p=d,y=h)):d===null||l!==null&&l<d?(u+=2,p=l):(c+=2,p=d,y=h),p!==null&&Rm(e,t,r,n,p,y,s,a),l=u<o.length?o[u]:null,d=c<i.length?i[c]:null}}function Rm(e,t,r,n,o,i,s,a){if(!(t.type&3))return;let u=e.data,c=u[a+1],l=w0(c)?Vh(u,t,r,o,vr(c),s):void 0;if(!Ss(l)){Ss(i)||y0(c)&&(i=Vh(u,null,r,o,a,s));let d=Fp(ut(),r);ZI(n,s,d,o,i)}}function Vh(e,t,r,n,o,i){let s=t===null,a;for(;o>0;){let u=e[o],c=Array.isArray(u),l=c?u[1]:u,d=l===null,f=r[o+1];f===fe&&(f=d?be:void 0);let h=d?vu(f,n):l===n?f:void 0;if(c&&!Ss(h)&&(h=vu(u,n)),Ss(h)&&(a=h,s))return a;let p=e[o+1];o=s?_n(p):vr(p)}if(t!==null){let u=i?t.residualClasses:t.residualStyles;u!=null&&(a=vu(u,n))}return a}function Ss(e){return e!==void 0}function $0(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Me(Ge(e)))),e}function Om(e,t){return(e.flags&(t?8:16))!==0}function x1(e,t,r){tn("NgControlFlow");let n=T(),o=Mr(),i=V0(n,G+e),s=0;if(ke(n,o,t)){let a=j(null);try{if(TC(i,s),t!==-1){let u=U0(n[x],G+t),c=gc(i,u.tView.ssrId),l=em(n,u,r,{dehydratedView:c});tm(i,l,s,dc(u,c))}}finally{j(a)}}else{let a=MC(i,s);a!==void 0&&(a[Fe]=r)}}function V0(e,t){return e[t]}function U0(e,t){return Xc(e,t)}function B0(e,t,r,n,o,i){let s=t.consts,a=pr(s,o),u=xr(t,e,2,n,a);return Rl(t,r,u,pr(s,i)),u.attrs!==null&&ws(u,u.attrs,!1),u.mergedAttrs!==null&&ws(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function Pm(e,t,r,n){let o=T(),i=X(),s=G+e,a=o[V],u=i.firstCreatePass?B0(s,i,o,t,r,n):i.data[s],c=km(i,o,u,a,t,e);o[s]=c;let l=js(u);return Rn(u,!0),$g(a,c,u),!Ro(u)&&Us()&&Ys(i,o,c,u),aE()===0&&Qt(c,o),uE(),l&&(Al(i,o,u),xl(i,u,o)),n!==null&&_l(o,u),Pm}function Fm(){let e=we();nl()?rl():(e=e.parent,Rn(e,!1));let t=e;lE(t)&&fE(),cE();let r=X();return r.firstCreatePass&&(Bs(r,e),Kc(e)&&r.queries.elementEnd(e)),t.classesWithoutHost!=null&&SE(t)&&Nc(r,t,T(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&xE(t)&&Nc(r,t,T(),t.stylesWithoutHost,!1),Fm}function Hl(e,t,r,n){return Pm(e,t,r,n),Fm(),Hl}var km=(e,t,r,n,o,i)=>(Et(!0),Zs(n,o,Wp()));function H0(e,t,r,n,o,i){let s=t[qe],a=!s||br()||Ro(r)||qs(s,i);if(Et(a),a)return Zs(n,o,Wp());let u=ta(s,e,t,r);return gg(s,i)&&zs(s,i,u.nextSibling),s&&(sg(r)||ag(u))&&Ir(r)&&(dE(r),kg(u)),u}function z0(){km=H0}function q0(e,t,r,n,o){let i=t.consts,s=pr(i,n),a=xr(t,e,8,"ng-container",s);s!==null&&ws(a,s,!0);let u=pr(i,o);return Rl(t,r,a,u),t.queries!==null&&t.queries.elementStart(t,a),a}function W0(e,t,r){let n=T(),o=X(),i=e+G,s=o.firstCreatePass?q0(i,o,n,t,r):o.data[i];Rn(s,!0);let a=Lm(o,n,s,e);return n[i]=a,Us()&&Ys(o,n,a,s),Qt(a,n),js(s)&&(Al(o,n,s),xl(o,s,n)),r!=null&&_l(n,s),W0}function G0(){let e=we(),t=X();return nl()?rl():(e=e.parent,Rn(e,!1)),t.firstCreatePass&&(Bs(t,e),Kc(e)&&t.queries.elementEnd(e)),G0}var Lm=(e,t,r,n)=>(Et(!0),Il(t[V],""));function Z0(e,t,r,n){let o,i=t[qe],s=!i||br()||Ro(r);if(Et(s),s)return Il(t[V],"");let a=ta(i,e,t,r),u=oI(i,n);return zs(i,n,a),o=na(u,a),o}function Y0(){Lm=Z0}function A1(){return T()}function Q0(e,t,r){let n=T(),o=Mr();if(ke(n,o,t)){let i=X(),s=xo();Js(i,s,n,e,t,n[V],r,!0)}return Q0}var yn=void 0;function K0(e){let t=e,r=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return r===1&&n===0?1:5}var J0=["en",[["a","p"],["AM","PM"],yn],[["AM","PM"],yn,yn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],yn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],yn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",yn,"{1} 'at' {0}",yn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",K0],Nu={};function Re(e){let t=X0(e),r=Uh(t);if(r)return r;let n=t.split("-")[0];if(r=Uh(n),r)return r;if(n==="en")return J0;throw new D(701,!1)}function Uh(e){return e in Nu||(Nu[e]=_e.ng&&_e.ng.common&&_e.ng.common.locales&&_e.ng.common.locales[e]),Nu[e]}var ee=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ee||{});function X0(e){return e.toLowerCase().replace(/_/g,"-")}var yr="en-US",eM="USD";var tM=yr;function jm(e){typeof e=="string"&&(tM=e.toLowerCase().replace(/_/g,"-"))}function $m(e,t,r){let n=e[V];switch(r){case Node.COMMENT_NODE:return Il(n,t);case Node.TEXT_NODE:return El(n,t);case Node.ELEMENT_NODE:return Zs(n,t,null)}}var nM=(e,t,r,n)=>(Et(!0),$m(e,r,n));function rM(e,t,r,n){return Et(!0),$m(e,r,n)}function oM(){nM=rM}function zl(e,t,r,n){let o=T(),i=X(),s=we();return Vm(i,o,o[V],s,e,t,n),zl}function iM(e,t,r,n){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===r&&o[i+1]===n){let a=t[fo],u=o[i+2];return a.length>u?a[u]:null}typeof s=="string"&&(i+=2)}return null}function Vm(e,t,r,n,o,i,s){let a=js(n),c=e.firstCreatePass&&Jg(e),l=t[Fe],d=Kg(t),f=!0;if(n.type&3||s){let y=$e(n,t),m=s?s(y):y,v=d.length,A=s?O=>s(le(O[n.index])):n.index,q=null;if(!s&&a&&(q=iM(e,t,o,n.index)),q!==null){let O=q.__ngLastListenerFn__||q;O.__ngNextListenerFn__=i,q.__ngLastListenerFn__=i,f=!1}else{i=Hh(n,t,l,i,!1);let O=r.listen(m,o,i);d.push(i,O),c&&c.push(o,A,v,v+1)}}else i=Hh(n,t,l,i,!1);let h=n.outputs,p;if(f&&h!==null&&(p=h[o])){let y=p.length;if(y)for(let m=0;m<y;m+=2){let v=p[m],A=p[m+1],re=t[v][A].subscribe(i),Oe=d.length;d.push(i,re),c&&c.push(o,n.index,Oe,-(Oe+1))}}}function Bh(e,t,r,n){let o=j(null);try{return mt(6,t,r),r(n)!==!1}catch(i){return Xg(e,i),!1}finally{mt(7,t,r),j(o)}}function Hh(e,t,r,n,o){return function i(s){if(s===Function)return n;let a=e.componentOffset>-1?Xt(e.index,t):t;Fl(a);let u=Bh(t,r,n,s),c=i.__ngNextListenerFn__;for(;c;)u=Bh(t,r,c,s)&&u,c=c.__ngNextListenerFn__;return o&&u===!1&&s.preventDefault(),u}}function _1(e=1){return EE(e)}function sM(e,t){let r=null,n=bw(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){r=o;continue}if(n===null?gp(e,i,!0):Sw(n,i))return o}return r}function N1(e){let t=T()[Ne][Le];if(!t.projection){let r=e?e.length:1,n=t.projection=gw(r,null),o=n.slice(),i=t.child;for(;i!==null;){let s=e?sM(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:n[s]=i,o[s]=i),i=i.next}}}function R1(e,t=0,r){let n=T(),o=X(),i=xr(o,G+e,16,null,r||null);i.projection===null&&(i.projection=t),rl(),(!n[qe]||br())&&(i.flags&32)!==32&&WI(o,n,i)}function aM(e,t,r){return Um(e,"",t,"",r),aM}function Um(e,t,r,n,o){let i=T(),s=Mm(i,t,r,n);if(s!==fe){let a=X(),u=xo();Js(a,u,i,e,s,i[V],o,!1)}return Um}function Bm(e,t,r,n){Hb(e,t,r,n)}function O1(e,t,r){Bb(e,t,r)}function Hm(e){let t=T(),r=X(),n=Vp();ol(n+1);let o=$l(r,n);if(e.dirty&&nE(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Wb(t,n);e.reset(i,BE),e.notifyOnChanges()}return!0}return!1}function zm(){return Ub(T(),Vp())}function uM(e,t,r,n){r>=e.data.length&&(e.data[r]=null,e.blueprint[r]=null),t[r]=n}function P1(e){let t=pE();return $s(t,G+e)}function F1(e,t=""){let r=T(),n=X(),o=e+G,i=n.firstCreatePass?xr(n,o,1,t,null):n.data[o],s=qm(n,r,i,t,e);r[o]=s,Us()&&Ys(n,r,s,i),Rn(i,!1)}var qm=(e,t,r,n,o)=>(Et(!0),El(t[V],n));function cM(e,t,r,n,o){let i=t[qe],s=!i||br()||Ro(r)||qs(i,o);return Et(s),s?El(t[V],n):ta(i,e,t,r)}function lM(){qm=cM}function dM(e){return Wm("",e,""),dM}function Wm(e,t,r){let n=T(),o=Mm(n,e,t,r);return o!==fe&&No(n,ut(),o),Wm}function fM(e,t,r,n,o){let i=T(),s=p0(i,e,t,r,n,o);return s!==fe&&No(i,ut(),s),fM}function hM(e,t,r,n,o,i,s){let a=T(),u=g0(a,e,t,r,n,o,i,s);return u!==fe&&No(a,ut(),u),hM}function pM(e,t,r,n,o,i,s,a,u){let c=T(),l=m0(c,e,t,r,n,o,i,s,a,u);return l!==fe&&No(c,ut(),l),pM}function gM(e,t,r,n,o,i,s,a,u,c,l,d,f){let h=T(),p=v0(h,e,t,r,n,o,i,s,a,u,c,l,d,f);return p!==fe&&No(h,ut(),p),gM}function mM(e,t,r){Dm(t)&&(t=t());let n=T(),o=Mr();if(ke(n,o,t)){let i=X(),s=xo();Js(i,s,n,e,t,n[V],r,!1)}return mM}function k1(e,t){let r=Dm(e);return r&&e.set(t),r}function vM(e,t){let r=T(),n=X(),o=we();return Vm(n,r,r[V],o,e,t),vM}function yM(e,t,r){let n=X();if(n.firstCreatePass){let o=Yt(e);Rc(r,n.data,n.blueprint,o,!0),Rc(t,n.data,n.blueprint,o,!1)}}function Rc(e,t,r,n,o){if(e=Ce(e),Array.isArray(e))for(let i=0;i<e.length;i++)Rc(e[i],t,r,n,o);else{let i=X(),s=T(),a=we(),u=dr(e)?e:Ce(e.provide),c=Tp(e),l=a.providerIndexes&1048575,d=a.directiveStart,f=a.providerIndexes>>20;if(dr(e)||!e.multi){let h=new Mn(c,o,$),p=Ou(u,t,o?l:l+f,d);p===-1?(Wu(ds(a,s),i,u),Ru(i,e,t.length),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),r.push(h),s.push(h)):(r[p]=h,s[p]=h)}else{let h=Ou(u,t,l+f,d),p=Ou(u,t,l,l+f),y=h>=0&&r[h],m=p>=0&&r[p];if(o&&!m||!o&&!y){Wu(ds(a,s),i,u);let v=EM(o?wM:DM,r.length,o,n,c);!o&&m&&(r[p].providerFactory=v),Ru(i,e,t.length,0),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),r.push(v),s.push(v)}else{let v=Gm(r[o?p:h],c,!o&&n);Ru(i,e,h>-1?h:p,v)}!o&&n&&m&&r[p].componentProviders++}}}function Ru(e,t,r,n){let o=dr(t),i=jw(t);if(o||i){let u=(i?Ce(t.useClass):t).prototype.ngOnDestroy;if(u){let c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=c.indexOf(r);l===-1?c.push(r,[n,u]):c[l+1].push(n,u)}else c.push(r,u)}}}function Gm(e,t,r){return r&&e.componentProviders++,e.multi.push(t)-1}function Ou(e,t,r,n){for(let o=r;o<n;o++)if(t[o]===e)return o;return-1}function DM(e,t,r,n){return Oc(this.multi,[])}function wM(e,t,r,n){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Tn(r,r[x],this.providerFactory.index,n);i=a.slice(0,s),Oc(o,i);for(let u=s;u<a.length;u++)i.push(a[u])}else i=[],Oc(o,i);return i}function Oc(e,t){for(let r=0;r<e.length;r++){let n=e[r];t.push(n())}return t}function EM(e,t,r,n,o){let i=new Mn(e,r,$);return i.multi=[],i.index=t,i.componentProviders=0,Gm(i,o,n&&!r),i}function L1(e,t=[]){return r=>{r.providersResolver=(n,o)=>yM(n,o?o(e):e,t)}}var IM=(()=>{let t=class t{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let o=Cp(!1,n.type),i=o.length>0?ia([o],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}};t.\u0275prov=E({token:t,providedIn:"environment",factory:()=>new t(I(De))});let e=t;return e})();function Zm(e){tn("NgStandalone"),e.getStandaloneInjector=t=>t.get(IM).getOrCreateStandaloneInjector(e)}function j1(e,t,r){let n=Pt()+e,o=T();return o[n]===fe?_r(o,n,r?t.call(r):t()):Em(o,n)}function $1(e,t,r,n){return Ym(T(),Pt(),e,t,r,n)}function V1(e,t,r,n,o){return Qm(T(),Pt(),e,t,r,n,o)}function U1(e,t,r,n,o,i){return Km(T(),Pt(),e,t,r,n,o,i)}function B1(e,t,r,n,o,i,s){return CM(T(),Pt(),e,t,r,n,o,i,s)}function H1(e,t,r,n,o,i,s,a){let u=Pt()+e,c=T(),l=sa(c,u,r,n,o,i);return ke(c,u+4,s)||l?_r(c,u+5,a?t.call(a,r,n,o,i,s):t(r,n,o,i,s)):Em(c,u+5)}function aa(e,t){let r=e[t];return r===fe?void 0:r}function Ym(e,t,r,n,o,i){let s=t+r;return ke(e,s,o)?_r(e,s+1,i?n.call(i,o):n(o)):aa(e,s+1)}function Qm(e,t,r,n,o,i,s){let a=t+r;return mr(e,a,o,i)?_r(e,a+2,s?n.call(s,o,i):n(o,i)):aa(e,a+2)}function Km(e,t,r,n,o,i,s,a){let u=t+r;return Im(e,u,o,i,s)?_r(e,u+3,a?n.call(a,o,i,s):n(o,i,s)):aa(e,u+3)}function CM(e,t,r,n,o,i,s,a,u){let c=t+r;return sa(e,c,o,i,s,a)?_r(e,c+4,u?n.call(u,o,i,s,a):n(o,i,s,a)):aa(e,c+4)}function z1(e,t){var u;let r=X(),n,o=e+G;r.firstCreatePass?(n=bM(t,r.pipeRegistry),r.data[o]=n,n.onDestroy&&((u=r.destroyHooks)!=null?u:r.destroyHooks=[]).push(o,n.onDestroy)):n=r.data[o];let i=n.factory||(n.factory=En(n.type,!0)),s,a=Ae($);try{let c=ls(!1),l=i();return ls(c),uM(r,T(),o,l),l}finally{Ae(a)}}function bM(e,t){if(t)for(let r=t.length-1;r>=0;r--){let n=t[r];if(e===n.name)return n}}function q1(e,t,r){let n=e+G,o=T(),i=$s(o,n);return ql(o,n)?Ym(o,Pt(),t,i.transform,r,i):i.transform(r)}function W1(e,t,r,n){let o=e+G,i=T(),s=$s(i,o);return ql(i,o)?Qm(i,Pt(),t,s.transform,r,n,s):s.transform(r,n)}function G1(e,t,r,n,o){let i=e+G,s=T(),a=$s(s,i);return ql(s,i)?Km(s,Pt(),t,a.transform,r,n,o,a):a.transform(r,n,o)}function ql(e,t){return e[x].data[t].pure}function Z1(e,t){return ea(e,t)}var Qi=null;function MM(e){Qi!==null&&(e.defaultEncapsulation!==Qi.defaultEncapsulation||e.preserveWhitespaces!==Qi.preserveWhitespaces)||(Qi=e)}var ua=(()=>{let t=class t{log(n){console.log(n)}warn(n){console.warn(n)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"platform"});let e=t;return e})();var Jm=new C(""),Xm=new C(""),TM=(()=>{let t=class t{constructor(n,o,i){this._ngZone=n,this.registry=o,this._pendingCount=0,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,ao||(SM(i),i.addToWindow(o)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{W.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&this._pendingCount===0&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(o=>o.updateCb&&o.updateCb(n)?(clearTimeout(o.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,o,i){let s=-1;o&&o>0&&(s=setTimeout(()=>{this._callbacks=this._callbacks.filter(a=>a.timeoutId!==s),n()},o)),this._callbacks.push({doneCb:n,timeoutId:s,updateCb:i})}whenStable(n,o,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,o,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,o,i){return[]}};t.\u0275fac=function(o){return new(o||t)(I(W),I(ev),I(Xm))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),ev=(()=>{let t=class t{constructor(){this._applications=new Map}registerApplication(n,o){this._applications.set(n,o)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,o=!0){var i;return(i=ao==null?void 0:ao.findTestabilityInTree(this,n,o))!=null?i:null}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"platform"});let e=t;return e})();function SM(e){ao=e}var ao;function Nr(e){return!!e&&typeof e.then=="function"}function Wl(e){return!!e&&typeof e.subscribe=="function"}var ca=new C(""),Gl=(()=>{let t=class t{constructor(){var n;this.initialized=!1,this.done=!1,this.donePromise=new Promise((o,i)=>{this.resolve=o,this.reject=i}),this.appInits=(n=g(ca,{optional:!0}))!=null?n:[]}runInitializers(){if(this.initialized)return;let n=[];for(let i of this.appInits){let s=i();if(Nr(s))n.push(s);else if(Wl(s)){let a=new Promise((u,c)=>{s.subscribe({complete:u,error:c})});n.push(a)}}let o=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{o()}).catch(i=>{this.reject(i)}),n.length===0&&o(),this.initialized=!0}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Pn=new C("");function tv(){th(()=>{throw new D(600,!1)})}function xM(e){return e.isBoundToModule}function nv(e,t,r){try{let n=r();return Nr(n)?n.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):n}catch(n){throw t.runOutsideAngular(()=>e.handleError(n)),n}}function rv(e,t){return Array.isArray(t)?t.reduce(rv,e):w(w({},e),t)}var Lt=(()=>{let t=class t{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=g(ig),this.afterRenderEffectManager=g(Ll),this.externalTestViews=new Set,this.beforeRender=new se,this.afterTick=new se,this.componentTypes=[],this.components=[],this.isStable=g(Ar).hasPendingTasks.pipe(F(n=>!n)),this._injector=g(De)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,o){let i=n instanceof Ds;if(!this._injector.get(Gl).done){let h=!i&&Dp(n),p=!1;throw new D(405,p)}let a;i?a=n:a=this._injector.get(ra).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let u=xM(a)?void 0:this._injector.get(Kt),c=o||a.selector,l=a.create(ct.NULL,[],c,u),d=l.location.nativeElement,f=l.injector.get(Jm,null);return f==null||f.registerApplication(d),l.onDestroy(()=>{this.detachView(l.hostView),ns(this.components,l),f==null||f.unregisterApplication(d)}),this._loadComponent(l),l}tick(){this._tick(!0)}_tick(n){if(this._runningTick)throw new D(101,!1);let o=j(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(n)}catch(i){this.internalErrorHandler(i)}finally{this.afterTick.next(),this._runningTick=!1,j(o)}}detectChangesInAttachedViews(n){let o=0,i=this.afterRenderEffectManager;for(;;){if(o===om)throw new D(103,!1);if(n){let s=o===0;this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:u}of this._views)AM(a,s,u)}if(o++,i.executeInternalCallbacks(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>Pc(s))&&(i.execute(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>Pc(s))))break}}attachView(n){let o=n;this._views.push(o),o.attachToAppRef(this)}detachView(n){let o=n;ns(this._views,o),o.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let o=this._injector.get(Pn,[]);[...this._bootstrapListeners,...o].forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>ns(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new D(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function ns(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}var Wt;function Zl(e){Wt!=null||(Wt=new WeakMap);let t=Wt.get(e);if(t)return t;let r=e.isStable.pipe(Xe(n=>n)).toPromise().then(()=>{});return Wt.set(e,r),e.onDestroy(()=>Wt==null?void 0:Wt.delete(e)),r}function AM(e,t,r){!t&&!Pc(e)||_M(e,r,t)}function Pc(e){return tl(e)}function _M(e,t,r){let n;r?(n=0,e[S]|=1024):e[S]&64?n=0:n=1,im(e,t,n)}var Fc=class{constructor(t,r){this.ngModuleFactory=t,this.componentFactories=r}},la=(()=>{let t=class t{compileModuleSync(n){return new Ms(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let o=this.compileModuleSync(n),i=wp(n),s=_g(i.declarations).reduce((a,u)=>{let c=Dt(u);return c&&a.push(new An(c)),a},[]);return new Fc(o,s)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),NM=new C("");function RM(e,t,r){let n=new Ms(r);return Promise.resolve(n)}function zh(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var OM=(()=>{let t=class t{constructor(){this.zone=g(W),this.applicationRef=g(Lt)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){var n;(n=this._onMicrotaskEmptySubscription)==null||n.unsubscribe()}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function ov(e){return[{provide:W,useFactory:e},{provide:In,multi:!0,useFactory:()=>{let t=g(OM,{optional:!0});return()=>t.initialize()}},{provide:In,multi:!0,useFactory:()=>{let t=g(kM);return()=>{t.initialize()}}},{provide:ig,useFactory:PM}]}function PM(){let e=g(W),t=g(st);return r=>e.runOutsideAngular(()=>t.handleError(r))}function FM(e){let t=ov(()=>new W(iv(e)));return Jt([[],t])}function iv(e){var t,r;return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:(t=e==null?void 0:e.eventCoalescing)!=null?t:!1,shouldCoalesceRunChangeDetection:(r=e==null?void 0:e.runCoalescing)!=null?r:!1}}var kM=(()=>{let t=class t{constructor(){this.subscription=new J,this.initialized=!1,this.zone=g(W),this.pendingTasks=g(Ar)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{W.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{W.assertInAngularZone(),n!=null||(n=this.pendingTasks.add())}))}ngOnDestroy(){this.subscription.unsubscribe()}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function LM(){return typeof $localize<"u"&&$localize.locale||yr}var Rr=new C("",{providedIn:"root",factory:()=>g(Rr,L.Optional|L.SkipSelf)||LM()}),sv=new C("",{providedIn:"root",factory:()=>eM});var Yl=new C(""),av=(()=>{let t=class t{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,o){let i=wb(o==null?void 0:o.ngZone,iv({eventCoalescing:o==null?void 0:o.ngZoneEventCoalescing,runCoalescing:o==null?void 0:o.ngZoneRunCoalescing}));return i.run(()=>{let s=u0(n.moduleType,this.injector,ov(()=>i)),a=s.injector.get(st,null);return i.runOutsideAngular(()=>{let u=i.onError.subscribe({next:c=>{a.handleError(c)}});s.onDestroy(()=>{ns(this._modules,s),u.unsubscribe()})}),nv(a,i,()=>{let u=s.injector.get(Gl);return u.runInitializers(),u.donePromise.then(()=>{let c=s.injector.get(Rr,yr);return jm(c||yr),this._moduleDoBootstrap(s),s})})})}bootstrapModule(n,o=[]){let i=rv({},o);return RM(this.injector,i,n).then(s=>this.bootstrapModuleFactory(s,i))}_moduleDoBootstrap(n){let o=n.injector.get(Lt);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(i=>o.bootstrap(i));else if(n.instance.ngDoBootstrap)n.instance.ngDoBootstrap(o);else throw new D(-403,!1);this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new D(404,!1);this._modules.slice().forEach(o=>o.destroy()),this._destroyListeners.forEach(o=>o());let n=this._injector.get(Yl,null);n&&(n.forEach(o=>o()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}};t.\u0275fac=function(o){return new(o||t)(I(ct))},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"platform"});let e=t;return e})(),yt=null,uv=new C("");function jM(e){if(yt&&!yt.get(uv,!1))throw new D(400,!1);tv(),yt=e;let t=e.get(av);return fv(e),t}function cv(e,t,r=[]){let n=`Platform: ${t}`,o=new C(n);return(i=[])=>{let s=dv();if(!s||s.injector.get(uv,!1)){let a=[...r,...i,{provide:o,useValue:!0}];e?e(a):jM(lv(a,n))}return $M(o)}}function lv(e=[],t){return ct.create({name:t,providers:[{provide:Fs,useValue:"platform"},{provide:Yl,useValue:new Set([()=>yt=null])},...e]})}function $M(e){let t=dv();if(!t)throw new D(401,!1);return t}function dv(){var e;return(e=yt==null?void 0:yt.get(av))!=null?e:null}function VM(e=[]){if(yt)return yt;let t=lv(e);return yt=t,tv(),fv(t),t}function fv(e){let t=e.get(dl,null);t==null||t.forEach(r=>r())}var Fn=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=UM;let e=t;return e})();function UM(e){return BM(we(),T(),(e&16)===16)}function BM(e,t,r){if(Ir(e)&&!r){let n=Xt(e.index,t);return new Sn(n,n)}else if(e.type&47){let n=t[Ne];return new Sn(n,t)}return null}var kc=class{constructor(){}supports(t){return wm(t)}create(t){return new Lc(t)}},HM=(e,t)=>t,Lc=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||HM}forEachItem(t){let r;for(r=this._itHead;r!==null;r=r._next)t(r)}forEachOperation(t){let r=this._itHead,n=this._removalsHead,o=0,i=null;for(;r||n;){let s=!n||r&&r.currentIndex<qh(n,o,i)?r:n,a=qh(s,o,i),u=s.currentIndex;if(s===n)o--,n=n._nextRemoved;else if(r=r._next,s.previousIndex==null)o++;else{i||(i=[]);let c=a-o,l=u-o;if(c!=l){for(let f=0;f<c;f++){let h=f<i.length?i[f]:i[f]=0,p=h+f;l<=p&&p<c&&(i[f]=h+1)}let d=s.previousIndex;i[d]=l-c}}a!==u&&t(s,a,u)}}forEachPreviousItem(t){let r;for(r=this._previousItHead;r!==null;r=r._nextPrevious)t(r)}forEachAddedItem(t){let r;for(r=this._additionsHead;r!==null;r=r._nextAdded)t(r)}forEachMovedItem(t){let r;for(r=this._movesHead;r!==null;r=r._nextMoved)t(r)}forEachRemovedItem(t){let r;for(r=this._removalsHead;r!==null;r=r._nextRemoved)t(r)}forEachIdentityChange(t){let r;for(r=this._identityChangesHead;r!==null;r=r._nextIdentityChange)t(r)}diff(t){if(t==null&&(t=[]),!wm(t))throw new D(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let r=this._itHead,n=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),r===null||!Object.is(r.trackById,s)?(r=this._mismatch(r,i,s,a),n=!0):(n&&(r=this._verifyReinsertion(r,i,s,a)),Object.is(r.item,i)||this._addIdentityChange(r,i)),r=r._next}else o=0,c0(t,a=>{s=this._trackByFn(o,a),r===null||!Object.is(r.trackById,s)?(r=this._mismatch(r,a,s,o),n=!0):(n&&(r=this._verifyReinsertion(r,a,s,o)),Object.is(r.item,a)||this._addIdentityChange(r,a)),r=r._next,o++}),this.length=o;return this._truncate(r),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,r,n,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(n,null),t!==null?(Object.is(t.item,r)||this._addIdentityChange(t,r),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(n,o),t!==null?(Object.is(t.item,r)||this._addIdentityChange(t,r),this._moveAfter(t,i,o)):t=this._addAfter(new jc(r,n),i,o)),t}_verifyReinsertion(t,r,n,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(n,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let r=t._next;this._addToRemovals(this._unlink(t)),t=r}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,r,n){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,r,n),this._addToMoves(t,n),t}_moveAfter(t,r,n){return this._unlink(t),this._insertAfter(t,r,n),this._addToMoves(t,n),t}_addAfter(t,r,n){return this._insertAfter(t,r,n),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,r,n){let o=r===null?this._itHead:r._next;return t._next=o,t._prev=r,o===null?this._itTail=t:o._prev=t,r===null?this._itHead=t:r._next=t,this._linkedRecords===null&&(this._linkedRecords=new xs),this._linkedRecords.put(t),t.currentIndex=n,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let r=t._prev,n=t._next;return r===null?this._itHead=n:r._next=n,n===null?this._itTail=r:n._prev=r,t}_addToMoves(t,r){return t.previousIndex===r||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new xs),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,r){return t.item=r,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},jc=class{constructor(t,r){this.item=t,this.trackById=r,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},$c=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,r){let n;for(n=this._head;n!==null;n=n._nextDup)if((r===null||r<=n.currentIndex)&&Object.is(n.trackById,t))return n;return null}remove(t){let r=t._prevDup,n=t._nextDup;return r===null?this._head=n:r._nextDup=n,n===null?this._tail=r:n._prevDup=r,this._head===null}},xs=class{constructor(){this.map=new Map}put(t){let r=t.trackById,n=this.map.get(r);n||(n=new $c,this.map.set(r,n)),n.add(t)}get(t,r){let n=t,o=this.map.get(n);return o?o.get(t,r):null}remove(t){let r=t.trackById;return this.map.get(r).remove(t)&&this.map.delete(r),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function qh(e,t,r){let n=e.previousIndex;if(n===null)return n;let o=0;return r&&n<r.length&&(o=r[n]),n+t+o}var Vc=class{constructor(){}supports(t){return t instanceof Map||Ul(t)}create(){return new Uc}},Uc=class{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(t){let r;for(r=this._mapHead;r!==null;r=r._next)t(r)}forEachPreviousItem(t){let r;for(r=this._previousMapHead;r!==null;r=r._nextPrevious)t(r)}forEachChangedItem(t){let r;for(r=this._changesHead;r!==null;r=r._nextChanged)t(r)}forEachAddedItem(t){let r;for(r=this._additionsHead;r!==null;r=r._nextAdded)t(r)}forEachRemovedItem(t){let r;for(r=this._removalsHead;r!==null;r=r._nextRemoved)t(r)}diff(t){if(!t)t=new Map;else if(!(t instanceof Map||Ul(t)))throw new D(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let r=this._mapHead;if(this._appendAfter=null,this._forEach(t,(n,o)=>{if(r&&r.key===o)this._maybeAddToChanges(r,n),this._appendAfter=r,r=r._next;else{let i=this._getOrCreateRecordForKey(o,n);r=this._insertBeforeOrAppend(r,i)}}),r){r._prev&&(r._prev._next=null),this._removalsHead=r;for(let n=r;n!==null;n=n._nextRemoved)n===this._mapHead&&(this._mapHead=null),this._records.delete(n.key),n._nextRemoved=n._next,n.previousValue=n.currentValue,n.currentValue=null,n._prev=null,n._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,r){if(t){let n=t._prev;return r._next=t,r._prev=n,t._prev=r,n&&(n._next=r),t===this._mapHead&&(this._mapHead=r),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=r,r._prev=this._appendAfter):this._mapHead=r,this._appendAfter=r,null}_getOrCreateRecordForKey(t,r){if(this._records.has(t)){let o=this._records.get(t);this._maybeAddToChanges(o,r);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let n=new Bc(t);return this._records.set(t,n),n.currentValue=r,this._addToAdditions(n),n}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;t!==null;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;t!=null;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,r){Object.is(r,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=r,this._addToChanges(t))}_addToAdditions(t){this._additionsHead===null?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){this._changesHead===null?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,r){t instanceof Map?t.forEach(r):Object.keys(t).forEach(n=>r(t[n],n))}},Bc=class{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}};function Wh(){return new Ql([new kc])}var Ql=(()=>{let t=class t{constructor(n){this.factories=n}static create(n,o){if(o!=null){let i=o.factories.slice();n=n.concat(i)}return new t(n)}static extend(n){return{provide:t,useFactory:o=>t.create(n,o||Wh()),deps:[[t,new Ps,new Mo]]}}find(n){let o=this.factories.find(i=>i.supports(n));if(o!=null)return o;throw new D(901,!1)}};t.\u0275prov=E({token:t,providedIn:"root",factory:Wh});let e=t;return e})();function Gh(){return new Kl([new Vc])}var Kl=(()=>{let t=class t{constructor(n){this.factories=n}static create(n,o){if(o){let i=o.factories.slice();n=n.concat(i)}return new t(n)}static extend(n){return{provide:t,useFactory:o=>t.create(n,o||Gh()),deps:[[t,new Ps,new Mo]]}}find(n){let o=this.factories.find(i=>i.supports(n));if(o)return o;throw new D(901,!1)}};t.\u0275prov=E({token:t,providedIn:"root",factory:Gh});let e=t;return e})();var zM=cv(null,"core",[]);function hv(e){try{let{rootComponent:t,appProviders:r,platformProviders:n}=e,o=VM(n),i=[FM(),...r||[]],a=new Ts({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1}).injector,u=a.get(W);return u.run(()=>{a.resolveInjectorInitializers();let c=a.get(st,null),l;u.runOutsideAngular(()=>{l=u.onError.subscribe({next:h=>{c.handleError(h)}})});let d=()=>a.destroy(),f=o.get(Yl);return f.add(d),a.onDestroy(()=>{l.unsubscribe(),f.delete(d)}),nv(c,u,()=>{let h=a.get(Gl);return h.runInitializers(),h.donePromise.then(()=>{let p=a.get(Rr,yr);jm(p||yr);let y=a.get(Lt);return t!==void 0&&y.bootstrap(t),y})})})}catch(t){return Promise.reject(t)}}var Zh=!1,pv=!1;function qM(){Zh||(Zh=!0,tI(),z0(),lM(),Y0(),h0(),kb(),lb(),nC(),oM())}function WM(e,t){return Zl(e)}function gv(){return Jt([{provide:zi,useFactory:()=>{let e=!0;if(ro()){let t=g(en,{optional:!0});e=!!(t!=null&&t.get(gl,null))}return e&&tn("NgHydration"),e}},{provide:In,useValue:()=>{pv=!!g(iI,{optional:!0}),ro()&&g(zi)&&(ZM(),qM())},multi:!0},{provide:vg,useFactory:()=>ro()&&g(zi)},{provide:Pn,useFactory:()=>{if(ro()&&g(zi)){let e=g(Lt),t=g(ct);return()=>{WM(e,t).then(()=>{JC(e)})}}return()=>{}},multi:!0}])}function GM(){return pv}function ZM(){var r;let e=Ao(),t;for(let n of e.body.childNodes)if(n.nodeType===Node.COMMENT_NODE&&((r=n.textContent)==null?void 0:r.trim())===XE){t=n;break}if(!t)throw new D(-507,!1)}var Hc=class{constructor(){this.views=[],this.indexByContent=new Map}add(t){let r=JSON.stringify(t);if(!this.indexByContent.has(r)){let n=this.views.length;return this.views.push(t),this.indexByContent.set(r,n),n}return this.indexByContent.get(r)}getAll(){return this.views}},YM=0;function mv(e){return e.ssrId||(e.ssrId=`t${YM++}`),e.ssrId}function vv(e,t,r){let n=[];return Do(e,t,r,n),n.length}function QM(e){let t=[];return nm(e,t),t.length}function yv(e,t){let r=e[ie];return r&&!r.hasAttribute(vo)?_s(r,e,t):null}function Dv(e,t){let r=Pp(e[ie]),n=yv(r,t),o=le(r[ie]),i=e[de],s=_s(o,i,t),a=r[V],u=`${n}|${s}`;a.setAttribute(o,io,u)}function Y1(e,t){let r=new Hc,n=new Map,o=e._views;for(let a of o){let u=pg(a);if(u!==null){let c={serializedViewCollection:r,corruptedTextNodes:n};je(u)?Dv(u,c):yv(u,c),eT(n,t)}}let i=r.getAll();e.injector.get(en).set(gl,i)}function KM(e,t){var o;let r=[],n="";for(let i=ye;i<e.length;i++){let s=e[i],a,u,c;if(Jc(s)&&(s=s[G],je(s))){u=QM(s)+1,Dv(s,t);let d=Pp(s[ie]);c={[Ju]:d[x].ssrId,[gr]:u}}if(!c){let d=s[x];d.type===1?(a=d.ssrId,u=1):(a=mv(d),u=vv(d,s,d.firstChild)),c=w({[Ju]:a,[gr]:u},wv(e[i],t))}let l=JSON.stringify(c);if(r.length>0&&l===n){let d=r[r.length-1];(o=d[ir])!=null||(d[ir]=1),d[ir]++}else n=l,r.push(c)}return r}function As(e,t,r){var o;let n=t.index-G;(o=e[no])!=null||(e[no]={}),e[no][n]=ab(t,r)}function Yh(e,t){var n;let r=t.index-G;(n=e[nr])!=null||(e[nr]=[]),e[nr].includes(r)||e[nr].push(r)}function wv(e,t){var o,i,s,a;let r={},n=e[x];for(let u=G;u<n.bindingStartIndex;u++){let c=n.data[u],l=u-G;if(TE(c)){if(wo(c,e)&&tT(c)){Yh(r,c);continue}if(Array.isArray(c.projection)){for(let d of c.projection)if(d)if(!Array.isArray(d))!Zw(d)&&!hs(d)&&(wo(d,e)?Yh(r,d):As(r,d,e));else throw UC(le(e[u]))}if(JM(r,c,e),je(e[u])){let d=c.tView;d!==null&&((o=r[to])!=null||(r[to]={}),r[to][l]=mv(d));let f=e[u][ie];if(Array.isArray(f)){let h=le(f);h.hasAttribute(vo)||_s(h,f,t)}(i=r[Dn])!=null||(r[Dn]={}),r[Dn][l]=KM(e[u],t)}else if(Array.isArray(e[u])){let d=le(e[u][ie]);d.hasAttribute(vo)||_s(d,e[u],t)}else if(c.type&8)(s=r[eo])!=null||(r[eo]={}),r[eo][l]=vv(n,e,c.child);else if(c.type&16){let d=c.next;for(;d!==null&&d.type&16;)d=d.next;d&&!hs(d)&&As(r,d,e)}else if(c.type&1){let d=le(e[u]);d.textContent===""?t.corruptedTextNodes.set(d,"ngetn"):((a=d.nextSibling)==null?void 0:a.nodeType)===Node.TEXT_NODE&&t.corruptedTextNodes.set(d,"ngtns")}}}return r}function JM(e,t,r){t.projectionNext&&t.projectionNext!==t.next&&!hs(t.projectionNext)&&As(e,t.projectionNext,r),t.prev===null&&t.parent!==null&&wo(t.parent,r)&&!wo(t,r)&&As(e,t,r)}function XM(e){var r;let t=e[Fe];return t!=null&&t.constructor?((r=Dt(t.constructor))==null?void 0:r.encapsulation)===ot.ShadowDom:!1}function _s(e,t,r){let n=t[V];if(Yw(t)&&!GM()||XM(t))return n.setAttribute(e,vo,""),null;{let o=wv(t,r),i=r.serializedViewCollection.add(o);return n.setAttribute(e,io,i.toString()),i}}function eT(e,t){for(let[r,n]of e)r.after(t.createComment(n))}function tT(e){let t=e;for(;t!=null;){if(Ir(t))return!0;t=t.parent}return!1}function Oo(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Jl(e){let t=j(null);try{return e()}finally{j(t)}}function Q1(e,t){let r=Dt(e),n=t.elementInjector||ks();return new An(r).create(n,t.projectableNodes,t.hostElement,t.environmentInjector)}function Ev(e){let t=Dt(e);if(!t)return null;let r=new An(t);return{get selector(){return r.selector},get type(){return r.componentType},get inputs(){return r.inputs},get outputs(){return r.outputs},get ngContentSelectors(){return r.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}function K1(...e){return e.reduce((t,r)=>Object.assign(t,r,{providers:[...t.providers,...r.providers]}),{providers:[]})}var ya=null;function Pr(){return ya}function Nv(e){ya!=null||(ya=e)}var wa=class{};var me=new C(""),dd=(()=>{let t=class t{historyGo(n){throw new Error("")}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:()=>g(nT),providedIn:"platform"});let e=t;return e})(),Rv=new C(""),nT=(()=>{let t=class t extends dd{constructor(){super(),this._doc=g(me),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Pr().getBaseHref(this._doc)}onPopState(n){let o=Pr().getGlobalEventTarget(this._doc,"window");return o.addEventListener("popstate",n,!1),()=>o.removeEventListener("popstate",n)}onHashChange(n){let o=Pr().getGlobalEventTarget(this._doc,"window");return o.addEventListener("hashchange",n,!1),()=>o.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,o,i){this._history.pushState(n,o,i)}replaceState(n,o,i){this._history.replaceState(n,o,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:()=>new t,providedIn:"platform"});let e=t;return e})();function fd(e,t){if(e.length==0)return t;if(t.length==0)return e;let r=0;return e.endsWith("/")&&r++,t.startsWith("/")&&r++,r==2?e+t.substring(1):r==1?e+t:e+"/"+t}function Iv(e){let t=e.match(/#|\?|$/),r=t&&t.index||e.length,n=r-(e[r-1]==="/"?1:0);return e.slice(0,n)+e.slice(r)}function $t(e){return e&&e[0]!=="?"?"?"+e:e}var Bt=(()=>{let t=class t{historyGo(n){throw new Error("")}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:()=>g(hd),providedIn:"root"});let e=t;return e})(),Ov=new C(""),hd=(()=>{let t=class t extends Bt{constructor(n,o){var i,s,a;super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=(a=(s=o!=null?o:this._platformLocation.getBaseHrefFromDOM())!=null?s:(i=g(me).location)==null?void 0:i.origin)!=null?a:""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return fd(this._baseHref,n)}path(n=!1){let o=this._platformLocation.pathname+$t(this._platformLocation.search),i=this._platformLocation.hash;return i&&n?`${o}${i}`:o}pushState(n,o,i,s){let a=this.prepareExternalUrl(i+$t(s));this._platformLocation.pushState(n,o,a)}replaceState(n,o,i,s){let a=this.prepareExternalUrl(i+$t(s));this._platformLocation.replaceState(n,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){var o,i;(i=(o=this._platformLocation).historyGo)==null||i.call(o,n)}};t.\u0275fac=function(o){return new(o||t)(I(dd),I(Ov,8))},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Pv=(()=>{let t=class t extends Bt{constructor(n,o){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],o!=null&&(this._baseHref=o)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){var i;let o=(i=this._platformLocation.hash)!=null?i:"#";return o.length>0?o.substring(1):o}prepareExternalUrl(n){let o=fd(this._baseHref,n);return o.length>0?"#"+o:o}pushState(n,o,i,s){let a=this.prepareExternalUrl(i+$t(s));a.length==0&&(a=this._platformLocation.pathname),this._platformLocation.pushState(n,o,a)}replaceState(n,o,i,s){let a=this.prepareExternalUrl(i+$t(s));a.length==0&&(a=this._platformLocation.pathname),this._platformLocation.replaceState(n,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){var o,i;(i=(o=this._platformLocation).historyGo)==null||i.call(o,n)}};t.\u0275fac=function(o){return new(o||t)(I(dd),I(Ov,8))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),kr=(()=>{let t=class t{constructor(n){this._subject=new ge,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let o=this._locationStrategy.getBaseHref();this._basePath=iT(Iv(Cv(o))),this._locationStrategy.onPopState(i=>{this._subject.emit({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){var n;(n=this._urlChangeSubscription)==null||n.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,o=""){return this.path()==this.normalize(n+$t(o))}normalize(n){return t.stripTrailingSlash(oT(this._basePath,Cv(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,o="",i=null){this._locationStrategy.pushState(i,"",n,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+$t(o)),i)}replaceState(n,o="",i=null){this._locationStrategy.replaceState(i,"",n,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+$t(o)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){var o,i;(i=(o=this._locationStrategy).historyGo)==null||i.call(o,n)}onUrlChange(n){var o;return this._urlChangeListeners.push(n),(o=this._urlChangeSubscription)!=null||(this._urlChangeSubscription=this.subscribe(i=>{this._notifyUrlChangeListeners(i.url,i.state)})),()=>{var s;let i=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(i,1),this._urlChangeListeners.length===0&&((s=this._urlChangeSubscription)==null||s.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",o){this._urlChangeListeners.forEach(i=>i(n,o))}subscribe(n,o,i){return this._subject.subscribe({next:n,error:o,complete:i})}};t.normalizeQueryParams=$t,t.joinWithSlash=fd,t.stripTrailingSlash=Iv,t.\u0275fac=function(o){return new(o||t)(I(Bt))},t.\u0275prov=E({token:t,factory:()=>rT(),providedIn:"root"});let e=t;return e})();function rT(){return new kr(I(Bt))}function oT(e,t){if(!e||!t.startsWith(e))return t;let r=t.substring(e.length);return r===""||["/",";","?","#"].includes(r[0])?r:t}function Cv(e){return e.replace(/\/index.html$/,"")}function iT(e){if(new RegExp("^(https?:)?//").test(e)){let[,r]=e.split(/\/\/[^\/]+/);return r}return e}var Fv={ADP:[void 0,void 0,0],AFN:[void 0,"\u060B",0],ALL:[void 0,void 0,0],AMD:[void 0,"\u058F",2],AOA:[void 0,"Kz"],ARS:[void 0,"$"],AUD:["A$","$"],AZN:[void 0,"\u20BC"],BAM:[void 0,"KM"],BBD:[void 0,"$"],BDT:[void 0,"\u09F3"],BHD:[void 0,void 0,3],BIF:[void 0,void 0,0],BMD:[void 0,"$"],BND:[void 0,"$"],BOB:[void 0,"Bs"],BRL:["R$"],BSD:[void 0,"$"],BWP:[void 0,"P"],BYN:[void 0,void 0,2],BYR:[void 0,void 0,0],BZD:[void 0,"$"],CAD:["CA$","$",2],CHF:[void 0,void 0,2],CLF:[void 0,void 0,4],CLP:[void 0,"$",0],CNY:["CN\xA5","\xA5"],COP:[void 0,"$",2],CRC:[void 0,"\u20A1",2],CUC:[void 0,"$"],CUP:[void 0,"$"],CZK:[void 0,"K\u010D",2],DJF:[void 0,void 0,0],DKK:[void 0,"kr",2],DOP:[void 0,"$"],EGP:[void 0,"E\xA3"],ESP:[void 0,"\u20A7",0],EUR:["\u20AC"],FJD:[void 0,"$"],FKP:[void 0,"\xA3"],GBP:["\xA3"],GEL:[void 0,"\u20BE"],GHS:[void 0,"GH\u20B5"],GIP:[void 0,"\xA3"],GNF:[void 0,"FG",0],GTQ:[void 0,"Q"],GYD:[void 0,"$",2],HKD:["HK$","$"],HNL:[void 0,"L"],HRK:[void 0,"kn"],HUF:[void 0,"Ft",2],IDR:[void 0,"Rp",2],ILS:["\u20AA"],INR:["\u20B9"],IQD:[void 0,void 0,0],IRR:[void 0,void 0,0],ISK:[void 0,"kr",0],ITL:[void 0,void 0,0],JMD:[void 0,"$"],JOD:[void 0,void 0,3],JPY:["\xA5",void 0,0],KHR:[void 0,"\u17DB"],KMF:[void 0,"CF",0],KPW:[void 0,"\u20A9",0],KRW:["\u20A9",void 0,0],KWD:[void 0,void 0,3],KYD:[void 0,"$"],KZT:[void 0,"\u20B8"],LAK:[void 0,"\u20AD",0],LBP:[void 0,"L\xA3",0],LKR:[void 0,"Rs"],LRD:[void 0,"$"],LTL:[void 0,"Lt"],LUF:[void 0,void 0,0],LVL:[void 0,"Ls"],LYD:[void 0,void 0,3],MGA:[void 0,"Ar",0],MGF:[void 0,void 0,0],MMK:[void 0,"K",0],MNT:[void 0,"\u20AE",2],MRO:[void 0,void 0,0],MUR:[void 0,"Rs",2],MXN:["MX$","$"],MYR:[void 0,"RM"],NAD:[void 0,"$"],NGN:[void 0,"\u20A6"],NIO:[void 0,"C$"],NOK:[void 0,"kr",2],NPR:[void 0,"Rs"],NZD:["NZ$","$"],OMR:[void 0,void 0,3],PHP:["\u20B1"],PKR:[void 0,"Rs",2],PLN:[void 0,"z\u0142"],PYG:[void 0,"\u20B2",0],RON:[void 0,"lei"],RSD:[void 0,void 0,0],RUB:[void 0,"\u20BD"],RWF:[void 0,"RF",0],SBD:[void 0,"$"],SEK:[void 0,"kr",2],SGD:[void 0,"$"],SHP:[void 0,"\xA3"],SLE:[void 0,void 0,2],SLL:[void 0,void 0,0],SOS:[void 0,void 0,0],SRD:[void 0,"$"],SSP:[void 0,"\xA3"],STD:[void 0,void 0,0],STN:[void 0,"Db"],SYP:[void 0,"\xA3",0],THB:[void 0,"\u0E3F"],TMM:[void 0,void 0,0],TND:[void 0,void 0,3],TOP:[void 0,"T$"],TRL:[void 0,void 0,0],TRY:[void 0,"\u20BA"],TTD:[void 0,"$"],TWD:["NT$","$",2],TZS:[void 0,void 0,2],UAH:[void 0,"\u20B4"],UGX:[void 0,void 0,0],USD:["$"],UYI:[void 0,void 0,0],UYU:[void 0,"$"],UYW:[void 0,void 0,4],UZS:[void 0,void 0,2],VEF:[void 0,"Bs",2],VND:["\u20AB",void 0,0],VUV:[void 0,void 0,0],XAF:["FCFA",void 0,0],XCD:["EC$","$"],XOF:["F\u202FCFA",void 0,0],XPF:["CFPF",void 0,0],XXX:["\xA4"],YER:[void 0,void 0,0],ZAR:[void 0,"R"],ZMK:[void 0,void 0,0],ZMW:[void 0,"ZK"],ZWD:[void 0,void 0,0]},kv=function(e){return e[e.Decimal=0]="Decimal",e[e.Percent=1]="Percent",e[e.Currency=2]="Currency",e[e.Scientific=3]="Scientific",e}(kv||{});var Te=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(Te||{}),Y=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(Y||{}),Ve=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Ve||{}),Ue={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function sT(e){return Re(e)[ee.LocaleId]}function aT(e,t,r){let n=Re(e),o=[n[ee.DayPeriodsFormat],n[ee.DayPeriodsStandalone]],i=Ze(o,t);return Ze(i,r)}function uT(e,t,r){let n=Re(e),o=[n[ee.DaysFormat],n[ee.DaysStandalone]],i=Ze(o,t);return Ze(i,r)}function cT(e,t,r){let n=Re(e),o=[n[ee.MonthsFormat],n[ee.MonthsStandalone]],i=Ze(o,t);return Ze(i,r)}function lT(e,t){let n=Re(e)[ee.Eras];return Ze(n,t)}function fa(e,t){let r=Re(e);return Ze(r[ee.DateFormat],t)}function ha(e,t){let r=Re(e);return Ze(r[ee.TimeFormat],t)}function pa(e,t){let n=Re(e)[ee.DateTimeFormat];return Ze(n,t)}function Vt(e,t){let r=Re(e),n=r[ee.NumberSymbols][t];if(typeof n>"u"){if(t===Ue.CurrencyDecimal)return r[ee.NumberSymbols][Ue.Decimal];if(t===Ue.CurrencyGroup)return r[ee.NumberSymbols][Ue.Group]}return n}function dT(e,t){return Re(e)[ee.NumberFormats][t]}function fT(e){return Re(e)[ee.Currencies]}function Lv(e){if(!e[ee.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[ee.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function hT(e){let t=Re(e);return Lv(t),(t[ee.ExtraData][2]||[]).map(n=>typeof n=="string"?Xl(n):[Xl(n[0]),Xl(n[1])])}function pT(e,t,r){let n=Re(e);Lv(n);let o=[n[ee.ExtraData][0],n[ee.ExtraData][1]],i=Ze(o,t)||[];return Ze(i,r)||[]}function Ze(e,t){for(let r=t;r>-1;r--)if(typeof e[r]<"u")return e[r];throw new Error("Locale data API: locale data undefined")}function Xl(e){let[t,r]=e.split(":");return{hours:+t,minutes:+r}}function gT(e,t,r="en"){let n=fT(r)[e]||Fv[e]||[],o=n[1];return t==="narrow"&&typeof o=="string"?o:n[0]||e}var mT=2;function vT(e){let t,r=Fv[e];return r&&(t=r[2]),typeof t=="number"?t:mT}var yT=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Or={},DT=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,Ut=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(Ut||{}),B=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(B||{}),U=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(U||{});function wT(e,t,r,n){let o=AT(e);t=jt(r,t)||t;let s=[],a;for(;t;)if(a=DT.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let u=o.getTimezoneOffset();n&&(u=$v(n,u),o=xT(o,n,!0));let c="";return s.forEach(l=>{let d=TT(l);c+=d?d(o,r,u):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function Ea(e,t,r){let n=new Date(0);return n.setFullYear(e,t,r),n.setHours(0,0,0),n}function jt(e,t){var o;let r=sT(e);if((o=Or[r])!=null||(Or[r]={}),Or[r][t])return Or[r][t];let n="";switch(t){case"shortDate":n=fa(e,Ve.Short);break;case"mediumDate":n=fa(e,Ve.Medium);break;case"longDate":n=fa(e,Ve.Long);break;case"fullDate":n=fa(e,Ve.Full);break;case"shortTime":n=ha(e,Ve.Short);break;case"mediumTime":n=ha(e,Ve.Medium);break;case"longTime":n=ha(e,Ve.Long);break;case"fullTime":n=ha(e,Ve.Full);break;case"short":let i=jt(e,"shortTime"),s=jt(e,"shortDate");n=ga(pa(e,Ve.Short),[i,s]);break;case"medium":let a=jt(e,"mediumTime"),u=jt(e,"mediumDate");n=ga(pa(e,Ve.Medium),[a,u]);break;case"long":let c=jt(e,"longTime"),l=jt(e,"longDate");n=ga(pa(e,Ve.Long),[c,l]);break;case"full":let d=jt(e,"fullTime"),f=jt(e,"fullDate");n=ga(pa(e,Ve.Full),[d,f]);break}return n&&(Or[r][t]=n),n}function ga(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(r,n){return t!=null&&n in t?t[n]:r})),e}function ft(e,t,r="-",n,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=r));let s=String(e);for(;s.length<t;)s="0"+s;return n&&(s=s.slice(s.length-t)),i+s}function ET(e,t){return ft(e,3).substring(0,t)}function ce(e,t,r=0,n=!1,o=!1){return function(i,s){let a=IT(e,i);if((r>0||a>-r)&&(a+=r),e===B.Hours)a===0&&r===-12&&(a=12);else if(e===B.FractionalSeconds)return ET(a,t);let u=Vt(s,Ue.MinusSign);return ft(a,t,u,n,o)}}function IT(e,t){switch(e){case B.FullYear:return t.getFullYear();case B.Month:return t.getMonth();case B.Date:return t.getDate();case B.Hours:return t.getHours();case B.Minutes:return t.getMinutes();case B.Seconds:return t.getSeconds();case B.FractionalSeconds:return t.getMilliseconds();case B.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function K(e,t,r=Te.Format,n=!1){return function(o,i){return CT(o,i,e,t,r,n)}}function CT(e,t,r,n,o,i){switch(r){case U.Months:return cT(t,o,n)[e.getMonth()];case U.Days:return uT(t,o,n)[e.getDay()];case U.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(i){let c=hT(t),l=pT(t,o,n),d=c.findIndex(f=>{if(Array.isArray(f)){let[h,p]=f,y=s>=h.hours&&a>=h.minutes,m=s<p.hours||s===p.hours&&a<p.minutes;if(h.hours<p.hours){if(y&&m)return!0}else if(y||m)return!0}else if(f.hours===s&&f.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return aT(t,o,n)[s<12?0:1];case U.Eras:return lT(t,n)[e.getFullYear()<=0?0:1];default:let u=r;throw new Error(`unexpected translation type ${u}`)}}function ma(e){return function(t,r,n){let o=-1*n,i=Vt(r,Ue.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case Ut.Short:return(o>=0?"+":"")+ft(s,2,i)+ft(Math.abs(o%60),2,i);case Ut.ShortGMT:return"GMT"+(o>=0?"+":"")+ft(s,1,i);case Ut.Long:return"GMT"+(o>=0?"+":"")+ft(s,2,i)+":"+ft(Math.abs(o%60),2,i);case Ut.Extended:return n===0?"Z":(o>=0?"+":"")+ft(s,2,i)+":"+ft(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var bT=0,Da=4;function MT(e){let t=Ea(e,bT,1).getDay();return Ea(e,0,1+(t<=Da?Da:Da+7)-t)}function jv(e){let t=e.getDay(),r=t===0?-3:Da-t;return Ea(e.getFullYear(),e.getMonth(),e.getDate()+r)}function ed(e,t=!1){return function(r,n){let o;if(t){let i=new Date(r.getFullYear(),r.getMonth(),1).getDay()-1,s=r.getDate();o=1+Math.floor((s+i)/7)}else{let i=jv(r),s=MT(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return ft(o,e,Vt(n,Ue.MinusSign))}}function va(e,t=!1){return function(r,n){let i=jv(r).getFullYear();return ft(i,e,Vt(n,Ue.MinusSign),t)}}var td={};function TT(e){if(td[e])return td[e];let t;switch(e){case"G":case"GG":case"GGG":t=K(U.Eras,Y.Abbreviated);break;case"GGGG":t=K(U.Eras,Y.Wide);break;case"GGGGG":t=K(U.Eras,Y.Narrow);break;case"y":t=ce(B.FullYear,1,0,!1,!0);break;case"yy":t=ce(B.FullYear,2,0,!0,!0);break;case"yyy":t=ce(B.FullYear,3,0,!1,!0);break;case"yyyy":t=ce(B.FullYear,4,0,!1,!0);break;case"Y":t=va(1);break;case"YY":t=va(2,!0);break;case"YYY":t=va(3);break;case"YYYY":t=va(4);break;case"M":case"L":t=ce(B.Month,1,1);break;case"MM":case"LL":t=ce(B.Month,2,1);break;case"MMM":t=K(U.Months,Y.Abbreviated);break;case"MMMM":t=K(U.Months,Y.Wide);break;case"MMMMM":t=K(U.Months,Y.Narrow);break;case"LLL":t=K(U.Months,Y.Abbreviated,Te.Standalone);break;case"LLLL":t=K(U.Months,Y.Wide,Te.Standalone);break;case"LLLLL":t=K(U.Months,Y.Narrow,Te.Standalone);break;case"w":t=ed(1);break;case"ww":t=ed(2);break;case"W":t=ed(1,!0);break;case"d":t=ce(B.Date,1);break;case"dd":t=ce(B.Date,2);break;case"c":case"cc":t=ce(B.Day,1);break;case"ccc":t=K(U.Days,Y.Abbreviated,Te.Standalone);break;case"cccc":t=K(U.Days,Y.Wide,Te.Standalone);break;case"ccccc":t=K(U.Days,Y.Narrow,Te.Standalone);break;case"cccccc":t=K(U.Days,Y.Short,Te.Standalone);break;case"E":case"EE":case"EEE":t=K(U.Days,Y.Abbreviated);break;case"EEEE":t=K(U.Days,Y.Wide);break;case"EEEEE":t=K(U.Days,Y.Narrow);break;case"EEEEEE":t=K(U.Days,Y.Short);break;case"a":case"aa":case"aaa":t=K(U.DayPeriods,Y.Abbreviated);break;case"aaaa":t=K(U.DayPeriods,Y.Wide);break;case"aaaaa":t=K(U.DayPeriods,Y.Narrow);break;case"b":case"bb":case"bbb":t=K(U.DayPeriods,Y.Abbreviated,Te.Standalone,!0);break;case"bbbb":t=K(U.DayPeriods,Y.Wide,Te.Standalone,!0);break;case"bbbbb":t=K(U.DayPeriods,Y.Narrow,Te.Standalone,!0);break;case"B":case"BB":case"BBB":t=K(U.DayPeriods,Y.Abbreviated,Te.Format,!0);break;case"BBBB":t=K(U.DayPeriods,Y.Wide,Te.Format,!0);break;case"BBBBB":t=K(U.DayPeriods,Y.Narrow,Te.Format,!0);break;case"h":t=ce(B.Hours,1,-12);break;case"hh":t=ce(B.Hours,2,-12);break;case"H":t=ce(B.Hours,1);break;case"HH":t=ce(B.Hours,2);break;case"m":t=ce(B.Minutes,1);break;case"mm":t=ce(B.Minutes,2);break;case"s":t=ce(B.Seconds,1);break;case"ss":t=ce(B.Seconds,2);break;case"S":t=ce(B.FractionalSeconds,1);break;case"SS":t=ce(B.FractionalSeconds,2);break;case"SSS":t=ce(B.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=ma(Ut.Short);break;case"ZZZZZ":t=ma(Ut.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=ma(Ut.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=ma(Ut.Long);break;default:return null}return td[e]=t,t}function $v(e,t){e=e.replace(/:/g,"");let r=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(r)?t:r}function ST(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function xT(e,t,r){let n=r?-1:1,o=e.getTimezoneOffset(),i=$v(t,o);return ST(e,n*(i-o))}function AT(e){if(bv(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return Ea(o,i-1,s)}let r=parseFloat(e);if(!isNaN(e-r))return new Date(r);let n;if(n=e.match(yT))return _T(n)}let t=new Date(e);if(!bv(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function _T(e){let t=new Date(0),r=0,n=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(r=Number(e[9]+e[10]),n=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-r,a=Number(e[5]||0)-n,u=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,u,c),t}function bv(e){return e instanceof Date&&!isNaN(e.valueOf())}var NT=/^(\d+)?\.((\d+)(-(\d+))?)?$/,Mv=22,Ia=".",Po="0",RT=";",OT=",",nd="#",Tv="\xA4";function PT(e,t,r,n,o,i,s=!1){let a="",u=!1;if(!isFinite(e))a=Vt(r,Ue.Infinity);else{let c=jT(e);s&&(c=LT(c));let l=t.minInt,d=t.minFrac,f=t.maxFrac;if(i){let A=i.match(NT);if(A===null)throw new Error(`${i} is not a valid digit info`);let q=A[1],O=A[3],re=A[5];q!=null&&(l=rd(q)),O!=null&&(d=rd(O)),re!=null?f=rd(re):O!=null&&d>f&&(f=d)}$T(c,d,f);let h=c.digits,p=c.integerLen,y=c.exponent,m=[];for(u=h.every(A=>!A);p<l;p++)h.unshift(0);for(;p<0;p++)h.unshift(0);p>0?m=h.splice(p,h.length):(m=h,h=[0]);let v=[];for(h.length>=t.lgSize&&v.unshift(h.splice(-t.lgSize,h.length).join(""));h.length>t.gSize;)v.unshift(h.splice(-t.gSize,h.length).join(""));h.length&&v.unshift(h.join("")),a=v.join(Vt(r,n)),m.length&&(a+=Vt(r,o)+m.join("")),y&&(a+=Vt(r,Ue.Exponential)+"+"+y)}return e<0&&!u?a=t.negPre+a+t.negSuf:a=t.posPre+a+t.posSuf,a}function FT(e,t,r,n,o){let i=dT(t,kv.Currency),s=kT(i,Vt(t,Ue.MinusSign));return s.minFrac=vT(n),s.maxFrac=s.minFrac,PT(e,s,t,Ue.CurrencyGroup,Ue.CurrencyDecimal,o).replace(Tv,r).replace(Tv,"").trim()}function kT(e,t="-"){let r={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},n=e.split(RT),o=n[0],i=n[1],s=o.indexOf(Ia)!==-1?o.split(Ia):[o.substring(0,o.lastIndexOf(Po)+1),o.substring(o.lastIndexOf(Po)+1)],a=s[0],u=s[1]||"";r.posPre=a.substring(0,a.indexOf(nd));for(let l=0;l<u.length;l++){let d=u.charAt(l);d===Po?r.minFrac=r.maxFrac=l+1:d===nd?r.maxFrac=l+1:r.posSuf+=d}let c=a.split(OT);if(r.gSize=c[1]?c[1].length:0,r.lgSize=c[2]||c[1]?(c[2]||c[1]).length:0,i){let l=o.length-r.posPre.length-r.posSuf.length,d=i.indexOf(nd);r.negPre=i.substring(0,d).replace(/'/g,""),r.negSuf=i.slice(d+l).replace(/'/g,"")}else r.negPre=t+r.posPre,r.negSuf=r.posSuf;return r}function LT(e){if(e.digits[0]===0)return e;let t=e.digits.length-e.integerLen;return e.exponent?e.exponent+=2:(t===0?e.digits.push(0,0):t===1&&e.digits.push(0),e.integerLen+=2),e}function jT(e){let t=Math.abs(e)+"",r=0,n,o,i,s,a;for((o=t.indexOf(Ia))>-1&&(t=t.replace(Ia,"")),(i=t.search(/e/i))>0?(o<0&&(o=i),o+=+t.slice(i+1),t=t.substring(0,i)):o<0&&(o=t.length),i=0;t.charAt(i)===Po;i++);if(i===(a=t.length))n=[0],o=1;else{for(a--;t.charAt(a)===Po;)a--;for(o-=i,n=[],s=0;i<=a;i++,s++)n[s]=Number(t.charAt(i))}return o>Mv&&(n=n.splice(0,Mv-1),r=o-1,o=1),{digits:n,exponent:r,integerLen:o}}function $T(e,t,r){if(t>r)throw new Error(`The minimum number of digits after fraction (${t}) is higher than the maximum (${r}).`);let n=e.digits,o=n.length-e.integerLen,i=Math.min(Math.max(t,o),r),s=i+e.integerLen,a=n[s];if(s>0){n.splice(Math.max(e.integerLen,s));for(let d=s;d<n.length;d++)n[d]=0}else{o=Math.max(0,o),e.integerLen=1,n.length=Math.max(1,s=i+1),n[0]=0;for(let d=1;d<s;d++)n[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)n.unshift(0),e.integerLen++;n.unshift(1),e.integerLen++}else n[s-1]++;for(;o<Math.max(0,i);o++)n.push(0);let u=i!==0,c=t+e.integerLen,l=n.reduceRight(function(d,f,h,p){return f=f+d,p[h]=f<10?f:f-10,u&&(p[h]===0&&h>=c?p.pop():u=!1),f>=10?1:0},0);l&&(n.unshift(l),e.integerLen++)}function rd(e){let t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}function Ca(e,t){t=encodeURIComponent(t);for(let r of e.split(";")){let n=r.indexOf("="),[o,i]=n==-1?[r,""]:[r.slice(0,n),r.slice(n+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var od=/\s+/,Sv=[],mL=(()=>{let t=class t{constructor(n,o){this._ngEl=n,this._renderer=o,this.initialClasses=Sv,this.stateMap=new Map}set klass(n){this.initialClasses=n!=null?n.trim().split(od):Sv}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(od):n}ngDoCheck(){for(let o of this.initialClasses)this._updateState(o,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let o of n)this._updateState(o,!0);else if(n!=null)for(let o of Object.keys(n))this._updateState(o,!!n[o]);this._applyStateDiff()}_updateState(n,o){let i=this.stateMap.get(n);i!==void 0?(i.enabled!==o&&(i.changed=!0,i.enabled=o),i.touched=!0):this.stateMap.set(n,{enabled:o,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let o=n[0],i=n[1];i.changed?(this._toggleClass(o,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(o,!1),this.stateMap.delete(o)),i.touched=!1}}_toggleClass(n,o){n=n.trim(),n.length>0&&n.split(od).forEach(i=>{o?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}};t.\u0275fac=function(o){return new(o||t)($(lt),$(On))},t.\u0275dir=Ot({type:t,selectors:[["","ngClass",""]],inputs:{klass:[ze.None,"class","klass"],ngClass:"ngClass"},standalone:!0});let e=t;return e})();var id=class{constructor(t,r,n,o){this.$implicit=t,this.ngForOf=r,this.index=n,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},vL=(()=>{let t=class t{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,o,i){this._viewContainer=n,this._template=o,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let o=this._viewContainer;n.forEachOperation((i,s,a)=>{if(i.previousIndex==null)o.createEmbeddedView(this._template,new id(i.item,this._ngForOf,-1,-1),a===null?void 0:a);else if(a==null)o.remove(s===null?void 0:s);else if(s!==null){let u=o.get(s);o.move(u,a),xv(u,i)}});for(let i=0,s=o.length;i<s;i++){let u=o.get(i).context;u.index=i,u.count=s,u.ngForOf=this._ngForOf}n.forEachIdentityChange(i=>{let s=o.get(i.currentIndex);xv(s,i)})}static ngTemplateContextGuard(n,o){return!0}};t.\u0275fac=function(o){return new(o||t)($(nn),$(xn),$(Ql))},t.\u0275dir=Ot({type:t,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0});let e=t;return e})();function xv(e,t){e.context.$implicit=t.item}var yL=(()=>{let t=class t{constructor(n,o){this._viewContainer=n,this._context=new sd,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=o}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Av("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Av("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,o){return!0}};t.\u0275fac=function(o){return new(o||t)($(nn),$(xn))},t.\u0275dir=Ot({type:t,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0});let e=t;return e})(),sd=class{constructor(){this.$implicit=null,this.ngIf=null}};function Av(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${Me(t)}'.`)}var DL=(()=>{let t=class t{constructor(n,o,i){this._ngEl=n,this._differs=o,this._renderer=i,this._ngStyle=null,this._differ=null}set ngStyle(n){this._ngStyle=n,!this._differ&&n&&(this._differ=this._differs.find(n).create())}ngDoCheck(){if(this._differ){let n=this._differ.diff(this._ngStyle);n&&this._applyChanges(n)}}_setStyle(n,o){let[i,s]=n.split("."),a=i.indexOf("-")===-1?void 0:wt.DashCase;o!=null?this._renderer.setStyle(this._ngEl.nativeElement,i,s?`${o}${s}`:o,a):this._renderer.removeStyle(this._ngEl.nativeElement,i,a)}_applyChanges(n){n.forEachRemovedItem(o=>this._setStyle(o.key,null)),n.forEachAddedItem(o=>this._setStyle(o.key,o.currentValue)),n.forEachChangedItem(o=>this._setStyle(o.key,o.currentValue))}};t.\u0275fac=function(o){return new(o||t)($(lt),$(Kl),$(On))},t.\u0275dir=Ot({type:t,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"},standalone:!0});let e=t;return e})();function Lr(e,t){return new D(2100,!1)}var ad=class{createSubscription(t,r){return Jl(()=>t.subscribe({next:r,error:n=>{throw n}}))}dispose(t){Jl(()=>t.unsubscribe())}},ud=class{createSubscription(t,r){return t.then(r,n=>{throw n})}dispose(t){}},VT=new ud,UT=new ad,wL=(()=>{let t=class t{constructor(n){this._latestValue=null,this.markForCheckOnValueUpdate=!0,this._subscription=null,this._obj=null,this._strategy=null,this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,o=>this._updateLatestValue(n,o))}_selectStrategy(n){if(Nr(n))return VT;if(Wl(n))return UT;throw Lr(t,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,o){var i;n===this._obj&&(this._latestValue=o,this.markForCheckOnValueUpdate&&((i=this._ref)==null||i.markForCheck()))}};t.\u0275fac=function(o){return new(o||t)($(Fn,16))},t.\u0275pipe=Nn({name:"async",type:t,pure:!1,standalone:!0});let e=t;return e})(),EL=(()=>{let t=class t{transform(n){if(n==null)return null;if(typeof n!="string")throw Lr(t,n);return n.toLowerCase()}};t.\u0275fac=function(o){return new(o||t)},t.\u0275pipe=Nn({name:"lowercase",type:t,pure:!0,standalone:!0});let e=t;return e})(),BT=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,IL=(()=>{let t=class t{transform(n){if(n==null)return null;if(typeof n!="string")throw Lr(t,n);return n.replace(BT,o=>o[0].toUpperCase()+o.slice(1).toLowerCase())}};t.\u0275fac=function(o){return new(o||t)},t.\u0275pipe=Nn({name:"titlecase",type:t,pure:!0,standalone:!0});let e=t;return e})();var HT="mediumDate",zT=new C(""),qT=new C(""),CL=(()=>{let t=class t{constructor(n,o,i){this.locale=n,this.defaultTimezone=o,this.defaultOptions=i}transform(n,o,i,s){var a,u,c,l,d;if(n==null||n===""||n!==n)return null;try{let f=(u=o!=null?o:(a=this.defaultOptions)==null?void 0:a.dateFormat)!=null?u:HT,h=(d=(l=i!=null?i:(c=this.defaultOptions)==null?void 0:c.timezone)!=null?l:this.defaultTimezone)!=null?d:void 0;return wT(n,f,s||this.locale,h)}catch(f){throw Lr(t,f.message)}}};t.\u0275fac=function(o){return new(o||t)($(Rr,16),$(zT,24),$(qT,24))},t.\u0275pipe=Nn({name:"date",type:t,pure:!0,standalone:!0});let e=t;return e})();var bL=(()=>{let t=class t{constructor(n,o="USD"){this._locale=n,this._defaultCurrencyCode=o}transform(n,o=this._defaultCurrencyCode,i="symbol",s,a){if(!WT(n))return null;a||(a=this._locale),typeof i=="boolean"&&(i=i?"symbol":"code");let u=o||this._defaultCurrencyCode;i!=="code"&&(i==="symbol"||i==="symbol-narrow"?u=gT(u,i==="symbol"?"wide":"narrow",a):u=i);try{let c=GT(n);return FT(c,a,u,o,s)}catch(c){throw Lr(t,c.message)}}};t.\u0275fac=function(o){return new(o||t)($(Rr,16),$(sv,16))},t.\u0275pipe=Nn({name:"currency",type:t,pure:!0,standalone:!0});let e=t;return e})();function WT(e){return!(e==null||e===""||e!==e)}function GT(e){if(typeof e=="string"&&!isNaN(Number(e)-parseFloat(e)))return Number(e);if(typeof e!="number")throw new Error(`${e} is not a number`);return e}var ML=(()=>{let t=class t{transform(n,o,i){if(n==null)return null;if(!this.supports(n))throw Lr(t,n);return n.slice(o,i)}supports(n){return typeof n=="string"||Array.isArray(n)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275pipe=Nn({name:"slice",type:t,pure:!1,standalone:!0});let e=t;return e})();var ZT=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=wr({type:t}),t.\u0275inj=Dr({});let e=t;return e})(),pd="browser",YT="server";function QT(e){return e===pd}function Fo(e){return e===YT}var Vv=(()=>{let t=class t{};t.\u0275prov=E({token:t,providedIn:"root",factory:()=>QT(g(We))?new cd(g(me),window):new ld});let e=t;return e})(),cd=class{constructor(t,r){this.document=t,this.window=r,this.offset=()=>[0,0]}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let r=KT(this.document,t);r&&(this.scrollToElement(r),r.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let r=t.getBoundingClientRect(),n=r.left+this.window.pageXOffset,o=r.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(n-i[0],o-i[1])}};function KT(e,t){let r=e.getElementById(t)||e.getElementsByName(t)[0];if(r)return r;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let n=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=n.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=n.nextNode()}}return null}var ld=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},Fr=class{};var Lo=class{},jo=class{},It=class e{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(r=>{let n=r.indexOf(":");if(n>0){let o=r.slice(0,n),i=o.toLowerCase(),s=r.slice(n+1).trim();this.maybeSetNormalizedName(o,i),this.headers.has(i)?this.headers.get(i).push(s):this.headers.set(i,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((r,n)=>{this.setHeaderEntries(n,r)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([r,n])=>{this.setHeaderEntries(r,n)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let r=this.headers.get(t.toLowerCase());return r&&r.length>0?r[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,r){return this.clone({name:t,value:r,op:"a"})}set(t,r){return this.clone({name:t,value:r,op:"s"})}delete(t,r){return this.clone({name:t,value:r,op:"d"})}maybeSetNormalizedName(t,r){this.normalizedNames.has(r)||this.normalizedNames.set(r,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(r=>{this.headers.set(r,t.headers.get(r)),this.normalizedNames.set(r,t.normalizedNames.get(r))})}clone(t){let r=new e;return r.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,r.lazyUpdate=(this.lazyUpdate||[]).concat([t]),r}applyUpdate(t){let r=t.name.toLowerCase();switch(t.op){case"a":case"s":let n=t.value;if(typeof n=="string"&&(n=[n]),n.length===0)return;this.maybeSetNormalizedName(t.name,r);let o=(t.op==="a"?this.headers.get(r):void 0)||[];o.push(...n),this.headers.set(r,o);break;case"d":let i=t.value;if(!i)this.headers.delete(r),this.normalizedNames.delete(r);else{let s=this.headers.get(r);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(r),this.normalizedNames.delete(r)):this.headers.set(r,s)}break}}setHeaderEntries(t,r){let n=(Array.isArray(r)?r:[r]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,n),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(r=>t(this.normalizedNames.get(r),this.headers.get(r)))}};var vd=class{encodeKey(t){return Bv(t)}encodeValue(t){return Bv(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function XT(e,t){let r=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],u=r.get(s)||[];u.push(a),r.set(s,u)}),r}var eS=/%(\d[a-f0-9])/gi,tS={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Bv(e){return encodeURIComponent(e).replace(eS,(t,r)=>{var n;return(n=tS[r])!=null?n:t})}function ba(e){return`${e}`}var on=class e{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new vd,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=XT(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(r=>{let n=t.fromObject[r],o=Array.isArray(n)?n.map(ba):[ba(n)];this.map.set(r,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let r=this.map.get(t);return r?r[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,r){return this.clone({param:t,value:r,op:"a"})}appendAll(t){let r=[];return Object.keys(t).forEach(n=>{let o=t[n];Array.isArray(o)?o.forEach(i=>{r.push({param:n,value:i,op:"a"})}):r.push({param:n,value:o,op:"a"})}),this.clone(r)}set(t,r){return this.clone({param:t,value:r,op:"s"})}delete(t,r){return this.clone({param:t,value:r,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let r=this.encoder.encodeKey(t);return this.map.get(t).map(n=>r+"="+this.encoder.encodeValue(n)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let r=new e({encoder:this.encoder});return r.cloneFrom=this.cloneFrom||this,r.updates=(this.updates||[]).concat(t),r}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let r=(t.op==="a"?this.map.get(t.param):void 0)||[];r.push(ba(t.value)),this.map.set(t.param,r);break;case"d":if(t.value!==void 0){let n=this.map.get(t.param)||[],o=n.indexOf(ba(t.value));o!==-1&&n.splice(o,1),n.length>0?this.map.set(t.param,n):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var yd=class{constructor(){this.map=new Map}set(t,r){return this.map.set(t,r),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function nS(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Hv(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function zv(e){return typeof Blob<"u"&&e instanceof Blob}function qv(e){return typeof FormData<"u"&&e instanceof FormData}function rS(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var ko=class e{constructor(t,r,n,o){var s,a;this.url=r,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase();let i;if(nS(this.method)||o?(this.body=n!==void 0?n:null,i=o):i=n,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),(s=this.headers)!=null||(this.headers=new It),(a=this.context)!=null||(this.context=new yd),!this.params)this.params=new on,this.urlWithParams=r;else{let u=this.params.toString();if(u.length===0)this.urlWithParams=r;else{let c=r.indexOf("?"),l=c===-1?"?":c<r.length-1?"&":"";this.urlWithParams=r+l+u}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Hv(this.body)||zv(this.body)||qv(this.body)||rS(this.body)?this.body:this.body instanceof on?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||qv(this.body)?null:zv(this.body)?this.body.type||null:Hv(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof on?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(t={}){var f,h,p,y;let r=t.method||this.method,n=t.url||this.url,o=t.responseType||this.responseType,i=(f=t.transferCache)!=null?f:this.transferCache,s=t.body!==void 0?t.body:this.body,a=(h=t.withCredentials)!=null?h:this.withCredentials,u=(p=t.reportProgress)!=null?p:this.reportProgress,c=t.headers||this.headers,l=t.params||this.params,d=(y=t.context)!=null?y:this.context;return t.setHeaders!==void 0&&(c=Object.keys(t.setHeaders).reduce((m,v)=>m.set(v,t.setHeaders[v]),c)),t.setParams&&(l=Object.keys(t.setParams).reduce((m,v)=>m.set(v,t.setParams[v]),l)),new e(r,n,s,{params:l,headers:c,context:d,reportProgress:u,responseType:o,withCredentials:a,transferCache:i})}},sn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(sn||{}),$o=class{constructor(t,r=Vo.Ok,n="OK"){this.headers=t.headers||new It,this.status=t.status!==void 0?t.status:r,this.statusText=t.statusText||n,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Ta=class e extends $o{constructor(t={}){super(t),this.type=sn.ResponseHeader}clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},kn=class e extends $o{constructor(t={}){super(t),this.type=sn.Response,this.body=t.body!==void 0?t.body:null}clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},rn=class extends $o{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},Vo=function(e){return e[e.Continue=100]="Continue",e[e.SwitchingProtocols=101]="SwitchingProtocols",e[e.Processing=102]="Processing",e[e.EarlyHints=103]="EarlyHints",e[e.Ok=200]="Ok",e[e.Created=201]="Created",e[e.Accepted=202]="Accepted",e[e.NonAuthoritativeInformation=203]="NonAuthoritativeInformation",e[e.NoContent=204]="NoContent",e[e.ResetContent=205]="ResetContent",e[e.PartialContent=206]="PartialContent",e[e.MultiStatus=207]="MultiStatus",e[e.AlreadyReported=208]="AlreadyReported",e[e.ImUsed=226]="ImUsed",e[e.MultipleChoices=300]="MultipleChoices",e[e.MovedPermanently=301]="MovedPermanently",e[e.Found=302]="Found",e[e.SeeOther=303]="SeeOther",e[e.NotModified=304]="NotModified",e[e.UseProxy=305]="UseProxy",e[e.Unused=306]="Unused",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e[e.BadRequest=400]="BadRequest",e[e.Unauthorized=401]="Unauthorized",e[e.PaymentRequired=402]="PaymentRequired",e[e.Forbidden=403]="Forbidden",e[e.NotFound=404]="NotFound",e[e.MethodNotAllowed=405]="MethodNotAllowed",e[e.NotAcceptable=406]="NotAcceptable",e[e.ProxyAuthenticationRequired=407]="ProxyAuthenticationRequired",e[e.RequestTimeout=408]="RequestTimeout",e[e.Conflict=409]="Conflict",e[e.Gone=410]="Gone",e[e.LengthRequired=411]="LengthRequired",e[e.PreconditionFailed=412]="PreconditionFailed",e[e.PayloadTooLarge=413]="PayloadTooLarge",e[e.UriTooLong=414]="UriTooLong",e[e.UnsupportedMediaType=415]="UnsupportedMediaType",e[e.RangeNotSatisfiable=416]="RangeNotSatisfiable",e[e.ExpectationFailed=417]="ExpectationFailed",e[e.ImATeapot=418]="ImATeapot",e[e.MisdirectedRequest=421]="MisdirectedRequest",e[e.UnprocessableEntity=422]="UnprocessableEntity",e[e.Locked=423]="Locked",e[e.FailedDependency=424]="FailedDependency",e[e.TooEarly=425]="TooEarly",e[e.UpgradeRequired=426]="UpgradeRequired",e[e.PreconditionRequired=428]="PreconditionRequired",e[e.TooManyRequests=429]="TooManyRequests",e[e.RequestHeaderFieldsTooLarge=431]="RequestHeaderFieldsTooLarge",e[e.UnavailableForLegalReasons=451]="UnavailableForLegalReasons",e[e.InternalServerError=500]="InternalServerError",e[e.NotImplemented=501]="NotImplemented",e[e.BadGateway=502]="BadGateway",e[e.ServiceUnavailable=503]="ServiceUnavailable",e[e.GatewayTimeout=504]="GatewayTimeout",e[e.HttpVersionNotSupported=505]="HttpVersionNotSupported",e[e.VariantAlsoNegotiates=506]="VariantAlsoNegotiates",e[e.InsufficientStorage=507]="InsufficientStorage",e[e.LoopDetected=508]="LoopDetected",e[e.NotExtended=510]="NotExtended",e[e.NetworkAuthenticationRequired=511]="NetworkAuthenticationRequired",e}(Vo||{});function gd(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var oS=(()=>{let t=class t{constructor(n){this.handler=n}request(n,o,i={}){let s;if(n instanceof ko)s=n;else{let c;i.headers instanceof It?c=i.headers:c=new It(i.headers);let l;i.params&&(i.params instanceof on?l=i.params:l=new on({fromObject:i.params})),s=new ko(n,o,i.body!==void 0?i.body:null,{headers:c,context:i.context,params:l,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let a=b(s).pipe(xt(c=>this.handler.handle(c)));if(n instanceof ko||i.observe==="events")return a;let u=a.pipe(xe(c=>c instanceof kn));switch(i.observe||"body"){case"body":switch(s.responseType){case"arraybuffer":return u.pipe(F(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return c.body}));case"blob":return u.pipe(F(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new Error("Response is not a Blob.");return c.body}));case"text":return u.pipe(F(c=>{if(c.body!==null&&typeof c.body!="string")throw new Error("Response is not a string.");return c.body}));case"json":default:return u.pipe(F(c=>c.body))}case"response":return u;default:throw new Error(`Unreachable: unhandled observe type ${i.observe}}`)}}delete(n,o={}){return this.request("DELETE",n,o)}get(n,o={}){return this.request("GET",n,o)}head(n,o={}){return this.request("HEAD",n,o)}jsonp(n,o){return this.request("JSONP",n,{params:new on().append(o,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,o={}){return this.request("OPTIONS",n,o)}patch(n,o,i={}){return this.request("PATCH",n,gd(i,o))}post(n,o,i={}){return this.request("POST",n,gd(i,o))}put(n,o,i={}){return this.request("PUT",n,gd(i,o))}};t.\u0275fac=function(o){return new(o||t)(I(Lo))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),iS=/^\)\]\}',?\n/,sS="X-Request-URL";function Wv(e){if(e.url)return e.url;let t=sS.toLocaleLowerCase();return e.headers.get(t)}var md=(()=>{let t=class t{constructor(){var n,o;this.fetchImpl=(o=(n=g(Dd,{optional:!0}))==null?void 0:n.fetch)!=null?o:fetch.bind(globalThis),this.ngZone=g(W)}handle(n){return new k(o=>{let i=new AbortController;return this.doRequest(n,i.signal,o).then(wd,s=>o.error(new rn({error:s}))),()=>i.abort()})}doRequest(n,o,i){return $n(this,null,function*(){var p,y,m,v;let s=this.createRequestInit(n),a;try{let A=this.fetchImpl(n.urlWithParams,w({signal:o},s));aS(A),i.next({type:sn.Sent}),a=yield A}catch(A){i.error(new rn({error:A,status:(p=A.status)!=null?p:0,statusText:A.statusText,url:n.urlWithParams,headers:A.headers}));return}let u=new It(a.headers),c=a.statusText,l=(y=Wv(a))!=null?y:n.urlWithParams,d=a.status,f=null;if(n.reportProgress&&i.next(new Ta({headers:u,status:d,statusText:c,url:l})),a.body){let A=a.headers.get("content-length"),q=[],O=a.body.getReader(),re=0,Oe,ve,Tt=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>$n(this,null,function*(){for(;;){let{done:fn,value:Zr}=yield O.read();if(fn)break;if(q.push(Zr),re+=Zr.length,n.reportProgress){ve=n.responseType==="text"?(ve!=null?ve:"")+(Oe!=null?Oe:Oe=new TextDecoder).decode(Zr,{stream:!0}):void 0;let mf=()=>i.next({type:sn.DownloadProgress,total:A?+A:void 0,loaded:re,partialText:ve});Tt?Tt.run(mf):mf()}}}));let Gr=this.concatChunks(q,re);try{let fn=(m=a.headers.get("Content-Type"))!=null?m:"";f=this.parseBody(n,Gr,fn)}catch(fn){i.error(new rn({error:fn,headers:new It(a.headers),status:a.status,statusText:a.statusText,url:(v=Wv(a))!=null?v:n.urlWithParams}));return}}d===0&&(d=f?Vo.Ok:0),d>=200&&d<300?(i.next(new kn({body:f,headers:u,status:d,statusText:c,url:l})),i.complete()):i.error(new rn({error:f,headers:u,status:d,statusText:c,url:l}))})}parseBody(n,o,i){switch(n.responseType){case"json":let s=new TextDecoder().decode(o).replace(iS,"");return s===""?null:JSON.parse(s);case"text":return new TextDecoder().decode(o);case"blob":return new Blob([o],{type:i});case"arraybuffer":return o.buffer}}createRequestInit(n){var s;let o={},i=n.withCredentials?"include":void 0;if(n.headers.forEach((a,u)=>o[a]=u.join(",")),(s=o.Accept)!=null||(o.Accept="application/json, text/plain, */*"),!o["Content-Type"]){let a=n.detectContentTypeHeader();a!==null&&(o["Content-Type"]=a)}return{body:n.serializeBody(),method:n.method,headers:o,credentials:i}}concatChunks(n,o){let i=new Uint8Array(o),s=0;for(let a of n)i.set(a,s),s+=a.length;return i}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),Dd=class{};function wd(){}function aS(e){e.then(wd,wd)}function uS(e,t){return t(e)}function cS(e,t,r){return(n,o)=>at(r,()=>t(n,i=>e(i,o)))}var Ed=new C(""),ny=new C(""),ry=new C("");var Gv=(()=>{let t=class t extends Lo{constructor(n,o){super(),this.backend=n,this.injector=o,this.chain=null,this.pendingTasks=g(Ar);let i=g(ry,{optional:!0});this.backend=i!=null?i:n}handle(n){if(this.chain===null){let i=Array.from(new Set([...this.injector.get(Ed),...this.injector.get(ny,[])]));this.chain=i.reduceRight((s,a)=>cS(s,a,this.injector),uS)}let o=this.pendingTasks.add();return this.chain(n,i=>this.backend.handle(i)).pipe(Ht(()=>this.pendingTasks.remove(o)))}};t.\u0275fac=function(o){return new(o||t)(I(jo),I(De))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})();var lS=/^\)\]\}',?\n/;function dS(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var Zv=(()=>{let t=class t{constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new D(-2800,!1);let o=this.xhrFactory;return(o.\u0275loadImpl?Q(o.\u0275loadImpl()):b(null)).pipe(Ie(()=>new k(s=>{let a=o.build();if(a.open(n.method,n.urlWithParams),n.withCredentials&&(a.withCredentials=!0),n.headers.forEach((m,v)=>a.setRequestHeader(m,v.join(","))),n.headers.has("Accept")||a.setRequestHeader("Accept","application/json, text/plain, */*"),!n.headers.has("Content-Type")){let m=n.detectContentTypeHeader();m!==null&&a.setRequestHeader("Content-Type",m)}if(n.responseType){let m=n.responseType.toLowerCase();a.responseType=m!=="json"?m:"text"}let u=n.serializeBody(),c=null,l=()=>{if(c!==null)return c;let m=a.statusText||"OK",v=new It(a.getAllResponseHeaders()),A=dS(a)||n.url;return c=new Ta({headers:v,status:a.status,statusText:m,url:A}),c},d=()=>{let{headers:m,status:v,statusText:A,url:q}=l(),O=null;v!==Vo.NoContent&&(O=typeof a.response>"u"?a.responseText:a.response),v===0&&(v=O?Vo.Ok:0);let re=v>=200&&v<300;if(n.responseType==="json"&&typeof O=="string"){let Oe=O;O=O.replace(lS,"");try{O=O!==""?JSON.parse(O):null}catch(ve){O=Oe,re&&(re=!1,O={error:ve,text:O})}}re?(s.next(new kn({body:O,headers:m,status:v,statusText:A,url:q||void 0})),s.complete()):s.error(new rn({error:O,headers:m,status:v,statusText:A,url:q||void 0}))},f=m=>{let{url:v}=l(),A=new rn({error:m,status:a.status||0,statusText:a.statusText||"Unknown Error",url:v||void 0});s.error(A)},h=!1,p=m=>{h||(s.next(l()),h=!0);let v={type:sn.DownloadProgress,loaded:m.loaded};m.lengthComputable&&(v.total=m.total),n.responseType==="text"&&a.responseText&&(v.partialText=a.responseText),s.next(v)},y=m=>{let v={type:sn.UploadProgress,loaded:m.loaded};m.lengthComputable&&(v.total=m.total),s.next(v)};return a.addEventListener("load",d),a.addEventListener("error",f),a.addEventListener("timeout",f),a.addEventListener("abort",f),n.reportProgress&&(a.addEventListener("progress",p),u!==null&&a.upload&&a.upload.addEventListener("progress",y)),a.send(u),s.next({type:sn.Sent}),()=>{a.removeEventListener("error",f),a.removeEventListener("abort",f),a.removeEventListener("load",d),a.removeEventListener("timeout",f),n.reportProgress&&(a.removeEventListener("progress",p),u!==null&&a.upload&&a.upload.removeEventListener("progress",y)),a.readyState!==a.DONE&&a.abort()}})))}};t.\u0275fac=function(o){return new(o||t)(I(Fr))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),oy=new C(""),fS="XSRF-TOKEN",hS=new C("",{providedIn:"root",factory:()=>fS}),pS="X-XSRF-TOKEN",gS=new C("",{providedIn:"root",factory:()=>pS}),Sa=class{},mS=(()=>{let t=class t{constructor(n,o,i){this.doc=n,this.platform=o,this.cookieName=i,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Ca(n,this.cookieName),this.lastCookieString=n),this.lastToken}};t.\u0275fac=function(o){return new(o||t)(I(me),I(We),I(hS))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})();function vS(e,t){let r=e.url.toLowerCase();if(!g(oy)||e.method==="GET"||e.method==="HEAD"||r.startsWith("http://")||r.startsWith("https://"))return t(e);let n=g(Sa).getToken(),o=g(gS);return n!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,n)})),t(e)}var Id=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Id||{});function iy(e,t){return{\u0275kind:e,\u0275providers:t}}function kL(...e){let t=[oS,Zv,Gv,{provide:Lo,useExisting:Gv},{provide:jo,useExisting:Zv},{provide:Ed,useValue:vS,multi:!0},{provide:oy,useValue:!0},{provide:Sa,useClass:mS}];for(let r of e)t.push(...r.\u0275providers);return Jt(t)}function LL(e){return iy(Id.Interceptors,e.map(t=>({provide:Ed,useValue:t,multi:!0})))}function jL(){return iy(Id.Fetch,[md,{provide:jo,useExisting:md},{provide:ry,useExisting:md}])}var Yv="b",Qv="h",Kv="s",Jv="st",Xv="u",ey="rt",Ma=new C(""),yS=["GET","HEAD"];function DS(e,t){var f;let d=g(Ma),{isCacheActive:r}=d,n=vf(d,["isCacheActive"]),{transferCache:o,method:i}=e;if(!r||i==="POST"&&!n.includePostRequests&&!o||i!=="POST"&&!yS.includes(i)||o===!1||((f=n.filter)==null?void 0:f.call(n,e))===!1)return t(e);let s=g(en),a=ES(e),u=s.get(a,null),c=n.includeHeaders;if(typeof o=="object"&&o.includeHeaders&&(c=o.includeHeaders),u){let{[Yv]:h,[ey]:p,[Qv]:y,[Kv]:m,[Jv]:v,[Xv]:A}=u,q=h;switch(p){case"arraybuffer":q=new TextEncoder().encode(h).buffer;break;case"blob":q=new Blob([h]);break}let O=new It(y);return b(new kn({body:q,headers:O,status:m,statusText:v,url:A}))}let l=Fo(g(We));return t(e).pipe(ae(h=>{h instanceof kn&&l&&s.set(a,{[Yv]:h.body,[Qv]:wS(h.headers,c),[Kv]:h.status,[Jv]:h.statusText,[Xv]:h.url||"",[ey]:e.responseType})}))}function wS(e,t){if(!t)return{};let r={};for(let n of t){let o=e.getAll(n);o!==null&&(r[n]=o)}return r}function ty(e){return[...e.keys()].sort().map(t=>`${t}=${e.getAll(t)}`).join("&")}function ES(e){let{params:t,method:r,responseType:n,url:o}=e,i=ty(t),s=e.serializeBody();s instanceof URLSearchParams?s=ty(s):typeof s!="string"&&(s="");let a=[r,n,o,s,i].join("|"),u=IS(a);return u}function IS(e){let t=0;for(let r of e)t=Math.imul(31,t)+r.charCodeAt(0)<<0;return t+=**********,t.toString()}function sy(e){return[{provide:Ma,useFactory:()=>(tn("NgHttpTransferCache"),w({isCacheActive:!0},e))},{provide:ny,useValue:DS,multi:!0,deps:[en,Ma]},{provide:Pn,multi:!0,useFactory:()=>{let t=g(Lt),r=g(Ma);return()=>{Zl(t).then(()=>{r.isCacheActive=!1})}}}]}var Md=class extends wa{constructor(){super(...arguments),this.supportsDOMEvents=!0}},Td=class e extends Md{static makeCurrent(){Nv(new e)}onAndCancel(t,r,n){return t.addEventListener(r,n),()=>{t.removeEventListener(r,n)}}dispatchEvent(t,r){t.dispatchEvent(r)}remove(t){t.parentNode&&t.parentNode.removeChild(t)}createElement(t,r){return r=r||this.getDefaultDocument(),r.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,r){return r==="window"?window:r==="document"?t:r==="body"?t.body:null}getBaseHref(t){let r=CS();return r==null?null:bS(r)}resetBaseElement(){Uo=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Ca(document.cookie,t)}},Uo=null;function CS(){return Uo=Uo||document.querySelector("base"),Uo?Uo.getAttribute("href"):null}function bS(e){return new URL(e,document.baseURI).pathname}var MS=(()=>{let t=class t{build(){return new XMLHttpRequest}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),Sd=new C(""),ly=(()=>{let t=class t{constructor(n,o){this._zone=o,this._eventNameToPlugin=new Map,n.forEach(i=>{i.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,o,i){return this._findPluginFor(o).addEventListener(n,o,i)}getZone(){return this._zone}_findPluginFor(n){let o=this._eventNameToPlugin.get(n);if(o)return o;if(o=this._plugins.find(s=>s.supports(n)),!o)throw new D(5101,!1);return this._eventNameToPlugin.set(n,o),o}};t.\u0275fac=function(o){return new(o||t)(I(Sd),I(W))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),xa=class{constructor(t){this._doc=t}},Cd="ng-app-id",dy=(()=>{let t=class t{constructor(n,o,i,s={}){this.doc=n,this.appId=o,this.nonce=i,this.platformId=s,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Fo(s),this.resetHostNodes()}addStyles(n){for(let o of n)this.changeUsageCount(o,1)===1&&this.onStyleAdded(o)}removeStyles(n){for(let o of n)this.changeUsageCount(o,-1)<=0&&this.onStyleRemoved(o)}ngOnDestroy(){let n=this.styleNodesInDOM;n&&(n.forEach(o=>o.remove()),n.clear());for(let o of this.getAllStyles())this.onStyleRemoved(o);this.resetHostNodes()}addHost(n){this.hostNodes.add(n);for(let o of this.getAllStyles())this.addStyleToHost(n,o)}removeHost(n){this.hostNodes.delete(n)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(n){for(let o of this.hostNodes)this.addStyleToHost(o,n)}onStyleRemoved(n){var i,s;let o=this.styleRef;(s=(i=o.get(n))==null?void 0:i.elements)==null||s.forEach(a=>a.remove()),o.delete(n)}collectServerRenderedStyles(){var o;let n=(o=this.doc.head)==null?void 0:o.querySelectorAll(`style[${Cd}="${this.appId}"]`);if(n!=null&&n.length){let i=new Map;return n.forEach(s=>{s.textContent!=null&&i.set(s.textContent,s)}),i}return null}changeUsageCount(n,o){let i=this.styleRef;if(i.has(n)){let s=i.get(n);return s.usage+=o,s.usage}return i.set(n,{usage:o,elements:[]}),o}getStyleElement(n,o){let i=this.styleNodesInDOM,s=i==null?void 0:i.get(o);if((s==null?void 0:s.parentNode)===n)return i.delete(o),s.removeAttribute(Cd),s;{let a=this.doc.createElement("style");return this.nonce&&a.setAttribute("nonce",this.nonce),a.textContent=o,this.platformIsServer&&a.setAttribute(Cd,this.appId),n.appendChild(a),a}}addStyleToHost(n,o){var u;let i=this.getStyleElement(n,o),s=this.styleRef,a=(u=s.get(o))==null?void 0:u.elements;a?a.push(i):s.set(o,{elements:[i],usage:1})}resetHostNodes(){let n=this.hostNodes;n.clear(),n.add(this.doc.head)}};t.\u0275fac=function(o){return new(o||t)(I(me),I(Hs),I(fl,8),I(We))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),bd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},_d=/%COMP%/g,fy="%COMP%",TS=`_nghost-${fy}`,SS=`_ngcontent-${fy}`,xS=!0,AS=new C("",{providedIn:"root",factory:()=>xS});function _S(e){return SS.replace(_d,e)}function NS(e){return TS.replace(_d,e)}function hy(e,t){return t.map(r=>r.replace(_d,e))}var ay=(()=>{let t=class t{constructor(n,o,i,s,a,u,c,l=null){this.eventManager=n,this.sharedStylesHost=o,this.appId=i,this.removeStylesOnCompDestroy=s,this.doc=a,this.platformId=u,this.ngZone=c,this.nonce=l,this.rendererByCompId=new Map,this.platformIsServer=Fo(u),this.defaultRenderer=new Bo(n,a,c,this.platformIsServer)}createRenderer(n,o){if(!n||!o)return this.defaultRenderer;this.platformIsServer&&o.encapsulation===ot.ShadowDom&&(o=te(w({},o),{encapsulation:ot.Emulated}));let i=this.getOrCreateRenderer(n,o);return i instanceof Aa?i.applyToHost(n):i instanceof Ho&&i.applyStyles(),i}getOrCreateRenderer(n,o){let i=this.rendererByCompId,s=i.get(o.id);if(!s){let a=this.doc,u=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,d=this.removeStylesOnCompDestroy,f=this.platformIsServer;switch(o.encapsulation){case ot.Emulated:s=new Aa(c,l,o,this.appId,d,a,u,f);break;case ot.ShadowDom:return new xd(c,l,n,o,a,u,this.nonce,f);default:s=new Ho(c,l,o,d,a,u,f);break}i.set(o.id,s)}return s}ngOnDestroy(){this.rendererByCompId.clear()}};t.\u0275fac=function(o){return new(o||t)(I(ly),I(dy),I(Hs),I(AS),I(me),I(We),I(W),I(fl))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),Bo=class{constructor(t,r,n,o){this.eventManager=t,this.doc=r,this.ngZone=n,this.platformIsServer=o,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(t,r){return r?this.doc.createElementNS(bd[r]||r,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,r){(uy(t)?t.content:t).appendChild(r)}insertBefore(t,r,n){t&&(uy(t)?t.content:t).insertBefore(r,n)}removeChild(t,r){t&&t.removeChild(r)}selectRootElement(t,r){let n=typeof t=="string"?this.doc.querySelector(t):t;if(!n)throw new D(-5104,!1);return r||(n.textContent=""),n}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,r,n,o){if(o){r=o+":"+r;let i=bd[o];i?t.setAttributeNS(i,r,n):t.setAttribute(r,n)}else t.setAttribute(r,n)}removeAttribute(t,r,n){if(n){let o=bd[n];o?t.removeAttributeNS(o,r):t.removeAttribute(`${n}:${r}`)}else t.removeAttribute(r)}addClass(t,r){t.classList.add(r)}removeClass(t,r){t.classList.remove(r)}setStyle(t,r,n,o){o&(wt.DashCase|wt.Important)?t.style.setProperty(r,n,o&wt.Important?"important":""):t.style[r]=n}removeStyle(t,r,n){n&wt.DashCase?t.style.removeProperty(r):t.style[r]=""}setProperty(t,r,n){t!=null&&(t[r]=n)}setValue(t,r){t.nodeValue=r}listen(t,r,n){if(typeof t=="string"&&(t=Pr().getGlobalEventTarget(this.doc,t),!t))throw new Error(`Unsupported event target ${t} for event ${r}`);return this.eventManager.addEventListener(t,r,this.decoratePreventDefault(n))}decoratePreventDefault(t){return r=>{if(r==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(r)):t(r))===!1&&r.preventDefault()}}};function uy(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var xd=class extends Bo{constructor(t,r,n,o,i,s,a,u){super(t,i,s,u),this.sharedStylesHost=r,this.hostEl=n,this.shadowRoot=n.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let c=hy(o.id,o.styles);for(let l of c){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=l,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,r){return super.appendChild(this.nodeOrShadowRoot(t),r)}insertBefore(t,r,n){return super.insertBefore(this.nodeOrShadowRoot(t),r,n)}removeChild(t,r){return super.removeChild(this.nodeOrShadowRoot(t),r)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Ho=class extends Bo{constructor(t,r,n,o,i,s,a,u){super(t,i,s,a),this.sharedStylesHost=r,this.removeStylesOnCompDestroy=o,this.styles=u?hy(u,n.styles):n.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},Aa=class extends Ho{constructor(t,r,n,o,i,s,a,u){let c=o+"-"+n.id;super(t,r,n,i,s,a,u,c),this.contentAttr=_S(c),this.hostAttr=NS(c)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,r){let n=super.createElement(t,r);return super.setAttribute(n,this.contentAttr,""),n}},RS=(()=>{let t=class t extends xa{constructor(n){super(n)}supports(n){return!0}addEventListener(n,o,i){return n.addEventListener(o,i,!1),()=>this.removeEventListener(n,o,i)}removeEventListener(n,o,i){return n.removeEventListener(o,i)}};t.\u0275fac=function(o){return new(o||t)(I(me))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})(),cy=["alt","control","meta","shift"],OS={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},PS={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},FS=(()=>{let t=class t extends xa{constructor(n){super(n)}supports(n){return t.parseEventName(n)!=null}addEventListener(n,o,i){let s=t.parseEventName(o),a=t.eventCallback(s.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Pr().onAndCancel(n,s.domEventName,a))}static parseEventName(n){let o=n.toLowerCase().split("."),i=o.shift();if(o.length===0||!(i==="keydown"||i==="keyup"))return null;let s=t._normalizeKey(o.pop()),a="",u=o.indexOf("code");if(u>-1&&(o.splice(u,1),a="code."),cy.forEach(l=>{let d=o.indexOf(l);d>-1&&(o.splice(d,1),a+=l+".")}),a+=s,o.length!=0||s.length===0)return null;let c={};return c.domEventName=i,c.fullKey=a,c}static matchEventFullKeyCode(n,o){let i=OS[n.key]||n.key,s="";return o.indexOf("code.")>-1&&(i=n.code,s="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),cy.forEach(a=>{if(a!==i){let u=PS[a];u(n)&&(s+=a+".")}}),s+=i,s===o)}static eventCallback(n,o,i){return s=>{t.matchEventFullKeyCode(s,n)&&i.runGuarded(()=>o(s))}}static _normalizeKey(n){return n==="esc"?"escape":n}};t.\u0275fac=function(o){return new(o||t)(I(me))},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})();function KL(e,t){return hv(w({rootComponent:e},kS(t)))}function kS(e){var t;return{appProviders:[...US,...(t=e==null?void 0:e.providers)!=null?t:[]],platformProviders:VS}}function LS(){Td.makeCurrent()}function jS(){return new st}function $S(){return fg(document),document}var VS=[{provide:We,useValue:pd},{provide:dl,useValue:LS,multi:!0},{provide:me,useFactory:$S,deps:[]}];var US=[{provide:Fs,useValue:"root"},{provide:st,useFactory:jS,deps:[]},{provide:Sd,useClass:RS,multi:!0,deps:[me,W,We]},{provide:Sd,useClass:FS,multi:!0,deps:[me]},ay,dy,ly,{provide:Eo,useExisting:ay},{provide:Fr,useClass:MS,deps:[]},[]];var py=(()=>{let t=class t{constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}};t.\u0275fac=function(o){return new(o||t)(I(me))},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();var BS=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:function(o){let i=null;return o?i=new(o||t):i=I(HS),i},providedIn:"root"});let e=t;return e})(),HS=(()=>{let t=class t extends BS{constructor(n){super(),this._doc=n}sanitize(n,o){if(o==null)return null;switch(n){case dt.NONE:return o;case dt.HTML:return Ft(o,"HTML")?Ge(o):yl(this._doc,String(o)).toString();case dt.STYLE:return Ft(o,"Style")?Ge(o):o;case dt.SCRIPT:if(Ft(o,"Script"))return Ge(o);throw new D(5200,!1);case dt.URL:return Ft(o,"URL")?Ge(o):Gs(String(o));case dt.RESOURCE_URL:if(Ft(o,"ResourceURL"))return Ge(o);throw new D(5201,!1);default:throw new D(5202,!1)}}bypassSecurityTrustHtml(n){return Dg(n)}bypassSecurityTrustStyle(n){return wg(n)}bypassSecurityTrustScript(n){return Eg(n)}bypassSecurityTrustUrl(n){return Ig(n)}bypassSecurityTrustResourceUrl(n){return Cg(n)}};t.\u0275fac=function(o){return new(o||t)(I(me))},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Ad=function(e){return e[e.NoHttpTransferCache=0]="NoHttpTransferCache",e[e.HttpTransferCacheOptions=1]="HttpTransferCacheOptions",e}(Ad||{});function JL(...e){let t=[],r=new Set,n=r.has(Ad.HttpTransferCacheOptions);for(let{\u0275providers:o,\u0275kind:i}of e)r.add(i),o.length&&t.push(o);return Jt([[],gv(),r.has(Ad.NoHttpTransferCache)||n?[]:sy({}),t])}var R="primary",ii=Symbol("RouteTitle"),Fd=class{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let r=this.params[t];return Array.isArray(r)?r[0]:r}return null}getAll(t){if(this.has(t)){let r=this.params[t];return Array.isArray(r)?r:[r]}return[]}get keys(){return Object.keys(this.params)}};function Br(e){return new Fd(e)}function qS(e,t,r){let n=r.path.split("/");if(n.length>e.length||r.pathMatch==="full"&&(t.hasChildren()||n.length<e.length))return null;let o={};for(let i=0;i<n.length;i++){let s=n[i],a=e[i];if(s.startsWith(":"))o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,n.length),posParams:o}}function WS(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;++r)if(!Ct(e[r],t[r]))return!1;return!0}function Ct(e,t){let r=e?kd(e):void 0,n=t?kd(t):void 0;if(!r||!n||r.length!=n.length)return!1;let o;for(let i=0;i<r.length;i++)if(o=r[i],!by(e[o],t[o]))return!1;return!0}function kd(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function by(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let r=[...e].sort(),n=[...t].sort();return r.every((o,i)=>n[i]===o)}else return e===t}function My(e){return e.length>0?e[e.length-1]:null}function dn(e){return gu(e)?e:Nr(e)?Q(Promise.resolve(e)):b(e)}var GS={exact:Sy,subset:xy},Ty={exact:ZS,subset:YS,ignored:()=>!0};function gy(e,t,r){return GS[r.paths](e.root,t.root,r.matrixParams)&&Ty[r.queryParams](e.queryParams,t.queryParams)&&!(r.fragment==="exact"&&e.fragment!==t.fragment)}function ZS(e,t){return Ct(e,t)}function Sy(e,t,r){if(!jn(e.segments,t.segments)||!Ra(e.segments,t.segments,r)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let n in t.children)if(!e.children[n]||!Sy(e.children[n],t.children[n],r))return!1;return!0}function YS(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(r=>by(e[r],t[r]))}function xy(e,t,r){return Ay(e,t,t.segments,r)}function Ay(e,t,r,n){if(e.segments.length>r.length){let o=e.segments.slice(0,r.length);return!(!jn(o,r)||t.hasChildren()||!Ra(o,r,n))}else if(e.segments.length===r.length){if(!jn(e.segments,r)||!Ra(e.segments,r,n))return!1;for(let o in t.children)if(!e.children[o]||!xy(e.children[o],t.children[o],n))return!1;return!0}else{let o=r.slice(0,e.segments.length),i=r.slice(e.segments.length);return!jn(e.segments,o)||!Ra(e.segments,o,n)||!e.children[R]?!1:Ay(e.children[R],t,i,n)}}function Ra(e,t,r){return t.every((n,o)=>Ty[r](e[o].parameters,n.parameters))}var an=class{constructor(t=new H([],{}),r={},n=null){this.root=t,this.queryParams=r,this.fragment=n}get queryParamMap(){var t;return(t=this._queryParamMap)!=null||(this._queryParamMap=Br(this.queryParams)),this._queryParamMap}toString(){return JS.serialize(this)}},H=class{constructor(t,r){this.segments=t,this.children=r,this.parent=null,Object.values(r).forEach(n=>n.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Oa(this)}},Ln=class{constructor(t,r){this.path=t,this.parameters=r}get parameterMap(){var t;return(t=this._parameterMap)!=null||(this._parameterMap=Br(this.parameters)),this._parameterMap}toString(){return Ny(this)}};function QS(e,t){return jn(e,t)&&e.every((r,n)=>Ct(r.parameters,t[n].parameters))}function jn(e,t){return e.length!==t.length?!1:e.every((r,n)=>r.path===t[n].path)}function KS(e,t){let r=[];return Object.entries(e.children).forEach(([n,o])=>{n===R&&(r=r.concat(t(o,n)))}),Object.entries(e.children).forEach(([n,o])=>{n!==R&&(r=r.concat(t(o,n)))}),r}var si=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:()=>new Qo,providedIn:"root"});let e=t;return e})(),Qo=class{parse(t){let r=new jd(t);return new an(r.parseRootSegment(),r.parseQueryParams(),r.parseFragment())}serialize(t){let r=`/${zo(t.root,!0)}`,n=tx(t.queryParams),o=typeof t.fragment=="string"?`#${XS(t.fragment)}`:"";return`${r}${n}${o}`}},JS=new Qo;function Oa(e){return e.segments.map(t=>Ny(t)).join("/")}function zo(e,t){if(!e.hasChildren())return Oa(e);if(t){let r=e.children[R]?zo(e.children[R],!1):"",n=[];return Object.entries(e.children).forEach(([o,i])=>{o!==R&&n.push(`${o}:${zo(i,!1)}`)}),n.length>0?`${r}(${n.join("//")})`:r}else{let r=KS(e,(n,o)=>o===R?[zo(e.children[R],!1)]:[`${o}:${zo(n,!1)}`]);return Object.keys(e.children).length===1&&e.children[R]!=null?`${Oa(e)}/${r[0]}`:`${Oa(e)}/(${r.join("//")})`}}function _y(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function _a(e){return _y(e).replace(/%3B/gi,";")}function XS(e){return encodeURI(e)}function Ld(e){return _y(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Pa(e){return decodeURIComponent(e)}function my(e){return Pa(e.replace(/\+/g,"%20"))}function Ny(e){return`${Ld(e.path)}${ex(e.parameters)}`}function ex(e){return Object.entries(e).map(([t,r])=>`;${Ld(t)}=${Ld(r)}`).join("")}function tx(e){let t=Object.entries(e).map(([r,n])=>Array.isArray(n)?n.map(o=>`${_a(r)}=${_a(o)}`).join("&"):`${_a(r)}=${_a(n)}`).filter(r=>r);return t.length?`?${t.join("&")}`:""}var nx=/^[^\/()?;#]+/;function Nd(e){let t=e.match(nx);return t?t[0]:""}var rx=/^[^\/()?;=#]+/;function ox(e){let t=e.match(rx);return t?t[0]:""}var ix=/^[^=?&#]+/;function sx(e){let t=e.match(ix);return t?t[0]:""}var ax=/^[^&#]+/;function ux(e){let t=e.match(ax);return t?t[0]:""}var jd=class{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new H([],{}):new H([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let r={};this.peekStartsWith("/(")&&(this.capture("/"),r=this.parseParens(!0));let n={};return this.peekStartsWith("(")&&(n=this.parseParens(!1)),(t.length>0||Object.keys(r).length>0)&&(n[R]=new H(t,r)),n}parseSegment(){let t=Nd(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new D(4009,!1);return this.capture(t),new Ln(Pa(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let r=ox(this.remaining);if(!r)return;this.capture(r);let n="";if(this.consumeOptional("=")){let o=Nd(this.remaining);o&&(n=o,this.capture(n))}t[Pa(r)]=Pa(n)}parseQueryParam(t){let r=sx(this.remaining);if(!r)return;this.capture(r);let n="";if(this.consumeOptional("=")){let s=ux(this.remaining);s&&(n=s,this.capture(n))}let o=my(r),i=my(n);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let r={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let n=Nd(this.remaining),o=this.remaining[n.length];if(o!=="/"&&o!==")"&&o!==";")throw new D(4010,!1);let i;n.indexOf(":")>-1?(i=n.slice(0,n.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=R);let s=this.parseChildren();r[i]=Object.keys(s).length===1?s[R]:new H([],s),this.consumeOptional("//")}return r}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new D(4011,!1)}};function Ry(e){return e.segments.length>0?new H([],{[R]:e}):e}function Oy(e){let t={};for(let[n,o]of Object.entries(e.children)){let i=Oy(o);if(n===R&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[n]=i)}let r=new H(e.segments,t);return cx(r)}function cx(e){if(e.numberOfChildren===1&&e.children[R]){let t=e.children[R];return new H(e.segments.concat(t.segments),t.children)}return e}function Hr(e){return e instanceof an}function lx(e,t,r=null,n=null){let o=Py(e);return Fy(o,t,r,n)}function Py(e){let t;function r(i){let s={};for(let u of i.children){let c=r(u);s[u.outlet]=c}let a=new H(i.url,s);return i===e&&(t=a),a}let n=r(e.root),o=Ry(n);return t!=null?t:o}function Fy(e,t,r,n){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Rd(o,o,o,r,n);let i=dx(t);if(i.toRoot())return Rd(o,o,new H([],{}),r,n);let s=fx(i,o,e),a=s.processChildren?Go(s.segmentGroup,s.index,i.commands):Ly(s.segmentGroup,s.index,i.commands);return Rd(o,s.segmentGroup,a,r,n)}function Fa(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Ko(e){return typeof e=="object"&&e!=null&&e.outlets}function Rd(e,t,r,n,o){let i={};n&&Object.entries(n).forEach(([u,c])=>{i[u]=Array.isArray(c)?c.map(l=>`${l}`):`${c}`});let s;e===t?s=r:s=ky(e,t,r);let a=Ry(Oy(s));return new an(a,i,o)}function ky(e,t,r){let n={};return Object.entries(e.children).forEach(([o,i])=>{i===t?n[o]=r:n[o]=ky(i,t,r)}),new H(e.segments,n)}var ka=class{constructor(t,r,n){if(this.isAbsolute=t,this.numberOfDoubleDots=r,this.commands=n,t&&n.length>0&&Fa(n[0]))throw new D(4003,!1);let o=n.find(Ko);if(o&&o!==My(n))throw new D(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function dx(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new ka(!0,0,e);let t=0,r=!1,n=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([u,c])=>{a[u]=typeof c=="string"?c.split("/"):c}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,u)=>{u==0&&a==="."||(u==0&&a===""?r=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new ka(r,t,n)}var Vr=class{constructor(t,r,n){this.segmentGroup=t,this.processChildren=r,this.index=n}};function fx(e,t,r){if(e.isAbsolute)return new Vr(t,!0,0);if(!r)return new Vr(t,!1,NaN);if(r.parent===null)return new Vr(r,!0,0);let n=Fa(e.commands[0])?0:1,o=r.segments.length-1+n;return hx(r,o,e.numberOfDoubleDots)}function hx(e,t,r){let n=e,o=t,i=r;for(;i>o;){if(i-=o,n=n.parent,!n)throw new D(4005,!1);o=n.segments.length}return new Vr(n,!1,o-i)}function px(e){return Ko(e[0])?e[0].outlets:{[R]:e}}function Ly(e,t,r){if(e!=null||(e=new H([],{})),e.segments.length===0&&e.hasChildren())return Go(e,t,r);let n=gx(e,t,r),o=r.slice(n.commandIndex);if(n.match&&n.pathIndex<e.segments.length){let i=new H(e.segments.slice(0,n.pathIndex),{});return i.children[R]=new H(e.segments.slice(n.pathIndex),e.children),Go(i,0,o)}else return n.match&&o.length===0?new H(e.segments,{}):n.match&&!e.hasChildren()?$d(e,t,r):n.match?Go(e,0,o):$d(e,t,r)}function Go(e,t,r){if(r.length===0)return new H(e.segments,{});{let n=px(r),o={};if(Object.keys(n).some(i=>i!==R)&&e.children[R]&&e.numberOfChildren===1&&e.children[R].segments.length===0){let i=Go(e.children[R],t,r);return new H(e.segments,i.children)}return Object.entries(n).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Ly(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{n[i]===void 0&&(o[i]=s)}),new H(e.segments,o)}}function gx(e,t,r){let n=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(n>=r.length)return i;let s=e.segments[o],a=r[n];if(Ko(a))break;let u=`${a}`,c=n<r.length-1?r[n+1]:null;if(o>0&&u===void 0)break;if(u&&c&&typeof c=="object"&&c.outlets===void 0){if(!yy(u,c,s))return i;n+=2}else{if(!yy(u,{},s))return i;n++}o++}return{match:!0,pathIndex:o,commandIndex:n}}function $d(e,t,r){let n=e.segments.slice(0,t),o=0;for(;o<r.length;){let i=r[o];if(Ko(i)){let u=mx(i.outlets);return new H(n,u)}if(o===0&&Fa(r[0])){let u=e.segments[t];n.push(new Ln(u.path,vy(r[0]))),o++;continue}let s=Ko(i)?i.outlets[R]:`${i}`,a=o<r.length-1?r[o+1]:null;s&&a&&Fa(a)?(n.push(new Ln(s,vy(a))),o+=2):(n.push(new Ln(s,{})),o++)}return new H(n,{})}function mx(e){let t={};return Object.entries(e).forEach(([r,n])=>{typeof n=="string"&&(n=[n]),n!==null&&(t[r]=$d(new H([],{}),0,n))}),t}function vy(e){let t={};return Object.entries(e).forEach(([r,n])=>t[r]=`${n}`),t}function yy(e,t,r){return e==r.path&&Ct(t,r.parameters)}var Zo="imperative",he=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(he||{}),Ye=class{constructor(t,r){this.id=t,this.url=r}},zr=class extends Ye{constructor(t,r,n="imperative",o=null){super(t,r),this.type=he.NavigationStart,this.navigationTrigger=n,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},ht=class extends Ye{constructor(t,r,n){super(t,r),this.urlAfterRedirects=n,this.type=he.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},He=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(He||{}),La=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(La||{}),un=class extends Ye{constructor(t,r,n,o){super(t,r),this.reason=n,this.code=o,this.type=he.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},cn=class extends Ye{constructor(t,r,n,o){super(t,r),this.reason=n,this.code=o,this.type=he.NavigationSkipped}},Jo=class extends Ye{constructor(t,r,n,o){super(t,r),this.error=n,this.target=o,this.type=he.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},ja=class extends Ye{constructor(t,r,n,o){super(t,r),this.urlAfterRedirects=n,this.state=o,this.type=he.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Vd=class extends Ye{constructor(t,r,n,o){super(t,r),this.urlAfterRedirects=n,this.state=o,this.type=he.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ud=class extends Ye{constructor(t,r,n,o,i){super(t,r),this.urlAfterRedirects=n,this.state=o,this.shouldActivate=i,this.type=he.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Bd=class extends Ye{constructor(t,r,n,o){super(t,r),this.urlAfterRedirects=n,this.state=o,this.type=he.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Hd=class extends Ye{constructor(t,r,n,o){super(t,r),this.urlAfterRedirects=n,this.state=o,this.type=he.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},zd=class{constructor(t){this.route=t,this.type=he.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},qd=class{constructor(t){this.route=t,this.type=he.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Wd=class{constructor(t){this.snapshot=t,this.type=he.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Gd=class{constructor(t){this.snapshot=t,this.type=he.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Zd=class{constructor(t){this.snapshot=t,this.type=he.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Yd=class{constructor(t){this.snapshot=t,this.type=he.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},$a=class{constructor(t,r,n){this.routerEvent=t,this.position=r,this.anchor=n,this.type=he.Scroll}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},Xo=class{},ei=class{constructor(t){this.url=t}};var Qd=class{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new ai,this.attachRef=null}},ai=(()=>{let t=class t{constructor(){this.contexts=new Map}onChildOutletCreated(n,o){let i=this.getOrCreateContext(n);i.outlet=o,this.contexts.set(n,i)}onChildOutletDestroyed(n){let o=this.getContext(n);o&&(o.outlet=null,o.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let o=this.getContext(n);return o||(o=new Qd,this.contexts.set(n,o)),o}getContext(n){return this.contexts.get(n)||null}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Va=class{constructor(t){this._root=t}get root(){return this._root.value}parent(t){let r=this.pathFromRoot(t);return r.length>1?r[r.length-2]:null}children(t){let r=Kd(t,this._root);return r?r.children.map(n=>n.value):[]}firstChild(t){let r=Kd(t,this._root);return r&&r.children.length>0?r.children[0].value:null}siblings(t){let r=Jd(t,this._root);return r.length<2?[]:r[r.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return Jd(t,this._root).map(r=>r.value)}};function Kd(e,t){if(e===t.value)return t;for(let r of t.children){let n=Kd(e,r);if(n)return n}return null}function Jd(e,t){if(e===t.value)return[t];for(let r of t.children){let n=Jd(e,r);if(n.length)return n.unshift(t),n}return[]}var Be=class{constructor(t,r){this.value=t,this.children=r}toString(){return`TreeNode(${this.value})`}};function $r(e){let t={};return e&&e.children.forEach(r=>t[r.value.outlet]=r),t}var Ua=class extends Va{constructor(t,r){super(t),this.snapshot=r,cf(this,t)}toString(){return this.snapshot.toString()}};function jy(e){let t=vx(e),r=new pe([new Ln("",{})]),n=new pe({}),o=new pe({}),i=new pe({}),s=new pe(""),a=new ln(r,n,i,s,o,R,e,t.root);return a.snapshot=t.root,new Ua(new Be(a,[]),t)}function vx(e){let t={},r={},n={},o="",i=new ti([],t,n,o,r,R,e,null,{});return new Ba("",new Be(i,[]))}var ln=class{constructor(t,r,n,o,i,s,a,u){var c,l;this.urlSubject=t,this.paramsSubject=r,this.queryParamsSubject=n,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=u,this.title=(l=(c=this.dataSubject)==null?void 0:c.pipe(F(d=>d[ii])))!=null?l:b(void 0),this.url=t,this.params=r,this.queryParams=n,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){var t;return(t=this._paramMap)!=null||(this._paramMap=this.params.pipe(F(r=>Br(r)))),this._paramMap}get queryParamMap(){var t;return(t=this._queryParamMap)!=null||(this._queryParamMap=this.queryParams.pipe(F(r=>Br(r)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function uf(e,t,r="emptyOnly"){var i,s;let n,{routeConfig:o}=e;return t!==null&&(r==="always"||(o==null?void 0:o.path)===""||!t.component&&!((i=t.routeConfig)!=null&&i.loadComponent))?n={params:w(w({},t.params),e.params),data:w(w({},t.data),e.data),resolve:w(w(w(w({},e.data),t.data),o==null?void 0:o.data),e._resolvedData)}:n={params:w({},e.params),data:w({},e.data),resolve:w(w({},e.data),(s=e._resolvedData)!=null?s:{})},o&&Vy(o)&&(n.resolve[ii]=o.title),n}var ti=class{get title(){var t;return(t=this.data)==null?void 0:t[ii]}constructor(t,r,n,o,i,s,a,u,c){this.url=t,this.params=r,this.queryParams=n,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=u,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){var t;return(t=this._paramMap)!=null||(this._paramMap=Br(this.params)),this._paramMap}get queryParamMap(){var t;return(t=this._queryParamMap)!=null||(this._queryParamMap=Br(this.queryParams)),this._queryParamMap}toString(){let t=this.url.map(n=>n.toString()).join("/"),r=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${r}')`}},Ba=class extends Va{constructor(t,r){super(r),this.url=t,cf(this,r)}toString(){return $y(this._root)}};function cf(e,t){t.value._routerState=e,t.children.forEach(r=>cf(e,r))}function $y(e){let t=e.children.length>0?` { ${e.children.map($y).join(", ")} } `:"";return`${e.value}${t}`}function Od(e){if(e.snapshot){let t=e.snapshot,r=e._futureSnapshot;e.snapshot=r,Ct(t.queryParams,r.queryParams)||e.queryParamsSubject.next(r.queryParams),t.fragment!==r.fragment&&e.fragmentSubject.next(r.fragment),Ct(t.params,r.params)||e.paramsSubject.next(r.params),WS(t.url,r.url)||e.urlSubject.next(r.url),Ct(t.data,r.data)||e.dataSubject.next(r.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Xd(e,t){let r=Ct(e.params,t.params)&&QS(e.url,t.url),n=!e.parent!=!t.parent;return r&&!n&&(!e.parent||Xd(e.parent,t.parent))}function Vy(e){return typeof e.title=="string"||e.title===null}var yx=(()=>{let t=class t{constructor(){this.activated=null,this._activatedRoute=null,this.name=R,this.activateEvents=new ge,this.deactivateEvents=new ge,this.attachEvents=new ge,this.detachEvents=new ge,this.parentContexts=g(ai),this.location=g(nn),this.changeDetector=g(Fn),this.environmentInjector=g(De),this.inputBinder=g(Wa,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(n){if(n.name){let{firstChange:o,previousValue:i}=n.name;if(o)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){var n;this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),(n=this.inputBinder)==null||n.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){var o;return((o=this.parentContexts.getContext(n))==null?void 0:o.outlet)===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n!=null&&n.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new D(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new D(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new D(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,o){var i;this.activated=n,this._activatedRoute=o,this.location.insert(n.hostView),(i=this.inputBinder)==null||i.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,o){var l;if(this.isActivated)throw new D(4013,!1);this._activatedRoute=n;let i=this.location,a=n.snapshot.component,u=this.parentContexts.getOrCreateContext(this.name).children,c=new ef(n,u,i.injector);this.activated=i.createComponent(a,{index:i.length,injector:c,environmentInjector:o!=null?o:this.environmentInjector}),this.changeDetector.markForCheck(),(l=this.inputBinder)==null||l.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275dir=Ot({type:t,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[Cr]});let e=t;return e})(),ef=class e{__ngOutletInjector(t){return new e(this.route,this.childContexts,t)}constructor(t,r,n){this.route=t,this.childContexts=r,this.parent=n}get(t,r){return t===ln?this.route:t===ai?this.childContexts:this.parent.get(t,r)}},Wa=new C(""),Dy=(()=>{let t=class t{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){var o;(o=this.outletDataSubscriptions.get(n))==null||o.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:o}=n,i=Jr([o.queryParams,o.params,o.data]).pipe(Ie(([s,a,u],c)=>(u=w(w(w({},s),a),u),c===0?b(u):Promise.resolve(u)))).subscribe(s=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==o||o.component===null){this.unsubscribeFromRouteData(n);return}let a=Ev(o.component);if(!a){this.unsubscribeFromRouteData(n);return}for(let{templateName:u}of a.inputs)n.activatedComponentRef.setInput(u,s[u])});this.outletDataSubscriptions.set(n,i)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})();function Dx(e,t,r){let n=ni(e,t._root,r?r._root:void 0);return new Ua(n,t)}function ni(e,t,r){if(r&&e.shouldReuseRoute(t.value,r.value.snapshot)){let n=r.value;n._futureSnapshot=t.value;let o=wx(e,t,r);return new Be(n,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>ni(e,a)),s}}let n=Ex(t.value),o=t.children.map(i=>ni(e,i));return new Be(n,o)}}function wx(e,t,r){return t.children.map(n=>{for(let o of r.children)if(e.shouldReuseRoute(n.value,o.value.snapshot))return ni(e,n,o);return ni(e,n)})}function Ex(e){return new ln(new pe(e.url),new pe(e.params),new pe(e.queryParams),new pe(e.fragment),new pe(e.data),e.outlet,e.component,e)}var Uy="ngNavigationCancelingError";function By(e,t){let{redirectTo:r,navigationBehaviorOptions:n}=Hr(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Hy(!1,He.Redirect);return o.url=r,o.navigationBehaviorOptions=n,o}function Hy(e,t){let r=new Error(`NavigationCancelingError: ${e||""}`);return r[Uy]=!0,r.cancellationCode=t,r}function Ix(e){return zy(e)&&Hr(e.url)}function zy(e){return!!e&&e[Uy]}var Cx=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275cmp=mp({type:t,selectors:[["ng-component"]],standalone:!0,features:[Zm],decls:1,vars:0,template:function(o,i){o&1&&Hl(0,"router-outlet")},dependencies:[yx],encapsulation:2});let e=t;return e})();function bx(e,t){var r;return e.providers&&!e._injector&&(e._injector=ia(e.providers,t,`Route: ${e.path}`)),(r=e._injector)!=null?r:t}function lf(e){let t=e.children&&e.children.map(lf),r=t?te(w({},e),{children:t}):w({},e);return!r.component&&!r.loadComponent&&(t||r.loadChildren)&&r.outlet&&r.outlet!==R&&(r.component=Cx),r}function bt(e){return e.outlet||R}function Mx(e,t){let r=e.filter(n=>bt(n)===t);return r.push(...e.filter(n=>bt(n)!==t)),r}function ui(e){var t;if(!e)return null;if((t=e.routeConfig)!=null&&t._injector)return e.routeConfig._injector;for(let r=e.parent;r;r=r.parent){let n=r.routeConfig;if(n!=null&&n._loadedInjector)return n._loadedInjector;if(n!=null&&n._injector)return n._injector}return null}var Tx=(e,t,r,n)=>F(o=>(new tf(t,o.targetRouterState,o.currentRouterState,r,n).activate(e),o)),tf=class{constructor(t,r,n,o,i){this.routeReuseStrategy=t,this.futureState=r,this.currState=n,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let r=this.futureState._root,n=this.currState?this.currState._root:null;this.deactivateChildRoutes(r,n,t),Od(this.futureState.root),this.activateChildRoutes(r,n,t)}deactivateChildRoutes(t,r,n){let o=$r(r);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],n),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,n)})}deactivateRoutes(t,r,n){let o=t.value,i=r?r.value:null;if(o===i)if(o.component){let s=n.getContext(o.outlet);s&&this.deactivateChildRoutes(t,r,s.children)}else this.deactivateChildRoutes(t,r,n);else i&&this.deactivateRouteAndItsChildren(r,n)}deactivateRouteAndItsChildren(t,r){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,r):this.deactivateRouteAndOutlet(t,r)}detachAndStoreRouteSubtree(t,r){let n=r.getContext(t.value.outlet),o=n&&t.value.component?n.children:r,i=$r(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(n&&n.outlet){let s=n.outlet.detach(),a=n.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,r){let n=r.getContext(t.value.outlet),o=n&&t.value.component?n.children:r,i=$r(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);n&&(n.outlet&&(n.outlet.deactivate(),n.children.onOutletDeactivated()),n.attachRef=null,n.route=null)}activateChildRoutes(t,r,n){let o=$r(r);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],n),this.forwardEvent(new Yd(i.value.snapshot))}),t.children.length&&this.forwardEvent(new Gd(t.value.snapshot))}activateRoutes(t,r,n){let o=t.value,i=r?r.value:null;if(Od(o),o===i)if(o.component){let s=n.getOrCreateContext(o.outlet);this.activateChildRoutes(t,r,s.children)}else this.activateChildRoutes(t,r,n);else if(o.component){let s=n.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Od(a.route.value),this.activateChildRoutes(t,null,s.children)}else{let a=ui(o.snapshot);s.attachRef=null,s.route=o,s.injector=a,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}}else this.activateChildRoutes(t,null,n)}},Ha=class{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Ur=class{constructor(t,r){this.component=t,this.route=r}};function Sx(e,t,r){let n=e._root,o=t?t._root:null;return qo(n,o,r,[n.value])}function xx(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Wr(e,t){let r=Symbol(),n=t.get(e,r);return n===r?typeof e=="function"&&!np(e)?e:t.get(e):n}function qo(e,t,r,n,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=$r(t);return e.children.forEach(s=>{Ax(s,i[s.value.outlet],r,n.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Yo(a,r.getContext(s),o)),o}function Ax(e,t,r,n,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=r?r.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let u=_x(s,i,i.routeConfig.runGuardsAndResolvers);u?o.canActivateChecks.push(new Ha(n)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?qo(e,t,a?a.children:null,n,o):qo(e,t,r,n,o),u&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Ur(a.outlet.component,s))}else s&&Yo(t,a,o),o.canActivateChecks.push(new Ha(n)),i.component?qo(e,null,a?a.children:null,n,o):qo(e,null,r,n,o);return o}function _x(e,t,r){if(typeof r=="function")return r(e,t);switch(r){case"pathParamsChange":return!jn(e.url,t.url);case"pathParamsOrQueryParamsChange":return!jn(e.url,t.url)||!Ct(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Xd(e,t)||!Ct(e.queryParams,t.queryParams);case"paramsChange":default:return!Xd(e,t)}}function Yo(e,t,r){let n=$r(e),o=e.value;Object.entries(n).forEach(([i,s])=>{o.component?t?Yo(s,t.children.getContext(i),r):Yo(s,null,r):Yo(s,t,r)}),o.component?t&&t.outlet&&t.outlet.isActivated?r.canDeactivateChecks.push(new Ur(t.outlet.component,o)):r.canDeactivateChecks.push(new Ur(null,o)):r.canDeactivateChecks.push(new Ur(null,o))}function ci(e){return typeof e=="function"}function Nx(e){return typeof e=="boolean"}function Rx(e){return e&&ci(e.canLoad)}function Ox(e){return e&&ci(e.canActivate)}function Px(e){return e&&ci(e.canActivateChild)}function Fx(e){return e&&ci(e.canDeactivate)}function kx(e){return e&&ci(e.canMatch)}function qy(e){return e instanceof Je||(e==null?void 0:e.name)==="EmptyError"}var Na=Symbol("INITIAL_VALUE");function qr(){return Ie(e=>Jr(e.map(t=>t.pipe(At(1),uu(Na)))).pipe(F(t=>{for(let r of t)if(r!==!0){if(r===Na)return Na;if(r===!1||r instanceof an)return r}return!0}),xe(t=>t!==Na),At(1)))}function Lx(e,t){return ne(r=>{let{targetSnapshot:n,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=r;return s.length===0&&i.length===0?b(te(w({},r),{guardsResult:!0})):jx(s,n,o,e).pipe(ne(a=>a&&Nx(a)?$x(n,i,e,t):b(a)),F(a=>te(w({},r),{guardsResult:a})))})}function jx(e,t,r,n){return Q(e).pipe(ne(o=>zx(o.component,o.route,r,t,n)),Xe(o=>o!==!0,!0))}function $x(e,t,r,n){return Q(t).pipe(xt(o=>Yn(Ux(o.route.parent,n),Vx(o.route,n),Hx(e,o.path,r),Bx(e,o.route,r))),Xe(o=>o!==!0,!0))}function Vx(e,t){return e!==null&&t&&t(new Zd(e)),b(!0)}function Ux(e,t){return e!==null&&t&&t(new Wd(e)),b(!0)}function Bx(e,t,r){let n=t.routeConfig?t.routeConfig.canActivate:null;if(!n||n.length===0)return b(!0);let o=n.map(i=>Bi(()=>{var c;let s=(c=ui(t))!=null?c:r,a=Wr(i,s),u=Ox(a)?a.canActivate(t,e):at(s,()=>a(t,e));return dn(u).pipe(Xe())}));return b(o).pipe(qr())}function Hx(e,t,r){let n=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>xx(s)).filter(s=>s!==null).map(s=>Bi(()=>{let a=s.guards.map(u=>{var f;let c=(f=ui(s.node))!=null?f:r,l=Wr(u,c),d=Px(l)?l.canActivateChild(n,e):at(c,()=>l(n,e));return dn(d).pipe(Xe())});return b(a).pipe(qr())}));return b(i).pipe(qr())}function zx(e,t,r,n,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return b(!0);let s=i.map(a=>{var d;let u=(d=ui(t))!=null?d:o,c=Wr(a,u),l=Fx(c)?c.canDeactivate(e,t,r,n):at(u,()=>c(e,t,r,n));return dn(l).pipe(Xe())});return b(s).pipe(qr())}function qx(e,t,r,n){let o=t.canLoad;if(o===void 0||o.length===0)return b(!0);let i=o.map(s=>{let a=Wr(s,e),u=Rx(a)?a.canLoad(t,r):at(e,()=>a(t,r));return dn(u)});return b(i).pipe(qr(),Wy(n))}function Wy(e){return eu(ae(t=>{if(Hr(t))throw By(e,t)}),F(t=>t===!0))}function Wx(e,t,r,n){let o=t.canMatch;if(!o||o.length===0)return b(!0);let i=o.map(s=>{let a=Wr(s,e),u=kx(a)?a.canMatch(t,r):at(e,()=>a(t,r));return dn(u)});return b(i).pipe(qr(),Wy(n))}var ri=class{constructor(t){this.segmentGroup=t||null}},za=class extends Error{constructor(t){super(),this.urlTree=t}};function jr(e){return Qn(new ri(e))}function Gx(e){return Qn(new D(4e3,!1))}function Zx(e){return Qn(Hy(!1,He.GuardRejected))}var nf=class{constructor(t,r){this.urlSerializer=t,this.urlTree=r}lineralizeSegments(t,r){let n=[],o=r.root;for(;;){if(n=n.concat(o.segments),o.numberOfChildren===0)return b(n);if(o.numberOfChildren>1||!o.children[R])return Gx(t.redirectTo);o=o.children[R]}}applyRedirectCommands(t,r,n){let o=this.applyRedirectCreateUrlTree(r,this.urlSerializer.parse(r),t,n);if(r.startsWith("/"))throw new za(o);return o}applyRedirectCreateUrlTree(t,r,n,o){let i=this.createSegmentGroup(t,r.root,n,o);return new an(i,this.createQueryParams(r.queryParams,this.urlTree.queryParams),r.fragment)}createQueryParams(t,r){let n={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i.startsWith(":")){let a=i.substring(1);n[o]=r[a]}else n[o]=i}),n}createSegmentGroup(t,r,n,o){let i=this.createSegments(t,r.segments,n,o),s={};return Object.entries(r.children).forEach(([a,u])=>{s[a]=this.createSegmentGroup(t,u,n,o)}),new H(i,s)}createSegments(t,r,n,o){return r.map(i=>i.path.startsWith(":")?this.findPosParam(t,i,o):this.findOrReturn(i,n))}findPosParam(t,r,n){let o=n[r.path.substring(1)];if(!o)throw new D(4001,!1);return o}findOrReturn(t,r){let n=0;for(let o of r){if(o.path===t.path)return r.splice(n),o;n++}return t}},rf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Yx(e,t,r,n,o){let i=df(e,t,r);return i.matched?(n=bx(t,n),Wx(n,t,r,o).pipe(F(s=>s===!0?i:w({},rf)))):b(i)}function df(e,t,r){var a,u;if(t.path==="**")return Qx(r);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||r.length>0)?w({},rf):{matched:!0,consumedSegments:[],remainingSegments:r,parameters:{},positionalParamSegments:{}};let o=(t.matcher||qS)(r,e,t);if(!o)return w({},rf);let i={};Object.entries((a=o.posParams)!=null?a:{}).forEach(([c,l])=>{i[c]=l.path});let s=o.consumed.length>0?w(w({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:r.slice(o.consumed.length),parameters:s,positionalParamSegments:(u=o.posParams)!=null?u:{}}}function Qx(e){return{matched:!0,parameters:e.length>0?My(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function wy(e,t,r,n){return r.length>0&&Xx(e,r,n)?{segmentGroup:new H(t,Jx(n,new H(r,e.children))),slicedSegments:[]}:r.length===0&&eA(e,r,n)?{segmentGroup:new H(e.segments,Kx(e,r,n,e.children)),slicedSegments:r}:{segmentGroup:new H(e.segments,e.children),slicedSegments:r}}function Kx(e,t,r,n){let o={};for(let i of r)if(Ga(e,t,i)&&!n[bt(i)]){let s=new H([],{});o[bt(i)]=s}return w(w({},n),o)}function Jx(e,t){let r={};r[R]=t;for(let n of e)if(n.path===""&&bt(n)!==R){let o=new H([],{});r[bt(n)]=o}return r}function Xx(e,t,r){return r.some(n=>Ga(e,t,n)&&bt(n)!==R)}function eA(e,t,r){return r.some(n=>Ga(e,t,n))}function Ga(e,t,r){return(e.hasChildren()||t.length>0)&&r.pathMatch==="full"?!1:r.path===""}function tA(e,t,r,n){return bt(e)!==n&&(n===R||!Ga(t,r,e))?!1:df(t,e,r).matched}function nA(e,t,r){return t.length===0&&!e.children[r]}var of=class{};function rA(e,t,r,n,o,i,s="emptyOnly"){return new sf(e,t,r,n,o,s,i).recognize()}var oA=31,sf=class{constructor(t,r,n,o,i,s,a){this.injector=t,this.configLoader=r,this.rootComponentType=n,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new nf(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(t){return new D(4002,`'${t.segmentGroup}'`)}recognize(){let t=wy(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(F(r=>{let n=new ti([],Object.freeze({}),Object.freeze(w({},this.urlTree.queryParams)),this.urlTree.fragment,{},R,this.rootComponentType,null,{}),o=new Be(n,r),i=new Ba("",o),s=lx(n,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),this.inheritParamsAndData(i._root,null),{state:i,tree:s}}))}match(t){return this.processSegmentGroup(this.injector,this.config,t,R).pipe(St(n=>{if(n instanceof za)return this.urlTree=n.urlTree,this.match(n.urlTree.root);throw n instanceof ri?this.noMatchError(n):n}))}inheritParamsAndData(t,r){let n=t.value,o=uf(n,r,this.paramsInheritanceStrategy);n.params=Object.freeze(o.params),n.data=Object.freeze(o.data),t.children.forEach(i=>this.inheritParamsAndData(i,n))}processSegmentGroup(t,r,n,o){return n.segments.length===0&&n.hasChildren()?this.processChildren(t,r,n):this.processSegment(t,r,n,n.segments,o,!0).pipe(F(i=>i instanceof Be?[i]:[]))}processChildren(t,r,n){let o=[];for(let i of Object.keys(n.children))i==="primary"?o.unshift(i):o.push(i);return Q(o).pipe(xt(i=>{let s=n.children[i],a=Mx(r,i);return this.processSegmentGroup(t,a,s,i)}),su((i,s)=>(i.push(...s),i)),zt(null),iu(),ne(i=>{if(i===null)return jr(n);let s=Gy(i);return iA(s),b(s)}))}processSegment(t,r,n,o,i,s){return Q(r).pipe(xt(a=>{var u;return this.processSegmentAgainstRoute((u=a._injector)!=null?u:t,r,a,n,o,i,s).pipe(St(c=>{if(c instanceof ri)return b(null);throw c}))}),Xe(a=>!!a),St(a=>{if(qy(a))return nA(n,o,i)?b(new of):jr(n);throw a}))}processSegmentAgainstRoute(t,r,n,o,i,s,a){return tA(n,o,i,s)?n.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,n,i,s):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,r,n,i,s):jr(o):jr(o)}expandSegmentAgainstRouteUsingRedirect(t,r,n,o,i,s){let{matched:a,consumedSegments:u,positionalParamSegments:c,remainingSegments:l}=df(r,o,i);if(!a)return jr(r);o.redirectTo.startsWith("/")&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>oA&&(this.allowRedirects=!1));let d=this.applyRedirects.applyRedirectCommands(u,o.redirectTo,c);return this.applyRedirects.lineralizeSegments(o,d).pipe(ne(f=>this.processSegment(t,n,r,f.concat(l),s,!1)))}matchSegmentAgainstRoute(t,r,n,o,i){let s=Yx(r,n,o,t,this.urlSerializer);return n.path==="**"&&(r.children={}),s.pipe(Ie(a=>{var u;return a.matched?(t=(u=n._injector)!=null?u:t,this.getChildConfig(t,n,o).pipe(Ie(({routes:c})=>{var A,q,O;let l=(A=n._loadedInjector)!=null?A:t,{consumedSegments:d,remainingSegments:f,parameters:h}=a,p=new ti(d,h,Object.freeze(w({},this.urlTree.queryParams)),this.urlTree.fragment,aA(n),bt(n),(O=(q=n.component)!=null?q:n._loadedComponent)!=null?O:null,n,uA(n)),{segmentGroup:y,slicedSegments:m}=wy(r,d,f,c);if(m.length===0&&y.hasChildren())return this.processChildren(l,c,y).pipe(F(re=>re===null?null:new Be(p,re)));if(c.length===0&&m.length===0)return b(new Be(p,[]));let v=bt(n)===i;return this.processSegment(l,c,y,m,v?R:i,!0).pipe(F(re=>new Be(p,re instanceof Be?[re]:[])))}))):jr(r)}))}getChildConfig(t,r,n){return r.children?b({routes:r.children,injector:t}):r.loadChildren?r._loadedRoutes!==void 0?b({routes:r._loadedRoutes,injector:r._loadedInjector}):qx(t,r,n,this.urlSerializer).pipe(ne(o=>o?this.configLoader.loadChildren(t,r).pipe(ae(i=>{r._loadedRoutes=i.routes,r._loadedInjector=i.injector})):Zx(r))):b({routes:[],injector:t})}};function iA(e){e.sort((t,r)=>t.value.outlet===R?-1:r.value.outlet===R?1:t.value.outlet.localeCompare(r.value.outlet))}function sA(e){let t=e.value.routeConfig;return t&&t.path===""}function Gy(e){let t=[],r=new Set;for(let n of e){if(!sA(n)){t.push(n);continue}let o=t.find(i=>n.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...n.children),r.add(o)):t.push(n)}for(let n of r){let o=Gy(n.children);t.push(new Be(n.value,o))}return t.filter(n=>!r.has(n))}function aA(e){return e.data||{}}function uA(e){return e.resolve||{}}function cA(e,t,r,n,o,i){return ne(s=>rA(e,t,r,n,s.extractedUrl,o,i).pipe(F(({state:a,tree:u})=>te(w({},s),{targetSnapshot:a,urlAfterRedirects:u}))))}function lA(e,t){return ne(r=>{let{targetSnapshot:n,guards:{canActivateChecks:o}}=r;if(!o.length)return b(r);let i=new Set(o.map(u=>u.route)),s=new Set;for(let u of i)if(!s.has(u))for(let c of Zy(u))s.add(c);let a=0;return Q(s).pipe(xt(u=>i.has(u)?dA(u,n,e,t):(u.data=uf(u,u.parent,e).resolve,b(void 0))),ae(()=>a++),Kn(1),ne(u=>a===s.size?b(r):Ee))})}function Zy(e){let t=e.children.map(r=>Zy(r)).flat();return[e,...t]}function dA(e,t,r,n){let o=e.routeConfig,i=e._resolve;return(o==null?void 0:o.title)!==void 0&&!Vy(o)&&(i[ii]=o.title),fA(i,e,t,n).pipe(F(s=>(e._resolvedData=s,e.data=uf(e,e.parent,r).resolve,null)))}function fA(e,t,r,n){let o=kd(e);if(o.length===0)return b({});let i={};return Q(o).pipe(ne(s=>hA(e[s],t,r,n).pipe(Xe(),ae(a=>{i[s]=a}))),Kn(1),ou(i),St(s=>qy(s)?Ee:Qn(s)))}function hA(e,t,r,n){var a;let o=(a=ui(t))!=null?a:n,i=Wr(e,o),s=i.resolve?i.resolve(t,r):at(o,()=>i(t,r));return dn(s)}function Pd(e){return Ie(t=>{let r=e(t);return r?Q(r).pipe(F(()=>t)):b(t)})}var Yy=(()=>{let t=class t{buildTitle(n){var s;let o,i=n.root;for(;i!==void 0;)o=(s=this.getResolvedTitleForRoute(i))!=null?s:o,i=i.children.find(a=>a.outlet===R);return o}getResolvedTitleForRoute(n){return n.data[ii]}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:()=>g(pA),providedIn:"root"});let e=t;return e})(),pA=(()=>{let t=class t extends Yy{constructor(n){super(),this.title=n}updateTitle(n){let o=this.buildTitle(n);o!==void 0&&this.title.setTitle(o)}};t.\u0275fac=function(o){return new(o||t)(I(py))},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),li=new C("",{providedIn:"root",factory:()=>({})}),oi=new C(""),ff=(()=>{let t=class t{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=g(la)}loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return b(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let o=dn(n.loadComponent()).pipe(F(Qy),ae(s=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=s}),Ht(()=>{this.componentLoaders.delete(n)})),i=new Xn(o,()=>new se).pipe(Jn());return this.componentLoaders.set(n,i),i}loadChildren(n,o){if(this.childrenLoaders.get(o))return this.childrenLoaders.get(o);if(o._loadedRoutes)return b({routes:o._loadedRoutes,injector:o._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(o);let s=gA(o,this.compiler,n,this.onLoadEndListener).pipe(Ht(()=>{this.childrenLoaders.delete(o)})),a=new Xn(s,()=>new se).pipe(Jn());return this.childrenLoaders.set(o,a),a}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function gA(e,t,r,n){return dn(e.loadChildren()).pipe(F(Qy),ne(o=>o instanceof Io||Array.isArray(o)?b(o):Q(t.compileModuleAsync(o))),F(o=>{n&&n(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(r).injector,s=i.get(oi,[],{optional:!0,self:!0}).flat()),{routes:s.map(lf),injector:i}}))}function mA(e){return e&&typeof e=="object"&&"default"in e}function Qy(e){return mA(e)?e.default:e}var hf=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:()=>g(vA),providedIn:"root"});let e=t;return e})(),vA=(()=>{let t=class t{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,o){return n}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Ky=new C(""),Jy=new C("");function yA(e,t,r){let n=e.get(Jy),o=e.get(me);return e.get(W).runOutsideAngular(()=>{if(!o.startViewTransition||n.skipNextTransition)return n.skipNextTransition=!1,new Promise(c=>setTimeout(c));let i,s=new Promise(c=>{i=c}),a=o.startViewTransition(()=>(i(),DA(e))),{onViewTransitionCreated:u}=n;return u&&at(e,()=>u({transition:a,from:t,to:r})),s})}function DA(e){return new Promise(t=>{oa(t,{injector:e})})}var pf=(()=>{let t=class t{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new se,this.transitionAbortSubject=new se,this.configLoader=g(ff),this.environmentInjector=g(De),this.urlSerializer=g(si),this.rootContexts=g(ai),this.location=g(kr),this.inputBindingEnabled=g(Wa,{optional:!0})!==null,this.titleStrategy=g(Yy),this.options=g(li,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=g(hf),this.createViewTransition=g(Ky,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>b(void 0),this.rootComponentType=null;let n=i=>this.events.next(new zd(i)),o=i=>this.events.next(new qd(i));this.configLoader.onLoadEndListener=o,this.configLoader.onLoadStartListener=n}complete(){var n;(n=this.transitions)==null||n.complete()}handleNavigationRequest(n){var i;let o=++this.navigationId;(i=this.transitions)==null||i.next(te(w(w({},this.transitions.value),n),{id:o}))}setupNavigations(n,o,i){return this.transitions=new pe({id:0,currentUrlTree:o,currentRawUrl:o,extractedUrl:this.urlHandlingStrategy.extract(o),urlAfterRedirects:this.urlHandlingStrategy.extract(o),rawUrl:o,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:Zo,restoredState:null,currentSnapshot:i.snapshot,targetSnapshot:null,currentRouterState:i,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(xe(s=>s.id!==0),F(s=>te(w({},s),{extractedUrl:this.urlHandlingStrategy.extract(s.rawUrl)})),Ie(s=>{let a=!1,u=!1;return b(s).pipe(Ie(c=>{var f;if(this.navigationId>s.id)return this.cancelNavigationTransition(s,"",He.SupersededByNewNavigation),Ee;this.currentTransition=s,this.currentNavigation={id:c.id,initialUrl:c.rawUrl,extractedUrl:c.extractedUrl,trigger:c.source,extras:c.extras,previousNavigation:this.lastSuccessfulNavigation?te(w({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let l=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),d=(f=c.extras.onSameUrlNavigation)!=null?f:n.onSameUrlNavigation;if(!l&&d!=="reload"){let h="";return this.events.next(new cn(c.id,this.urlSerializer.serialize(c.rawUrl),h,La.IgnoredSameUrlNavigation)),c.resolve(null),Ee}if(this.urlHandlingStrategy.shouldProcessUrl(c.rawUrl))return b(c).pipe(Ie(h=>{var y,m;let p=(y=this.transitions)==null?void 0:y.getValue();return this.events.next(new zr(h.id,this.urlSerializer.serialize(h.extractedUrl),h.source,h.restoredState)),p!==((m=this.transitions)==null?void 0:m.getValue())?Ee:Promise.resolve(h)}),cA(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),ae(h=>{s.targetSnapshot=h.targetSnapshot,s.urlAfterRedirects=h.urlAfterRedirects,this.currentNavigation=te(w({},this.currentNavigation),{finalUrl:h.urlAfterRedirects});let p=new ja(h.id,this.urlSerializer.serialize(h.extractedUrl),this.urlSerializer.serialize(h.urlAfterRedirects),h.targetSnapshot);this.events.next(p)}));if(l&&this.urlHandlingStrategy.shouldProcessUrl(c.currentRawUrl)){let{id:h,extractedUrl:p,source:y,restoredState:m,extras:v}=c,A=new zr(h,this.urlSerializer.serialize(p),y,m);this.events.next(A);let q=jy(this.rootComponentType).snapshot;return this.currentTransition=s=te(w({},c),{targetSnapshot:q,urlAfterRedirects:p,extras:te(w({},v),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=p,b(s)}else{let h="";return this.events.next(new cn(c.id,this.urlSerializer.serialize(c.extractedUrl),h,La.IgnoredByUrlHandlingStrategy)),c.resolve(null),Ee}}),ae(c=>{let l=new Vd(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}),F(c=>(this.currentTransition=s=te(w({},c),{guards:Sx(c.targetSnapshot,c.currentSnapshot,this.rootContexts)}),s)),Lx(this.environmentInjector,c=>this.events.next(c)),ae(c=>{if(s.guardsResult=c.guardsResult,Hr(c.guardsResult))throw By(this.urlSerializer,c.guardsResult);let l=new Ud(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot,!!c.guardsResult);this.events.next(l)}),xe(c=>c.guardsResult?!0:(this.cancelNavigationTransition(c,"",He.GuardRejected),!1)),Pd(c=>{if(c.guards.canActivateChecks.length)return b(c).pipe(ae(l=>{let d=new Bd(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(d)}),Ie(l=>{let d=!1;return b(l).pipe(lA(this.paramsInheritanceStrategy,this.environmentInjector),ae({next:()=>d=!0,complete:()=>{d||this.cancelNavigationTransition(l,"",He.NoDataFromResolver)}}))}),ae(l=>{let d=new Hd(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(d)}))}),Pd(c=>{let l=d=>{var h;let f=[];(h=d.routeConfig)!=null&&h.loadComponent&&!d.routeConfig._loadedComponent&&f.push(this.configLoader.loadComponent(d.routeConfig).pipe(ae(p=>{d.component=p}),F(()=>{})));for(let p of d.children)f.push(...l(p));return f};return Jr(l(c.targetSnapshot.root)).pipe(zt(null),At(1))}),Pd(()=>this.afterPreactivation()),Ie(()=>{var f;let{currentSnapshot:c,targetSnapshot:l}=s,d=(f=this.createViewTransition)==null?void 0:f.call(this,this.environmentInjector,c.root,l.root);return d?Q(d).pipe(F(()=>s)):b(s)}),F(c=>{let l=Dx(n.routeReuseStrategy,c.targetSnapshot,c.currentRouterState);return this.currentTransition=s=te(w({},c),{targetRouterState:l}),this.currentNavigation.targetRouterState=l,s}),ae(()=>{this.events.next(new Xo)}),Tx(this.rootContexts,n.routeReuseStrategy,c=>this.events.next(c),this.inputBindingEnabled),At(1),ae({next:c=>{var l;a=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new ht(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects))),(l=this.titleStrategy)==null||l.updateTitle(c.targetRouterState.snapshot),c.resolve(!0)},complete:()=>{a=!0}}),cu(this.transitionAbortSubject.pipe(ae(c=>{throw c}))),Ht(()=>{var c;!a&&!u&&this.cancelNavigationTransition(s,"",He.SupersededByNewNavigation),((c=this.currentTransition)==null?void 0:c.id)===s.id&&(this.currentNavigation=null,this.currentTransition=null)}),St(c=>{var l;if(u=!0,zy(c))this.events.next(new un(s.id,this.urlSerializer.serialize(s.extractedUrl),c.message,c.cancellationCode)),Ix(c)?this.events.next(new ei(c.url)):s.resolve(!1);else{this.events.next(new Jo(s.id,this.urlSerializer.serialize(s.extractedUrl),c,(l=s.targetSnapshot)!=null?l:void 0));try{s.resolve(n.errorHandler(c))}catch(d){this.options.resolveNavigationPromiseOnError?s.resolve(!1):s.reject(d)}}return Ee}))}))}cancelNavigationTransition(n,o,i){let s=new un(n.id,this.urlSerializer.serialize(n.extractedUrl),o,i);this.events.next(s),n.resolve(!1)}isUpdatingInternalState(){var n,o;return((n=this.currentTransition)==null?void 0:n.extractedUrl.toString())!==((o=this.currentTransition)==null?void 0:o.currentUrlTree.toString())}isUpdatedBrowserUrl(){var o,i;return this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))).toString()!==((o=this.currentTransition)==null?void 0:o.extractedUrl.toString())&&!((i=this.currentTransition)!=null&&i.extras.skipLocationChange)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function wA(e){return e!==Zo}var EA=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:()=>g(IA),providedIn:"root"});let e=t;return e})(),af=class{shouldDetach(t){return!1}store(t,r){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,r){return t.routeConfig===r.routeConfig}},IA=(()=>{let t=class t extends af{};t.\u0275fac=(()=>{let n;return function(i){return(n||(n=ul(t)))(i||t)}})(),t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Xy=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:()=>g(CA),providedIn:"root"});let e=t;return e})(),CA=(()=>{let t=class t extends Xy{constructor(){super(...arguments),this.location=g(kr),this.urlSerializer=g(si),this.options=g(li,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=g(hf),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new an,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=jy(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){var n,o;return this.canceledNavigationResolution!=="computed"?this.currentPageId:(o=(n=this.restoredState())==null?void 0:n.\u0275routerPageId)!=null?o:this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(o=>{o.type==="popstate"&&n(o.url,o.state)})}handleRouterEvent(n,o){if(n instanceof zr)this.stateMemento=this.createStateMemento();else if(n instanceof cn)this.rawUrlTree=o.initialUrl;else if(n instanceof ja){if(this.urlUpdateStrategy==="eager"&&!o.extras.skipLocationChange){let i=this.urlHandlingStrategy.merge(o.finalUrl,o.initialUrl);this.setBrowserUrl(i,o)}}else n instanceof Xo?(this.currentUrlTree=o.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(o.finalUrl,o.initialUrl),this.routerState=o.targetRouterState,this.urlUpdateStrategy==="deferred"&&(o.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,o))):n instanceof un&&(n.code===He.GuardRejected||n.code===He.NoDataFromResolver)?this.restoreHistory(o):n instanceof Jo?this.restoreHistory(o,!0):n instanceof ht&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,o){let i=this.urlSerializer.serialize(n);if(this.location.isCurrentPathEqualTo(i)||o.extras.replaceUrl){let s=this.browserPageId,a=w(w({},o.extras.state),this.generateNgRouterState(o.id,s));this.location.replaceState(i,"",a)}else{let s=w(w({},o.extras.state),this.generateNgRouterState(o.id,this.browserPageId+1));this.location.go(i,"",s)}}restoreHistory(n,o=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,s=this.currentPageId-i;s!==0?this.location.historyGo(s):this.currentUrlTree===n.finalUrl&&s===0&&(this.resetState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(o&&this.resetState(n),this.resetUrlToCurrentUrlTree())}resetState(n){var o;this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,(o=n.finalUrl)!=null?o:this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,o){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:o}:{navigationId:n}}};t.\u0275fac=(()=>{let n;return function(i){return(n||(n=ul(t)))(i||t)}})(),t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Wo=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(Wo||{});function eD(e,t){e.events.pipe(xe(r=>r instanceof ht||r instanceof un||r instanceof Jo||r instanceof cn),F(r=>r instanceof ht||r instanceof cn?Wo.COMPLETE:(r instanceof un?r.code===He.Redirect||r.code===He.SupersededByNewNavigation:!1)?Wo.REDIRECTING:Wo.FAILED),xe(r=>r!==Wo.REDIRECTING),At(1)).subscribe(()=>{t()})}function bA(e){throw e}var MA={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},TA={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Mt=(()=>{let t=class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){var n,o;this.disposed=!1,this.isNgZoneEnabled=!1,this.console=g(ua),this.stateManager=g(Xy),this.options=g(li,{optional:!0})||{},this.pendingTasks=g(Ar),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=g(pf),this.urlSerializer=g(si),this.location=g(kr),this.urlHandlingStrategy=g(hf),this._events=new se,this.errorHandler=this.options.errorHandler||bA,this.navigated=!1,this.routeReuseStrategy=g(EA),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=(o=(n=g(oi,{optional:!0}))==null?void 0:n.flat())!=null?o:[],this.componentInputBindingEnabled=!!g(Wa,{optional:!0}),this.eventsSubscription=new J,this.isNgZoneEnabled=g(W)instanceof W&&W.isInAngularZone(),this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:i=>{this.console.warn(i)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(o=>{try{let i=this.navigationTransitions.currentTransition,s=this.navigationTransitions.currentNavigation;if(i!==null&&s!==null){if(this.stateManager.handleRouterEvent(o,s),o instanceof un&&o.code!==He.Redirect&&o.code!==He.SupersededByNewNavigation)this.navigated=!0;else if(o instanceof ht)this.navigated=!0;else if(o instanceof ei){let a=this.urlHandlingStrategy.merge(o.url,i.currentRawUrl),u={info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:this.urlUpdateStrategy==="eager"||wA(i.source)};this.scheduleNavigation(a,Zo,null,u,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}xA(o)&&this._events.next(o)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Zo,this.stateManager.restoredState())}setUpLocationChangeListener(){var n;(n=this.nonRouterCurrentEntryChangeSubscription)!=null||(this.nonRouterCurrentEntryChangeSubscription=this.stateManager.registerNonRouterCurrentEntryChangeListener((o,i)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(o,"popstate",i)},0)}))}navigateToSyncWithBrowser(n,o,i){let s={replaceUrl:!0},a=i!=null&&i.navigationId?i:null;if(i){let c=w({},i);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(s.state=c)}let u=this.parseUrl(n);this.scheduleNavigation(u,o,a,s)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(lf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,o={}){let{relativeTo:i,queryParams:s,fragment:a,queryParamsHandling:u,preserveFragment:c}=o,l=c?this.currentUrlTree.fragment:a,d=null;switch(u){case"merge":d=w(w({},this.currentUrlTree.queryParams),s);break;case"preserve":d=this.currentUrlTree.queryParams;break;default:d=s||null}d!==null&&(d=this.removeEmptyProps(d));let f;try{let h=i?i.snapshot:this.routerState.snapshot.root;f=Py(h)}catch{(typeof n[0]!="string"||!n[0].startsWith("/"))&&(n=[]),f=this.currentUrlTree.root}return Fy(f,n,d,l!=null?l:null)}navigateByUrl(n,o={skipLocationChange:!1}){let i=Hr(n)?n:this.parseUrl(n),s=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(s,Zo,null,o)}navigate(n,o={skipLocationChange:!1}){return SA(n),this.navigateByUrl(this.createUrlTree(n,o),o)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,o){let i;if(o===!0?i=w({},MA):o===!1?i=w({},TA):i=o,Hr(n))return gy(this.currentUrlTree,n,i);let s=this.parseUrl(n);return gy(this.currentUrlTree,s,i)}removeEmptyProps(n){return Object.entries(n).reduce((o,[i,s])=>(s!=null&&(o[i]=s),o),{})}scheduleNavigation(n,o,i,s,a){if(this.disposed)return Promise.resolve(!1);let u,c,l;a?(u=a.resolve,c=a.reject,l=a.promise):l=new Promise((f,h)=>{u=f,c=h});let d=this.pendingTasks.add();return eD(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(d))}),this.navigationTransitions.handleNavigationRequest({source:o,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:s,resolve:u,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(f=>Promise.reject(f))}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function SA(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new D(4008,!1)}function xA(e){return!(e instanceof Xo)&&!(e instanceof ei)}var Ey=(()=>{let t=class t{constructor(n,o,i,s,a,u){var l;this.router=n,this.route=o,this.tabIndexAttribute=i,this.renderer=s,this.el=a,this.locationStrategy=u,this.href=null,this.commands=null,this.onChanges=new se,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1;let c=(l=a.nativeElement.tagName)==null?void 0:l.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=n.events.subscribe(d=>{d instanceof ht&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(n){n!=null?(this.commands=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(n,o,i,s,a){let u=this.urlTree;if(u===null||this.isAnchorElement&&(n!==0||o||i||s||a||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(u,c),!this.isAnchorElement}ngOnDestroy(){var n;(n=this.subscription)==null||n.unsubscribe()}updateHref(){var i;let n=this.urlTree;this.href=n!==null&&this.locationStrategy?(i=this.locationStrategy)==null?void 0:i.prepareExternalUrl(this.router.serializeUrl(n)):null;let o=this.href===null?null:Ag(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",o)}applyAttributeValue(n,o){let i=this.renderer,s=this.el.nativeElement;o!==null?i.setAttribute(s,n,o):i.removeAttribute(s,n)}get urlTree(){return this.commands===null?null:this.router.createUrlTree(this.commands,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}};t.\u0275fac=function(o){return new(o||t)($(Mt),$(ln),cl("tabindex"),$(On),$(lt),$(Bt))},t.\u0275dir=Ot({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(o,i){o&1&&zl("click",function(a){return i.onClick(a.button,a.ctrlKey,a.shiftKey,a.altKey,a.metaKey)}),o&2&&Bl("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[ze.HasDecoratorInputTransform,"preserveFragment","preserveFragment",Oo],skipLocationChange:[ze.HasDecoratorInputTransform,"skipLocationChange","skipLocationChange",Oo],replaceUrl:[ze.HasDecoratorInputTransform,"replaceUrl","replaceUrl",Oo],routerLink:"routerLink"},standalone:!0,features:[Vl,Cr]});let e=t;return e})(),mj=(()=>{let t=class t{get isActive(){return this._isActive}constructor(n,o,i,s,a){this.router=n,this.element=o,this.renderer=i,this.cdr=s,this.link=a,this.classes=[],this._isActive=!1,this.routerLinkActiveOptions={exact:!1},this.isActiveChange=new ge,this.routerEventsSubscription=n.events.subscribe(u=>{u instanceof ht&&this.update()})}ngAfterContentInit(){b(this.links.changes,b(null)).pipe(gt()).subscribe(n=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){var o;(o=this.linkInputChangesSubscription)==null||o.unsubscribe();let n=[...this.links.toArray(),this.link].filter(i=>!!i).map(i=>i.onChanges);this.linkInputChangesSubscription=Q(n).pipe(gt()).subscribe(i=>{this._isActive!==this.isLinkActive(this.router)(i)&&this.update()})}set routerLinkActive(n){let o=Array.isArray(n)?n:n.split(" ");this.classes=o.filter(i=>!!i)}ngOnChanges(n){this.update()}ngOnDestroy(){var n;this.routerEventsSubscription.unsubscribe(),(n=this.linkInputChangesSubscription)==null||n.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let n=this.hasActiveLinks();this.classes.forEach(o=>{n?this.renderer.addClass(this.element.nativeElement,o):this.renderer.removeClass(this.element.nativeElement,o)}),n&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==n&&(this._isActive=n,this.cdr.markForCheck(),this.isActiveChange.emit(n))})}isLinkActive(n){let o=AA(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return i=>{let s=i.urlTree;return s?n.isActive(s,o):!1}}hasActiveLinks(){let n=this.isLinkActive(this.router);return this.link&&n(this.link)||this.links.some(n)}};t.\u0275fac=function(o){return new(o||t)($(Mt),$(lt),$(On),$(Fn),$(Ey,8))},t.\u0275dir=Ot({type:t,selectors:[["","routerLinkActive",""]],contentQueries:function(o,i,s){if(o&1&&Bm(s,Ey,5),o&2){let a;Hm(a=zm())&&(i.links=a)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],standalone:!0,features:[Cr]});let e=t;return e})();function AA(e){return!!e.paths}var qa=class{};var _A=(()=>{let t=class t{constructor(n,o,i,s,a){this.router=n,this.injector=i,this.preloadingStrategy=s,this.loader=a}setUpPreloading(){this.subscription=this.router.events.pipe(xe(n=>n instanceof ht),xt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,o){var s,a,u;let i=[];for(let c of o){c.providers&&!c._injector&&(c._injector=ia(c.providers,n,`Route: ${c.path}`));let l=(s=c._injector)!=null?s:n,d=(a=c._loadedInjector)!=null?a:l;(c.loadChildren&&!c._loadedRoutes&&c.canLoad===void 0||c.loadComponent&&!c._loadedComponent)&&i.push(this.preloadConfig(l,c)),(c.children||c._loadedRoutes)&&i.push(this.processRoutes(d,(u=c.children)!=null?u:c._loadedRoutes))}return Q(i).pipe(gt())}preloadConfig(n,o){return this.preloadingStrategy.preload(o,()=>{let i;o.loadChildren&&o.canLoad===void 0?i=this.loader.loadChildren(n,o):i=b(null);let s=i.pipe(ne(a=>{var u;return a===null?b(void 0):(o._loadedRoutes=a.routes,o._loadedInjector=a.injector,this.processRoutes((u=a.injector)!=null?u:n,a.routes))}));if(o.loadComponent&&!o._loadedComponent){let a=this.loader.loadComponent(o);return Q([s,a]).pipe(gt())}else return s})}};t.\u0275fac=function(o){return new(o||t)(I(Mt),I(la),I(De),I(qa),I(ff))},t.\u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),tD=new C(""),NA=(()=>{let t=class t{constructor(n,o,i,s,a={}){this.urlSerializer=n,this.transitions=o,this.viewportScroller=i,this.zone=s,this.options=a,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},this.environmentInjector=g(De),a.scrollPositionRestoration||(a.scrollPositionRestoration="disabled"),a.anchorScrolling||(a.anchorScrolling="disabled")}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof zr?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof ht?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof cn&&n.code===La.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof $a&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,o){this.zone.runOutsideAngular(()=>$n(this,null,function*(){yield new Promise(i=>{setTimeout(()=>{i()}),oa(()=>{i()},{injector:this.environmentInjector})}),this.zone.run(()=>{this.transitions.events.next(new $a(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,o))})}))}ngOnDestroy(){var n,o;(n=this.routerEventsSubscription)==null||n.unsubscribe(),(o=this.scrollEventsSubscription)==null||o.unsubscribe()}};t.\u0275fac=function(o){Ug()},t.\u0275prov=E({token:t,factory:t.\u0275fac});let e=t;return e})();function vj(e,...t){return Jt([{provide:oi,multi:!0,useValue:e},[],{provide:ln,useFactory:nD,deps:[Mt]},{provide:Pn,multi:!0,useFactory:rD},t.map(r=>r.\u0275providers)])}function nD(e){return e.routerState.root}function di(e,t){return{\u0275kind:e,\u0275providers:t}}function rD(){let e=g(ct);return t=>{var i,s;let r=e.get(Lt);if(t!==r.components[0])return;let n=e.get(Mt),o=e.get(oD);e.get(gf)===1&&n.initialNavigation(),(i=e.get(iD,null,L.Optional))==null||i.setUpPreloading(),(s=e.get(tD,null,L.Optional))==null||s.init(),n.resetRootComponentType(r.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var oD=new C("",{factory:()=>new se}),gf=new C("",{providedIn:"root",factory:()=>1});function RA(){return di(2,[{provide:gf,useValue:0},{provide:ca,multi:!0,deps:[ct],useFactory:t=>{let r=t.get(Rv,Promise.resolve());return()=>r.then(()=>new Promise(n=>{let o=t.get(Mt),i=t.get(oD);eD(o,()=>{n(!0)}),t.get(pf).afterPreactivation=()=>(n(!0),i.closed?b(void 0):i),o.initialNavigation()}))}}])}function OA(){return di(3,[{provide:ca,multi:!0,useFactory:()=>{let t=g(Mt);return()=>{t.setUpLocationChangeListener()}}},{provide:gf,useValue:2}])}var iD=new C("");function PA(e){return di(0,[{provide:iD,useExisting:_A},{provide:qa,useExisting:e}])}function FA(){return di(8,[Dy,{provide:Wa,useExisting:Dy}])}function kA(e){let t=[{provide:Ky,useValue:yA},{provide:Jy,useValue:w({skipNextTransition:!!(e!=null&&e.skipInitialTransition)},e)}];return di(9,t)}var Iy=new C("ROUTER_FORROOT_GUARD"),LA=[kr,{provide:si,useClass:Qo},Mt,ai,{provide:ln,useFactory:nD,deps:[Mt]},ff,[]],yj=(()=>{let t=class t{constructor(n){}static forRoot(n,o){return{ngModule:t,providers:[LA,[],{provide:oi,multi:!0,useValue:n},{provide:Iy,useFactory:UA,deps:[[Mt,new Mo,new Ps]]},{provide:li,useValue:o||{}},o!=null&&o.useHash?$A():VA(),jA(),o!=null&&o.preloadingStrategy?PA(o.preloadingStrategy).\u0275providers:[],o!=null&&o.initialNavigation?BA(o):[],o!=null&&o.bindToComponentInputs?FA().\u0275providers:[],o!=null&&o.enableViewTransitions?kA().\u0275providers:[],HA()]}}static forChild(n){return{ngModule:t,providers:[{provide:oi,multi:!0,useValue:n}]}}};t.\u0275fac=function(o){return new(o||t)(I(Iy,8))},t.\u0275mod=wr({type:t}),t.\u0275inj=Dr({});let e=t;return e})();function jA(){return{provide:tD,useFactory:()=>{let e=g(Vv),t=g(W),r=g(li),n=g(pf),o=g(si);return r.scrollOffset&&e.setOffset(r.scrollOffset),new NA(o,n,e,t,r)}}}function $A(){return{provide:Bt,useClass:Pv}}function VA(){return{provide:Bt,useClass:hd}}function UA(e){return"guarded"}function BA(e){return[e.initialNavigation==="disabled"?OA().\u0275providers:[],e.initialNavigation==="enabledBlocking"?RA().\u0275providers:[]]}var Cy=new C("");function HA(){return[{provide:Cy,useFactory:rD},{provide:Pn,multi:!0,useExisting:Cy}]}export{J as a,k as b,se as c,pe as d,kD as e,Ee as f,Q as g,b as h,Qn as i,gu as j,LD as k,F as l,ne as m,Yn as n,Bi as o,jD as p,mu as q,AD as r,qD as s,xe as t,yD as u,St as v,xt as w,SD as x,At as y,Ht as z,Xe as A,qf as B,uu as C,Ie as D,cu as E,ae as F,D as G,ep as H,E as I,Dr as J,C as K,I as L,g as M,Mo as N,ze as O,mp as P,wr as Q,Ot as R,Nn as S,Jt as T,Pw as U,De as V,qw as W,Cr as X,p1 as Y,g1 as Z,m1 as _,v1 as $,ul as aa,ct as ba,ll as ca,y1 as da,lt as ea,ge as fa,fg as ga,Hs as ha,dl as ia,We as ja,D1 as ka,en as la,XE as ma,zi as na,dt as oa,w1 as pa,bI as qa,E1 as ra,I1 as sa,C1 as ta,$ as ua,xn as va,ys as wa,Eo as xa,On as ya,W as za,nn as Aa,T1 as Ba,n0 as Ca,Vl as Da,d0 as Ea,Bl as Fa,x0 as Ga,xm as Ha,A0 as Ia,S1 as Ja,x1 as Ka,Pm as La,Fm as Ma,Hl as Na,W0 as Oa,G0 as Pa,A1 as Qa,Q0 as Ra,zl as Sa,_1 as Ta,N1 as Ua,R1 as Va,aM as Wa,Um as Xa,O1 as Ya,Hm as Za,zm as _a,P1 as $a,F1 as ab,dM as bb,Wm as cb,fM as db,hM as eb,pM as fb,gM as gb,mM as hb,k1 as ib,vM as jb,L1 as kb,Zm as lb,j1 as mb,$1 as nb,V1 as ob,U1 as pb,B1 as qb,H1 as rb,z1 as sb,q1 as tb,W1 as ub,G1 as vb,Z1 as wb,ua as xb,Jm as yb,TM as zb,Nr as Ab,ca as Bb,Lt as Cb,Zl as Db,la as Eb,uv as Fb,cv as Gb,Fn as Hb,zM as Ib,Y1 as Jb,Oo as Kb,Q1 as Lb,K1 as Mb,Pr as Nb,Nv as Ob,me as Pb,dd as Qb,Ov as Rb,kr as Sb,mL as Tb,vL as Ub,yL as Vb,DL as Wb,wL as Xb,EL as Yb,IL as Zb,CL as _b,bL as $b,ML as ac,ZT as bc,YT as cc,QT as dc,Fo as ec,Vv as fc,ld as gc,Fr as hc,It as ic,on as jc,sn as kc,kn as lc,rn as mc,oS as nc,ny as oc,kL as pc,LL as qc,jL as rc,Td as sc,Sd as tc,xa as uc,ay as vc,KL as wc,BS as xc,JL as yc,ht as zc,ln as Ac,yx as Bc,gA as Cc,Mt as Dc,Ey as Ec,mj as Fc,vj as Gc,yj as Hc};
