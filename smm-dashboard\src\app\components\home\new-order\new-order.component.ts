import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ServiceDropdownComponent } from "../../common/service-dropdown/service-dropdown.component";
import { IconsModule } from '../../../icons/icons.module';
import { IconDropdownComponent } from "../../common/icon-dropdown/icon-dropdown.component";
import { TranslateModule } from '@ngx-translate/core';
import { OrderSuccessComponent } from "../../common/order-success/order-success.component";
import { CategoriesService } from '../../../core/services/categories.service';
import { PlatformSelectionService } from '../../../core/services/platform-selection.service';
import { ServiceSelectionService } from '../../../core/services/service-selection.service';
import { FavoritesService } from '../../../core/services/favorites.service';
import { OrderService, CreateOrderReq } from '../../../core/services/order.service';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { IconBaseModel } from '../../../model/base-model';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { Subscription } from 'rxjs';
import { ServiceAutocompleteComponent } from "../../common/service-autocomplete/service-autocomplete.component";
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { OrderRes } from '../../../model/response/order-res.model';
import { CurrencyConvertPipe } from '../../../core/pipes/currency-convert.pipe';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { VoucherService } from '../../../core/services/voucher.service';
import { VoucherLiteRes } from '../../../model/response/voucher-lite-res.model';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-new-order',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    ServiceDropdownComponent,
    IconsModule,
    TranslateModule,
    OrderSuccessComponent,
    ServiceAutocompleteComponent,
    CurrencyConvertPipe,
    IconDropdownComponent
],
  templateUrl: './new-order.component.html',
  styleUrl: './new-order.component.css'
})
export class NewOrderComponent implements OnInit, OnDestroy {
  categories: IconBaseModel[] = [];
  services: SuperGeneralSvRes[] = [];
  platforms: SuperPlatformRes[] = [];
  selectedCategory: IconBaseModel | undefined;
  selectedService: SuperGeneralSvRes | undefined;
  selectedPlatformId: number | null = null;

  // Search related properties
  searchTerm: string = '';
  searchResults: SuperGeneralSvRes[] = [];
  showAutocomplete: boolean = false;
  allServices: SuperGeneralSvRes[] = [];

  @ViewChild(ServiceDropdownComponent) serviceDropdown!: ServiceDropdownComponent;
  @ViewChild('categoryDropdown', { static: false }) categoryDropdown: any;

  private platformSelectionSubscription: Subscription | undefined;
  private favoritesSubscription: Subscription | undefined;
  private userSubscription: Subscription | undefined;

  // Track favorite services
  favoriteServiceIds: number[] = [];
  favoriteServices: SuperGeneralSvRes[] = [];

  // User data
  user: UserRes | undefined;

  constructor(
    private categoriesService: CategoriesService,
    private platformSelectionService: PlatformSelectionService,
    private serviceSelectionService: ServiceSelectionService,
    private favoritesService: FavoritesService,
    private orderService: OrderService,
    private userService: UserService,
    private voucherService: VoucherService,
    private translate: TranslateService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadPlatforms();

    // Subscribe to platform selection events
    this.platformSelectionSubscription = this.platformSelectionService.platformSelected$.subscribe(platform => {
      this.handlePlatformSelection(platform);
    });

    // Subscribe to favorites
    this.favoritesSubscription = this.favoritesService.favorites$.subscribe(favoriteIds => {
      this.favoriteServiceIds = favoriteIds;
      this.updateFavoriteServices();
    });

    // Subscribe to user data
    this.userSubscription = this.userService.user$.subscribe(user => {
      this.user = user;

      // Recalculate fee if user data changes and we have a selected service
      if (this.selectedService) {
        this.calculateFee();
      }

      // If user is not loaded yet, trigger a fetch
      if (!user) {
        this.userService.get$.next();
      }
    });

    // Check for serviceId in query parameters
    this.route.queryParams.subscribe(params => {
      const serviceId = params['serviceId'];
      if (serviceId) {
        console.log('Service ID from URL:', serviceId);
        // Wait for platforms to load before selecting the service
        setTimeout(() => {
          this.selectServiceById(Number(serviceId));
        }, 1000);
      }
    });
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    if (this.platformSelectionSubscription) {
      this.platformSelectionSubscription.unsubscribe();
    }

    if (this.favoritesSubscription) {
      this.favoritesSubscription.unsubscribe();
    }

    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  handlePlatformSelection(platform: SuperPlatformRes): void {
    console.log('NewOrderComponent received platform selection:', platform.name);

    // Store the selected platform ID
    this.selectedPlatformId = platform.id;

    // Check if this is the "All networks" platform (id = 0)
    if (platform.id === 0) {
      // For "All networks", reload all categories from all platforms
      this.loadAllCategories();
      return;
    }

    // Filter categories to only show those from the selected platform
    const platformCategories = platform.categories
      .filter(category => !category.hide)
      .map(category => ({
        id: category.id.toString(),
        label: category.name,
        icon: platform.icon as IconName,
        sort: category.sort
      }))
      // Sort categories by sort field
      .sort((a, b) => a.sort - b.sort);

    // Update the categories list to only show categories from this platform
    this.categories = platformCategories;
    console.log('Filtered categories for platform:', platform.name, 'Categories count:', this.categories.length);

    // If there are categories for this platform, select the first one
    if (platformCategories.length > 0) {
      const categoryToSelect = platformCategories[0];
      console.log('Selecting category:', categoryToSelect.label);
      this.onCategorySelected(categoryToSelect);
    } else {
      // If no categories are available for this platform, clear the services
      this.services = [];
      this.selectedService = undefined;
      this.serviceSelectionService.selectService(undefined);
      this.calculateFee();
    }
  }

  // Helper method to load all categories from all platforms
  private loadAllCategories(): void {
    // Reset categories array
    this.categories = [];

    // Add all categories from all platforms
    this.platforms.forEach(platform => {
      if (!platform.hide) {
        platform.categories.forEach(category => {
          if (!category.hide) {
            this.categories.push({
              id: category.id.toString(),
              label: category.name,
              icon: platform.icon as IconName,
              sort: category.sort
            });
          }
        });

      }
    });

    // Sort categories by sort field
    this.categories.sort((a, b) => a.sort - b.sort);

    console.log('Loaded all categories, count:', this.categories.length);

    // Select the first category if available
    if (this.categories.length > 0) {
      this.selectedCategory = this.categories[0];
      this.onCategorySelected(this.selectedCategory);
    } else {
      // If no categories are available, clear the services
      this.services = [];
      this.selectedService = undefined;
      this.serviceSelectionService.selectService(undefined);
      this.calculateFee();
    }
  }

  loadPlatforms(): void {
    this.categoriesService.getPlatforms().subscribe(platforms => {
      this.platforms = platforms;

      // Initialize allServices array for search functionality
      this.allServices = [];

      // Initialize categories array to display all categories initially
      this.categories = [];

      // Store all categories and services for search functionality
      platforms.forEach(platform => {
        platform.categories.forEach(category => {
          if (!category.hide) {
            // Add category to the categories list
            this.categories.push({
              id: category.id.toString(),
              label: category.name,
              icon: platform.icon as IconName,
              sort: category.sort
            });

            // Add all services to allServices array for search functionality
            category.services.forEach(service => {
              this.allServices.push(service);
            });
          }
        });
      });

      // Sort categories by sort field for consistent display
      this.categories.sort((a, b) => a.sort - b.sort);

      // Always set the first category as the selected category and load its services
      if (this.categories.length > 0) {
        this.selectedCategory = this.categories[0];
        console.log('Setting default selected category:', this.selectedCategory);
        this.onCategorySelected(this.selectedCategory);
      } else {
        // Initialize with empty services if no categories are available
        this.services = [];
        this.selectedService = undefined;
        console.log('No categories available');
      }

      console.log('Platforms loaded, all categories displayed');
      console.log('Total categories:', this.categories.length);
      console.log('Total services available for search:', this.allServices.length);
    });
  }

  onCategorySelected(category: IconBaseModel): void {
    // Always set the selected category to the provided category
    this.selectedCategory = category;

    // Find the selected category in platforms data and get its services
    for (const platform of this.platforms) {
      const foundCategory = platform.categories.find(c => c.id.toString() === category.id);
      if (foundCategory) {
        // Map SuperGeneralSvRes to GService
        this.services = foundCategory.services;

        // Set the first service as the selected service
        if (this.services.length > 0) {
          this.selectedService = this.services[0];
          this.serviceSelectionService.selectService(this.selectedService);
          this.calculateFee();
        }

        console.log('Services loaded for category:', category.label, 'Services count:', this.services.length);
        break;
      }
    }
  }

  // Track active button
  activeButton: 'new_order' | 'favorite' | 'auto_subscription' = 'new_order';

  formData = {
    link: '',
    quantity: null as number | null,
    // avgTime: '22 phút',
    fee: 0,
    comments: '',
    voucher_code: ''
  };

  // Order creation related properties
  createdOrder: OrderRes | null = null;
  isOrderSuccess = false;
  isLoading = false;
  errorMessage = '';
  quantityError = '';

  // Price and discount related properties
  originalPrice = 0;
  discountPercent = 0;
  discountType = '';

  // Voucher related properties
  voucherDiscount = 0;
  voucherApplied = false;
  voucherError = '';
  isValidatingVoucher = false;

  // Calculate fee based on selected service and quantity, applying discounts
  calculateFee(): void {
    if (this.selectedService && this.formData.quantity !== null && this.formData.quantity > 0) {
      // Get the base price
      let basePrice = this.selectedService.price;
      let discountedPrice = basePrice;
      let discountPercent = 0;
      let discountType = '';

      // Check if service has special prices (priority)
      if (this.selectedService.special_prices && this.selectedService.special_prices.length > 0) {
        const specialPrice = this.selectedService.special_prices[0]; // Use the first special price

        if (specialPrice.discount_type === 'FIXED') {
          // Fixed price discount
          discountedPrice = specialPrice.discount_value;
          discountType = 'FIXED';
        } else if (specialPrice.discount_type === 'PERCENT') {
          // Percentage discount
          discountPercent = specialPrice.discount_value;
          discountedPrice = basePrice * (1 - discountPercent / 100);
          discountType = 'PERCENT';
        }
      } else {
        // Check for user's custom discount (only if no special prices)
        // Get the custom_discount from the GUserSuperRes model
        // Note: In the current UserRes model, custom_discount might not be available
        // This is a workaround until the UserRes model is updated
        const userCustomDiscount = this.user && (this.user as any).custom_discount ? (this.user as any).custom_discount : 0;

        if (userCustomDiscount > 0) {
          discountPercent = userCustomDiscount;
          discountedPrice = basePrice * (1 - discountPercent / 100);
          discountType = 'PERCENT';
        }
      }

      // Store original and discounted prices for display
      this.originalPrice = basePrice / 1000 * this.formData.quantity;
      this.discountPercent = discountPercent;
      this.discountType = discountType;

      // Calculate final fee based on discounted price
      this.formData.fee = discountedPrice / 1000 * this.formData.quantity;

      // Apply voucher discount if a voucher is applied
      if (this.voucherApplied && this.voucherDiscount > 0) {
        // Apply voucher discount (percentage)
        const voucherDiscountAmount = this.formData.fee * (this.voucherDiscount / 100);
        this.formData.fee -= voucherDiscountAmount;
      }

      console.log('Fee calculated:', this.formData.fee,
                 'Original Price:', basePrice,
                 'Discounted Price:', discountedPrice,
                 'Discount:', discountPercent + '%',
                 'Voucher Discount:', this.voucherDiscount + '%',
                 'Quantity:', this.formData.quantity);
    } else {
      this.formData.fee = 0;
      this.originalPrice = 0;
      this.discountPercent = 0;
      this.discountType = '';
    }
  }

  /**
   * Validate voucher code and apply discount if valid
   */
  validateVoucherCode(): void {
    // Reset voucher state
    this.voucherError = '';
    this.voucherApplied = false;
    this.voucherDiscount = 0;

    // Check if voucher code is provided
    if (!this.formData.voucher_code || this.formData.voucher_code.trim() === '') {
      // No voucher code provided, recalculate fee without voucher
      this.calculateFee();
      return;
    }

    // Set loading state
    this.isValidatingVoucher = true;

    // Call API to validate voucher code
    this.voucherService.verifyVoucherCode(this.formData.voucher_code.trim()).subscribe({
      next: (response: VoucherLiteRes) => {
        // Voucher is valid, apply discount
        this.voucherApplied = true;
        this.voucherDiscount = response.discount_value;

        // Recalculate fee with voucher discount
        this.calculateFee();

        console.log('Voucher applied successfully:', this.formData.voucher_code, 'Discount:', this.voucherDiscount + '%');
        this.isValidatingVoucher = false;
      },
      error: (error) => {
        // Voucher is invalid
        if (error.status === 404) {
          this.voucherError = this.translate.instant('invalid_voucher');
        } else if (error.status === 410) {
          this.voucherError = this.translate.instant('voucher_expired');
        } else if (error.status === 429) {
          this.voucherError = this.translate.instant('voucher_limit_reached');
        } else {
          this.voucherError = error.message || this.translate.instant('invalid_voucher');
        }

        this.voucherApplied = false;
        this.voucherDiscount = 0;

        // Recalculate fee without voucher discount
        this.calculateFee();

        console.error('Error validating voucher:', error);
        this.isValidatingVoucher = false;
      }
    });
  }

  /**
   * Clear voucher code and reset voucher state
   */
  clearVoucherCode(): void {
    // Reset voucher code and state
    this.formData.voucher_code = '';
    this.voucherError = '';
    this.voucherApplied = false;
    this.voucherDiscount = 0;

    // Recalculate fee without voucher discount
    this.calculateFee();

    console.log('Voucher code cleared');
  }

  // Validate quantity and calculate fee
  validateQuantityAndCalculateFee(): void {
    this.quantityError = '';

    if (!this.selectedService) {
      return;
    }

    // If service type is 'Comment', calculate quantity based on number of comment lines
    if (this.selectedService.type === 'Custom Comments' && this.formData.comments) {
      // Split comments by newline and filter out empty lines
      const commentLines = this.formData.comments
        .split('\n')
        .filter(line => line.trim().length > 0);

      // Update quantity based on number of comment lines
      this.formData.quantity = commentLines.length;
    }

    const min = this.selectedService.min || 10;
    const max = this.selectedService.max || 5000000;

    if (this.formData.quantity !== null) {
      if (this.formData.quantity < min) {
        this.quantityError = `Quantity must be at least ${min}`;
      } else if (this.formData.quantity > max) {
        this.quantityError = `Quantity cannot exceed ${max}`;
      }
    }

    // Calculate fee regardless of validation errors
    this.calculateFee();
  }

  // Get comments as array of strings
  getCommentsArray(): string[] {
    if (!this.formData.comments) {
      return [];
    }

    // Split by newline and filter out empty lines
    return this.formData.comments
      .split('\n')
      .filter(line => line.trim().length > 0);
  }

  onSubmit() {
    if (!this.selectedService) {
      this.errorMessage = 'Please select a service';
      return;
    }

    if (!this.formData.link) {
      this.errorMessage = 'Please enter a link';
      return;
    }

    if (!this.formData.quantity || this.formData.quantity <= 0) {
      this.errorMessage = 'Please enter a quantity';
      return;
    }

    // Validate quantity before submitting
    this.validateQuantityAndCalculateFee();
    if (this.quantityError) {
      this.errorMessage = this.quantityError;
      return;
    }

    // Reset error message
    this.errorMessage = '';
    this.isLoading = true;

    // Create base order data
    const orderData: CreateOrderReq = {
      service_id: this.selectedService.id,
      link: this.formData.link,
      quantity: this.formData.quantity
    };

    // Add voucher code if provided
    if (this.formData.voucher_code && this.formData.voucher_code.trim() !== '') {
      orderData.voucher_code = this.formData.voucher_code.trim();
    }

    // Add comments array if service type is 'Comment'
    if (this.selectedService.type === 'Custom Comments') {
      orderData.comments = this.getCommentsArray();

      // Validate that we have at least one comment
      if (orderData.comments.length === 0) {
        this.errorMessage = 'Please enter at least one comment';
        this.isLoading = false;
        return;
      }
    }

    this.orderService.createOrder(orderData).subscribe({
      next: (response) => {
        console.log('Order created successfully:', response);
        this.createdOrder = response;
        this.isOrderSuccess = true;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error creating order:', error);
        this.errorMessage = error.message || 'Failed to create order. Please try again.';
        this.isLoading = false;
      }
    });
  }



  // Reset order success state
  resetOrderState() {
    this.isOrderSuccess = false;
    this.createdOrder = null;
    this.formData.link = '';
    this.formData.quantity = null;
    this.formData.comments = '';
    this.formData.voucher_code = '';
    this.errorMessage = '';
    this.quantityError = '';
    this.voucherError = '';
    this.voucherApplied = false;
    this.voucherDiscount = 0;
    this.calculateFee();
  }

  // Set active button when clicked
  setActiveButton(button: 'new_order' | 'favorite' | 'auto_subscription') {
    this.activeButton = button;

    if (button === 'favorite') {
      // When favorite button is clicked, show only favorite services
      this.updateFavoriteServices();

      // Set the first favorite service as selected if available
      if (this.favoriteServices.length > 0) {
        this.selectedService = this.favoriteServices[0];
        this.serviceSelectionService.selectService(this.selectedService);
        this.calculateFee();
      } else {
        // If no favorites, clear selected service and set services to empty array
        this.selectedService = undefined;
        this.serviceSelectionService.selectService(undefined);
        // Create an empty array for services
        this.services = [];
        this.calculateFee();
        console.log('No favorite services available');
      }
    } else if (button === 'new_order') {
      // When new order button is clicked, restore normal view based on platform selection
      if (this.selectedCategory) {
        // If a category is already selected, use it
        this.onCategorySelected(this.selectedCategory);
      } else {
        // If no category is selected, check if we have a selected platform
        const selectedPlatformId = this.selectedPlatformId;
        if (selectedPlatformId !== null) {
          // Find the platform and reapply its categories
          const selectedPlatform = this.platforms.find(p => p.id === selectedPlatformId);
          if (selectedPlatform) {
            // Re-filter categories for this platform
            this.handlePlatformSelection(selectedPlatform);
          } else if (selectedPlatformId === 0) {
            // If "All networks" was selected but not found in platforms (could happen after reload)
            this.loadAllCategories();
          }
        } else {
          // If no platform is selected, load all categories
          this.loadAllCategories();
        }
      }
    }
  }

  // Update the list of favorite services
  updateFavoriteServices(): void {
    if (this.favoriteServiceIds.length === 0) {
      this.favoriteServices = [];
      // Even when there are no favorites, we need to update the services list
      if (this.activeButton === 'favorite') {
        this.services = [];
        console.log('No favorite services found, showing empty list');
      }
      return;
    }

    // Filter all services to get only favorites
    this.favoriteServices = this.allServices.filter(service =>
      this.favoriteServiceIds.includes(service.id)
    );

    // If we're in favorites mode, update the services list
    if (this.activeButton === 'favorite') {
      this.services = this.favoriteServices;
      console.log('Showing favorite services:', this.favoriteServices.length);
    }
  }

  // Search functionality - new implementation
  onSearchInput(event: Event): void {
    const term = (event.target as HTMLInputElement).value;
    this.searchTerm = term;
    console.log('Search input changed, term:', term);

    if (term.length === 0) {
      // If search term is cleared, show all services
      console.log('Search term cleared, showing all services');
      this.showAllServices();
      this.showAutocomplete = true;
    } else {
      // If there's a search term, perform search
      this.performSearch();
    }
  }

  // Handle click on search input
  onSearchClick(): void {
    console.log('Search input clicked, searchTerm:', this.searchTerm);
    if (this.searchTerm.length === 0) {
      // If no search term, show all available services
      this.showAllServices();
      // Always show autocomplete when clicking
      this.showAutocomplete = true;
      console.log('Showing autocomplete with all services');
    } else {
      // If there's a search term, perform search
      this.performSearch();
      console.log('Performing search with term:', this.searchTerm);
    }
  }

  // Show all available services
  private showAllServices(): void {
    if (this.activeButton === 'favorite') {
      this.searchResults = [...this.favoriteServices];
    } else {
      this.searchResults = [...this.allServices];
    }
    console.log('Showing all services:', this.searchResults.length);
  }

  // Perform search based on current search term
  private performSearch(): void {
    const term = this.searchTerm.trim();

    if (term.length === 0) {
      // If no search term, show all services
      this.showAllServices();
      this.showAutocomplete = true;
      return;
    }

    // Get the services to search from based on current mode
    const servicesToSearch = this.activeButton === 'favorite' ? this.favoriteServices : this.allServices;
    console.log('Searching in', servicesToSearch.length, 'services with term:', term);

    // Search by both name, description, and ID (always search all fields)
    const lowerCaseTerm = term.toLowerCase();
    this.searchResults = servicesToSearch.filter(service =>
      service.name.toLowerCase().includes(lowerCaseTerm) ||
      service.description.toLowerCase().includes(lowerCaseTerm) ||
      service.id.toString().includes(term)
    );

    console.log('Search results:', this.searchResults.length);
    // Always show autocomplete when performing search
    this.showAutocomplete = true;
  }

  // Handle autocomplete visibility change
  onAutocompleteVisibilityChange(isVisible: boolean): void {
    this.showAutocomplete = isVisible;
  }

  // Handle keyboard events for search input
  handleSearchKeydown(event: KeyboardEvent): void {
    // If Enter key is pressed and we have search results
    if (event.key === 'Enter' && this.searchResults.length > 0) {
      // Select the first result
      this.onServiceSelected(this.searchResults[0]);
      event.preventDefault();
    }

    // If Escape key is pressed, hide autocomplete
    if (event.key === 'Escape') {
      this.showAutocomplete = false;
      event.preventDefault();
    }

    // If ArrowDown key is pressed and autocomplete is not visible, show all services
    if (event.key === 'ArrowDown' && !this.showAutocomplete) {
      this.onSearchClick();
      event.preventDefault();
    }
  }

  // Handle service selection from dropdown
  onServiceDropdownSelected(service: SuperGeneralSvRes): void {
    this.selectedService = service;
    this.serviceSelectionService.selectService(service);
    console.log('Service selected from dropdown:', service);

    // Clear any previous quantity errors
    this.quantityError = '';

    // If service type is 'Comment', reset comments and quantity
    if (service.type === 'Custom Comments') {
      this.formData.comments = '';
      // Don't reset quantity here as it will be calculated from comments
    }

    // Validate quantity with the new service limits
    this.validateQuantityAndCalculateFee();

    // If we're in favorites mode, update the isFavorite property
    if (this.activeButton === 'favorite') {
      service.is_favorite = true;
    }
  }

  // Select a service by its ID
  selectServiceById(serviceId: number): void {
    console.log('Selecting service by ID:', serviceId);

    // Find the service in allServices
    const service = this.allServices.find(s => s.id === serviceId);

    if (service) {
      console.log('Found service:', service.name);
      // Use the existing onServiceSelected method to select the service
      this.onServiceSelected(service);
    } else {
      console.warn('Service not found with ID:', serviceId);
    }
  }

  // Handle service selection from autocomplete
  onServiceSelected(service: SuperGeneralSvRes): void {
    console.log('Service selected from autocomplete:', service);

    // First, directly set the selected service to ensure it's immediately available
    this.selectedService = service;
    this.serviceSelectionService.selectService(service);

    // Find the category for this service
    if (service.category_id) {
      const category = this.categories.find(c => c.id === service.category_id.toString());
      if (category) {
        console.log('Found category for service:', category.label);

        // Update the selected category without calling onCategorySelected to avoid resetting the service
        this.selectedCategory = category;

        // Find all services for this category
        for (const platform of this.platforms) {
          const foundCategory = platform.categories.find(c => c.id.toString() === category.id);
          if (foundCategory) {
            // Update the services array without changing the selected service
            this.services = foundCategory.services;
            console.log('Updated services list for category:', category.label, 'Services count:', this.services.length);
            break;
          }
        }

        // Make sure the selected service is in the services array
        if (!this.services.some(s => s.id === service.id)) {
          console.log('Adding selected service to services array');
          this.services = [service, ...this.services];
        }

        // Update both dropdowns UI with a delay to ensure Angular has updated the view
        setTimeout(() => {
          // Update category dropdown
          if (this.categoryDropdown) {
            console.log('Updating category dropdown UI with:', category.label);
            // Force the category dropdown to update its selected option
            if (this.categoryDropdown.updateSelectedOption) {
              this.categoryDropdown.updateSelectedOption(category);
            } else {
              this.categoryDropdown.selectedOption = category;
            }

            // If the dropdown is open, close it
            if (this.categoryDropdown.isOpen) {
              this.categoryDropdown.isOpen = false;
            }
          } else {
            console.warn('Category dropdown not available');
          }

          // Update service dropdown
          if (this.serviceDropdown) {
            console.log('Updating service dropdown UI with service:', service.id, service.name);
            // Force the dropdown to update its selected option
            this.serviceDropdown.selectedOption = service;

            // If the dropdown is open, close it
            if (this.serviceDropdown.isOpen) {
              this.serviceDropdown.isOpen = false;
              // We can't call removeDropdownFromDOM directly as it's private
              // The dropdown will handle cleanup on its own when isOpen is set to false
            }
          } else {
            console.warn('Service dropdown not available');
          }

          // Clear any previous quantity errors
          this.quantityError = '';

          // If service type is 'Comment', reset comments and quantity
          if (service.type === 'Custom Comments') {
            this.formData.comments = '';
            // Don't reset quantity here as it will be calculated from comments
          }

          // Validate quantity with the new service limits
          this.validateQuantityAndCalculateFee();
        }, 100);
      } else {
        console.log('Category not found for service, keeping service selected');

        // If we can't find the category, make sure the service is still in the services array
        if (!this.services.some(s => s.id === service.id)) {
          this.services = [service, ...this.services];
        }

        // Update the service dropdown UI
        setTimeout(() => {
          if (this.serviceDropdown) {
            this.serviceDropdown.selectedOption = service;
          }
          // Clear any previous quantity errors
          this.quantityError = '';

          // If service type is 'Comment', reset comments and quantity
          if (service.type === 'Custom Comments') {
            this.formData.comments = '';
            // Don't reset quantity here as it will be calculated from comments
          }

          // Validate quantity with the new service limits
          this.validateQuantityAndCalculateFee();
        }, 100);
      }
    } else {
      console.log('No category_id for service, keeping service selected');

      // If there's no category ID, make sure the service is still in the services array
      if (!this.services.some(s => s.id === service.id)) {
        this.services = [service, ...this.services];
      }

      // Update the service dropdown UI
      setTimeout(() => {
        if (this.serviceDropdown) {
          this.serviceDropdown.selectedOption = service;
        }
        this.calculateFee();
      }, 100);
    }

    // Clear search and hide autocomplete
    this.searchTerm = '';
    this.searchResults = [];
    this.showAutocomplete = false;
  }
}
