
package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.CategoryReq;
import tndung.vnfb.smm.dto.request.EditCategoryReq;
import tndung.vnfb.smm.dto.response.category.CategoryRes;
import tndung.vnfb.smm.service.CategoryService;

import javax.validation.Valid;
import java.util.List;



@RestController
@RequestMapping("/v1/categories")
@RequiredArgsConstructor
public class CategoryController {

    private final CategoryService categoryService;

    @PostMapping()
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<CategoryRes> add(@RequestBody @Valid CategoryReq req) {
        return ApiResponseEntity.success(categoryService.add(req));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<CategoryRes> edit(@PathVariable Long id, @RequestBody @Valid EditCategoryReq req) {
        return ApiResponseEntity.success(categoryService.edit(id, req));
    }

    @PutMapping("/{id}/active")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<CategoryRes> active(@PathVariable Long id) {
        return ApiResponseEntity.success(categoryService.active(id));
    }

    @PutMapping("/{id}/deactivate")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<CategoryRes> deactivate(@PathVariable Long id) {
        return ApiResponseEntity.success(categoryService.deactivate(id));
    }

    @GetMapping()
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL')")
    public ApiResponseEntity<List<CategoryRes>> getAll() {
        return ApiResponseEntity.success(categoryService.getAll());
    }


    @GetMapping("{id}/platform")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL')")
    public ApiResponseEntity<List<CategoryRes>> getByPlatformId(@PathVariable Long id) {
        return ApiResponseEntity.success(categoryService.getByPlatform(id));
    }

    @PatchMapping("/swap-sort")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> swapSort( @RequestParam Long id1,
                                               @RequestParam Long id2) {
        categoryService.swapSort(id1, id2);
        return ApiResponseEntity.success();
    }

    @PatchMapping("/reorder")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> reorder(@RequestParam Long id1,
                                            @RequestParam Long id2) {
        categoryService.reorder(id1, id2);
        return ApiResponseEntity.success();
    }

    @PutMapping("/{id}/platform")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL')")
    public ApiResponseEntity<String> changePlatform(@PathVariable Long id,
                                                               @RequestParam Long newPlatform) {
        categoryService.changePlatform(id, newPlatform);
        return ApiResponseEntity.success();
    }
}
