/* Admin container */
.admin-container {
  @apply w-full bg-white rounded-lg md:p-6 p-2;
}

/* Admin header */
.admin-header {
  @apply flex justify-between items-center mb-6;
}

.admin-title {
  @apply text-xl font-bold uppercase;
}

/* Search and filter container */
.search-filter-container {
  @apply mb-6;
}

/* Search input */
.search-input-wrapper {
  @apply relative flex items-center w-full;
}

.search-input {
  @apply w-full h-[52px] px-4 py-2 bg-[#f5f7fc] rounded-lg border-none focus:outline-none focus:ring-2 focus:ring-[var(--primary)];
}

.search-button {
  @apply absolute right-2 bg-[var(--primary)] text-white p-2 rounded-md;
}

/* Category container */
.category-container {
  @apply mb-8 bg-white rounded-lg overflow-hidden;
}

.category-header {
  @apply flex justify-between items-center p-4 bg-[#f5f7fc] border border-gray-200 rounded-t-lg;
}

.category-title {
  @apply flex items-center gap-2 font-medium;
}

.category-actions {
  @apply flex items-center gap-2;
}

/* Table styling */
.table-container {
  @apply overflow-x-auto rounded-b-lg;
}

.services-table {
  @apply w-full border-collapse;
}

.services-table th {
  @apply bg-gray-50 px-4 py-3 text-left font-medium text-gray-800 border border-gray-200;
}

.services-table td {
  @apply px-4 py-3 border border-gray-200;
}

.service-row {
  @apply hover:bg-gray-50;
}

/* Admin services table specific styling */
.admin-services-table {
  @apply w-full border-collapse;
}

.admin-services-table th {
  @apply bg-gray-50 px-4 py-3 text-left font-medium text-gray-800 border border-gray-200;
  height: 50px;
  vertical-align: middle;
}

.admin-services-table td {
  @apply px-4 py-3 border border-gray-200;
  height: 50px;
  vertical-align: middle;
}

/* Dropdown menu styling */
.dropdown-menu {
  @apply bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none;
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000; /* High z-index to stay on top */
  transition: opacity 0.2s ease;
  position: absolute; /* Use absolute positioning */
}

/* Removed hiding dropdown menu when scrolling to match admin-support.component.ts behavior */

.dropdown-menu a {
  @apply flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100;
}

.dropdown-menu a:hover {
  @apply bg-gray-100;
}

.dropdown-menu .border-t {
  @apply my-1;
}

/* Toggle Switch Styling */
.toggle-checkbox {
  right: 0;
  z-index: 1;
  border-color: #4CAF50;
  transition: all 0.3s;
}

.toggle-checkbox:checked {
  right: 0;
  border-color: #4CAF50;
}

.toggle-checkbox:checked + .toggle-label {
  background-color: #4CAF50;
}

.toggle-label {
  transition: background-color 0.3s;
}

/* Category grouping in single table */
.category-header-row {
  @apply bg-[#e0e6f4];
  border-bottom: 0;
  /* border-top: 0.5px solid #9ca3af !important; */
}

.category-header-row td {
  @apply p-0;
  background-color: #e0e6f4;
  padding: 6px 0;
  /* border: 0.5px solid #9ca3af !important;
  border-top: 0.5px solid #9ca3af !important; */
}

.category-header-row:first-child td {
  /* border-top: 0.5px solid #9ca3af !important; */
}

/* Override global table styles that might remove top border */
table.services-table,
table.admin-services-table {
  border-top: initial !important;
}

/* Ensure category headers always have top border */
tr.category-header-row {
  border-top: 0.5px solid #9ca3af !important;
}

/* Add spacing between categories */
.category-header-row:not(:first-child) {
  @apply mt-4;
}

.category-header-row:not(:first-child) td {
  border-top: 0.5px solid #9ca3af !important;
}

/* Style for empty category rows */
.empty-category-row {
  @apply bg-gray-50 text-gray-500;
}

/* Ensure proper spacing in the table */
table tbody tr:last-child td {
  @apply border-b;
}

/* Service row borders */
.service-row td {
  border: 1px solid #e5e7eb;
}

/* First service row after category header should connect with header */
.category-services tr:first-child td {
  border-top-color: transparent;
}

/* Deactivated service styling */
.service-row.deactivated {
  background-color: #fff5f5;
}

.service-row.deactivated td {
  color: #e53e3e;
}

/* Drag and drop styles */
.service-row {
  transition: all 0.2s ease;
  position: relative;
  cursor: grab;
}

.service-row:active {
  cursor: grabbing;
}

.service-row.dragging {
  opacity: 0.5;
  transform: scale(0.98);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.service-row.drop-target {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.05);
}

.service-row.drag-enter {
  background-color: rgba(0, 149, 246, 0.1);
}

/* Highlight drop area when dragging */
.service-row.highlight-drop-area {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.15);
  box-shadow: 0 0 8px rgba(0, 149, 246, 0.3);
}

/* Service drag preview styles */
.service-drag-preview {
  display: none;
}

.service-preview-table {
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.service-row-preview {
  background-color: white;
}

.service-row-preview.deactivated {
  background-color: #fff5f5;
  color: #e53e3e;
}

/* Empty drop zone styles */
.empty-drop-zone {
  transition: all 0.2s ease;
}

.empty-drop-zone td div {
  transition: all 0.2s ease;
}

.empty-drop-zone:hover td div {
  border-color: #0095f6;
  background-color: rgba(0, 149, 246, 0.05);
}

/* Category drag and drop styles */
.category-header-row {
  transition: all 0.2s ease;
  position: relative;
  cursor: grab;
}

.category-header-row:active {
  cursor: grabbing;
}

.category-header-row.dragging {
  opacity: 0.5;
  transform: scale(0.98);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.category-header-row.drop-target {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.05);
}

.category-header-row.drag-enter {
  background-color: rgba(0, 149, 246, 0.1);
}

/* Highlight drop area when dragging */
.category-header-row.highlight-drop-area {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.15);
  box-shadow: 0 0 8px rgba(0, 149, 246, 0.3);
}

.category-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.category-drag-handle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Category drag preview styles */
.category-drag-preview {
  display: none;
}

.category-preview-table {
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.category-header-row-preview {
  background-color: #f5f7fc;
}

/* CDK Drag and Drop Styles */
.cdk-drag-placeholder {
  @apply opacity-0;
  height: 0 !important;
  overflow: hidden;
  border: none !important;
}

.cdk-drag-placeholder td {
  height: 0 !important;
  padding: 0 !important;
  border: none !important;
}

.cdk-drag-animating {
  @apply transition-transform duration-300 ease-in-out;
}

/* When dragging, make other items shift smoothly - Table rows */
.category-services.cdk-drop-list-dragging .service-row:not(.cdk-drag-placeholder) {
  @apply transition-transform duration-300 ease-in-out;
}

/* When dragging, make other items shift smoothly - Card view */
.cdk-drop-list-dragging .service-card:not(.cdk-drag-placeholder) {
  @apply transition-transform duration-300 ease-in-out;
}

/* Ensure proper spacing during drag operations */
.cdk-drop-list-dragging .cdk-drag-placeholder {
  @apply transition-all duration-300 ease-in-out;
}

/* Table-specific drag styles */
.category-services {
  position: relative;
  width: 100%;
}

.category-services.cdk-drop-list {
  min-height: 50px; /* Ensure minimum height for drop zone */
  width: 100%;
}

/* Ensure table maintains full width during drag operations */
.category-services tr {
  width: 100%;
  display: table-row;
}

.category-services td {
  width: auto;
  display: table-cell;
}

/* Fix table layout for consistent column widths */
.table {
  table-layout: fixed;
  width: 100%;
}

.admin-services-table {
  width: 100% !important;
  table-layout: fixed !important;
  min-width: 100% !important;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

/* Force table to use full width */
.table .admin-services-table {
  width: 100% !important;
  table-layout: fixed !important;
}

/* Ensure table container uses full width */
.table {
  width: 100% !important;
  display: block;
}

/* Force the table element itself to use full width */
.table table {
  width: 100% !important;
  table-layout: fixed !important;
  display: table !important;
}

/* Ensure the main container uses full width */
.admin-container {
  width: 100% !important;
  max-width: none !important;
}

/* Force table cells to respect column widths */
.admin-services-table col {
  width: auto !important;
}

/* Override any conflicting styles */
.admin-services-table td,
.admin-services-table th {
  box-sizing: border-box;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Define specific column widths for better layout - Override colgroup */
.admin-services-table th:nth-child(1),
.admin-services-table td:nth-child(1) {
  width: 50px !important; /* Checkbox column */
  min-width: 50px !important;
  max-width: 50px !important;
}

.admin-services-table th:nth-child(2),
.admin-services-table td:nth-child(2) {
  width: 30% !important; /* Service name column */
  min-width: 200px !important;
}

.admin-services-table th:nth-child(3),
.admin-services-table td:nth-child(3) {
  width: 20% !important; /* API info column */
  min-width: 150px !important;
}

.admin-services-table th:nth-child(4),
.admin-services-table td:nth-child(4) {
  width: 80px !important; /* Min column */
  min-width: 80px !important;
  max-width: 80px !important;
}

.admin-services-table th:nth-child(5),
.admin-services-table td:nth-child(5) {
  width: 80px !important; /* Max column */
  min-width: 80px !important;
  max-width: 80px !important;
}

.admin-services-table th:nth-child(6),
.admin-services-table td:nth-child(6) {
  width: 120px !important; /* Refill column */
  min-width: 120px !important;
  max-width: 120px !important;
}

.admin-services-table th:nth-child(7),
.admin-services-table td:nth-child(7) {
  width: 120px !important; /* Average time column */
  min-width: 120px !important;
  max-width: 120px !important;
}

.admin-services-table th:nth-child(8),
.admin-services-table td:nth-child(8) {
  width: 60px !important; /* Action column */
  min-width: 60px !important;
  max-width: 60px !important;
}

/* Improve visual feedback during drag */
.service-row.cdk-drag {
  @apply cursor-grabbing;
}

.service-card.cdk-drag {
  @apply cursor-grabbing;
}

/* Service row preview for CDK drag */
.service-row-preview {
  @apply bg-white rounded-lg shadow-lg;
  width: var(--service-width);
  box-sizing: border-box;
}

.service-row-preview table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  min-width: 800px; /* Ensure minimum width for preview */
}

.service-preview-table {
  width: 100% !important;
  table-layout: fixed !important;
  min-width: 800px !important;
}

.service-row-preview table td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Apply same column widths to preview table */
.service-row-preview table td:nth-child(1) {
  width: 50px; /* Checkbox column */
}

.service-row-preview table td:nth-child(2) {
  width: 30%; /* Service name column */
}

.service-row-preview table td:nth-child(3) {
  width: 20%; /* API info column */
}

.service-row-preview table td:nth-child(4) {
  width: 80px; /* Min column */
}

.service-row-preview table td:nth-child(5) {
  width: 80px; /* Max column */
}

.service-row-preview table td:nth-child(6) {
  width: 120px; /* Refill column */
}

.service-row-preview table td:nth-child(7) {
  width: 120px; /* Average time column */
}

.service-row-preview table td:nth-child(8) {
  width: 60px; /* Action column */
}

.service-row-preview.deactivated {
  @apply bg-red-50;
}

/* Service card preview for CDK drag */
.service-card-preview {
  width: 300px; /* Fixed width for card preview */
  box-sizing: border-box;
}

/* Drag handle styling */
.service-drag-handle {
  @apply cursor-grab;
}

.service-drag-handle:active {
  @apply cursor-grabbing;
}

/* Custom cursor for draggable items */
.service-row {
  @apply cursor-pointer;
}

.service-row:hover {
  @apply bg-gray-50;
}

.service-row:active {
  @apply cursor-grabbing;
}

.service-card {
  @apply cursor-pointer;
}

.service-card:hover {
  @apply bg-gray-50;
}

.service-card:active {
  @apply cursor-grabbing;
}

/* Enhanced CDK drag preview styling */
.cdk-drag-preview {
  @apply shadow-xl;
  z-index: 1000;
  transform: rotate(5deg);
  transition: transform 0.2s ease-in-out;
}

.cdk-drag-preview.service-row-preview {
  width: var(--service-width) !important;
  max-width: var(--service-width) !important;
}

.cdk-drag-preview.service-card-preview {
  width: 300px !important;
  max-width: 300px !important;
}

/* Drop zone visual feedback */
.cdk-drop-list-receiving-drag {
  background-color: rgba(0, 149, 246, 0.02);
}

/* Dragging state for better visual feedback */
.cdk-drag-dragging {
  opacity: 0.6;
  transform: scale(0.95);
}

/* Ensure table rows maintain proper structure during drag */
.category-services tr.cdk-drag-placeholder {
  display: table-row !important;
  visibility: hidden !important;
  height: 0 !important;
  opacity: 0 !important;
}

.category-services tr.cdk-drag-placeholder td {
  height: 0 !important;
  padding: 0 !important;
  border: none !important;
  line-height: 0 !important;
  font-size: 0 !important;
  overflow: hidden !important;
}

/* Ensure proper table layout during drag operations */
.category-services.cdk-drop-list {
  display: table-row-group;
}

/* Fix for table row animations */
.category-services .service-row {
  display: table-row;
  position: relative;
}

/* Improve drag handle visibility */
.service-drag-handle {
  opacity: 0.6;
  transition: opacity 0.2s ease-in-out;
}

.service-row:hover .service-drag-handle,
.service-card:hover .service-drag-handle {
  opacity: 1;
}

/* Better visual feedback for drop zones */
.category-services.cdk-drop-list-receiving-drag {
  background-color: rgba(0, 149, 246, 0.03);
  border-radius: 4px;
}

/* Smooth animation for items moving out of the way */
.category-services.cdk-drop-list-dragging .service-row:not(.cdk-drag-placeholder) {
  transition: transform 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Card view drop zone feedback */
.cdk-drop-list-receiving-drag .service-card {
  background-color: rgba(0, 149, 246, 0.02);
}

/* Smooth transitions for all draggable elements */
.service-row,
.service-card {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out, background-color 0.2s ease-in-out;
}

/* Active drag state */
.cdk-drop-list-dragging .service-row:not(.cdk-drag-placeholder):not(.cdk-drag-animating) {
  transform: translateY(0);
}

.cdk-drop-list-dragging .service-card:not(.cdk-drag-placeholder):not(.cdk-drag-animating) {
  transform: translateY(0);
}

/* Status badge */
.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.status-success {
  @apply bg-green-100 text-green-800;
}

.status-error {
  @apply bg-red-100 text-red-800;
}

.status-warning {
  @apply bg-yellow-100 text-yellow-800;
}

/* Lifetime Refill badge */


/* Action menu */
.btn-icon {
  @apply p-2 rounded-full hover:bg-gray-200 transition-colors;
}

.action-menu {
  @apply absolute z-10 bg-white rounded-lg shadow-lg border border-gray-200 w-48;
}

/* Menu container */
.menu-container {
  position: relative;
  display: inline-block;
}

.action-item {
  @apply flex items-center gap-2 px-4 py-2 hover:bg-gray-100 cursor-pointer;
}

/* Button styles */
.btn-primary {
  @apply bg-[var(--primary)] text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-[var(--primary-hover)];
}

.btn-mess {
  @apply bg-[var(--primary)] text-white px-4 rounded-lg cursor-pointer transition-colors duration-300 hover:bg-[var(--primary-hover)] active:bg-[var(--primary-active)];
}



/* Styles for the single table with category grouping */
.category-services {
  display: contents; /* This makes the tbody behave as if it's not there, allowing its children to be part of the parent table */
}

/* Remove default table spacing */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

tbody tr {
  border-spacing: 0;
}

/* This section has been consolidated with the earlier category-header-row styles */

/* Category visibility toggle */
.header-left {
  @apply px-4 py-2;
}

.category-visibility-toggle {
  @apply flex items-center gap-2;
}

.toggle-arrow-btn {
  @apply p-1 rounded-full hover:bg-gray-100 transition-colors duration-200 focus:outline-none;
  @apply w-7 h-7 flex items-center justify-center;
  @apply bg-transparent border-0;
}

.toggle-arrow-btn:hover {
  @apply bg-gray-50;
}

.toggle-arrow-btn fa-icon {
  @apply text-lg text-gray-500;
}

/* Action menu button styling */
.action-menu-button {
  @apply p-2 rounded-full transition-colors duration-200 focus:outline-none;
  @apply bg-transparent border-0 cursor-pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-menu-button:hover {
  @apply bg-gray-100;
}

.action-menu-button fa-icon {
  @apply text-gray-600;
}

/* Maintain the same color when the menu is open */
.menu-open .action-menu-button,
.action-menu-button:active,
.action-menu-button:focus {
  @apply bg-transparent;
  color: inherit;
}

/* Checkbox styling */
.form-checkbox {
  @apply h-4 w-4 rounded border-gray-300 text-[var(--primary)] focus:ring-[var(--primary)];
  cursor: pointer;
  margin: 0 auto;
  display: block;
  vertical-align: middle;
}

/* Checkbox container */
td.text-center, th.text-center {
  @apply align-middle ;
  vertical-align: middle;
  width: 40px;
}

/* Checkbox in category header */
.category-header-row .form-checkbox {
  margin-right: 8px;
  display: inline-block;
}

/* Selected count styling */
th span.font-medium {
  @apply text-[var(--primary)];
}

/* API Info column styling */
.api-info-column {
  max-width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.api-info-column .api-id-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

.api-info-column .api-id {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.api-info-column .copy-icon {
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s;
  font-size: 14px;
}

.api-info-column .copy-icon:hover {
  color: #3b82f6;
}

.api-info-column .api-url-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

.api-info-column .api-url {
  font-size: 14px;
  color: #3b82f6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  max-width: calc(100% - 24px);
}

.api-info-column .api-url:hover {
  text-decoration: underline;
}

/* Mobile Card View Styling */
.service-card {
  transition: all 0.2s ease;
  position: relative;
}

.service-card.deactivated {
  background-color: #fff5f5;
  color: #e53e3e;
}

.service-card.dragging {
  opacity: 0.5;
  transform: scale(0.98);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.service-card.drop-target {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.05);
}

.service-card.drag-enter {
  background-color: rgba(0, 149, 246, 0.1);
}

.service-card.highlight-drop-area {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.15);
  box-shadow: 0 0 8px rgba(0, 149, 246, 0.3);
}

.category-header-card {
  transition: all 0.2s ease;
  position: relative;
  cursor: grab;
}

.category-header-card:active {
  cursor: grabbing;
}

.category-header-card.dragging {
  opacity: 0.5;
  transform: scale(0.98);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.category-header-card.drop-target {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.05);
}

.category-header-card.drag-enter {
  background-color: rgba(0, 149, 246, 0.1);
}

.category-header-card.highlight-drop-area {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.15);
  box-shadow: 0 0 8px rgba(0, 149, 246, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-header {
    @apply flex-col items-start gap-4;
  }

  .search-filter-container {
    @apply flex-col;
  }

  /* Make table scrollable horizontally on mobile */
  .table {
    @apply overflow-x-auto;
  }

  /* Ensure minimum width for table cells on mobile */
  table {
    min-width: 800px;
  }

  /* Adjust category header for mobile */
  .header-left {
    @apply flex-row justify-between w-full;
  }

  .toggle-arrow-btn {
    @apply w-8 h-8;
  }

  /* Improve touch targets for mobile */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Hide view mode toggle on small screens */
  @media (max-width: 480px) {
    .viewMode-toggle {
      display: none;
    }
  }

  /* Ensure proper spacing between cards */
  .service-card {
    margin-bottom: 8px;
  }

  /* Improve spacing in mobile cards */
  .grid-cols-1.gap-2 {
    row-gap: 0.75rem;
  }

  /* Improve menu positioning on mobile */
  .dropdown-menu {
    position: absolute;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    right: 0;
    left: auto !important;
  }

  /* Ensure action buttons are easy to tap */
  .action-menu-button {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Ensure menu items are easy to tap */
  .dropdown-menu a {
    padding: 12px 16px;
    min-height: 44px;
  }
}
