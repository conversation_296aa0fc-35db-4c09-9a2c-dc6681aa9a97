package tndung.vnfb.smm.service;

import org.springframework.data.domain.Sort;
import tndung.vnfb.smm.dto.request.ServiceReq;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.dto.response.service.SuperServiceRes;
import tndung.vnfb.smm.dto.response.smm.SMMServiceRes;
import tndung.vnfb.smm.entity.GService;

import java.math.BigDecimal;
import java.util.List;

public interface GSvService {
    List<SMMServiceRes> getSMMServices();
    List<SMMServiceRes> getServicesByProvider(Long providerId);
    List<ServiceRes> getAll();

    List<SuperServiceRes> getSuperAll();

    ServiceRes add(ServiceReq req);

    ServiceRes edit(Long id,ServiceReq req);



    List<ServiceRes> getByCategory(Long categoryId);

    SuperServiceRes getResById(Long serviceId);

    GService getById(Long serviceId);

    void swapSort(Long id1, Long id2);

    void reorder(Long id1, Long id2);

    void changePrice(Long id, BigDecimal percent);


    SuperServiceRes deactivate(Long id);

    SuperServiceRes active(Long id);

    GService findById(Long id);

    void changeCategory(Long serviceId, Long newCategory);

    void priceSort(Long categoryId, Sort.Direction direction);

    SuperServiceRes duplicate(Long serviceId);

    void delete(Long id);
}
