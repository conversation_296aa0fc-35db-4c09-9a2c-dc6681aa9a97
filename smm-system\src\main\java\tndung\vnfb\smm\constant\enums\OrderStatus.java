package tndung.vnfb.smm.constant.enums;

import lombok.Getter;

@Getter
public enum OrderStatus {
    PENDING("Pending"),
    PROCESSING("Processing"),
    IN_PROGRESS("In progress"),
    COMPLETED("Completed"),
    PARTIAL("Partial"),
    CANCELED("Canceled"),
    FAILED("Failed");


    private final String value;


    public static OrderStatus findByValue(String value) {
        for(OrderStatus e : values()) {
            if(e.value.equals(value)) return e;
        }
        return null;
    }
    OrderStatus(final String id) {
        this.value = id;
    }
}
