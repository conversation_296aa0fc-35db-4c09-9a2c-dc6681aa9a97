import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { BehaviorSubject, Observable, Subject, finalize, tap, of, switchMap, map, catchError, throwError } from 'rxjs';
import { ConfigService } from './config.service';
import { ToastService } from './toast.service';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';
import { SuperGeneralSvReq } from '../../model/request/super-general-sv-req.model';
import { SuperCategoryRes } from '../../model/response/super-category.model';
import { SuperPlatformRes } from '../../model/response/super-platform.model';
import { ProviderRes } from '../../model/response/provider-res.model';
import { SMMServiceRes } from '../../model/response/smm-service-res.model';
import { SpecialPriceRes } from '../../model/response/special-price-res.model';
import { CustomDiscountServiceReq, DiscountType } from '../../model/request/custom-discount-service-req.model';
import { isPlatformBrowser } from '@angular/common';
import { Status } from '../../constant/status';
import { PlatformReq } from '../../model/request/platform-req.model';
import { CategoryReq } from '../../model/request/category-req.model';

@Injectable({
  providedIn: 'root'
})
export class AdminServiceService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _loadingAction$ = new BehaviorSubject<boolean>(false);
  private _services$ = new BehaviorSubject<SuperGeneralSvRes[]>([]);
  private _servicesByCategory$ = new BehaviorSubject<{ [categoryId: string]: SuperGeneralSvRes[] }>({});
  private _search$ = new Subject<string>();
  private _platforms$ = new BehaviorSubject<SuperPlatformRes[]>([]);
  private _categories$ = new BehaviorSubject<SuperCategoryRes[]>([]);
  private _loadingServiceIds = new Set<number>();

  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    private toastService: ToastService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    // Set up search subscription in browser environment
    if (isPlatformBrowser(this.platformId)) {
      this._search$
        .pipe(
          tap(() => this._loading$.next(true)),
          switchMap(term => this.searchServices(term))
        )
        .subscribe();
    }
  }

  // Expose observables for components to subscribe to
  get loading$() {
    return this._loading$.asObservable();
  }

  get loadingAction$() {
    return this._loadingAction$.asObservable();
  }

  get services$() {
    return this._services$.asObservable();
  }
  getServiceValue() {
    return this._services$.value;
  }

  get servicesByCategory$() {
    return this._servicesByCategory$.asObservable();
  }

  // Get the current value of servicesByCategory
  getServicesByCategoryValue(): { [categoryId: string]: SuperGeneralSvRes[] } {
    return this._servicesByCategory$.value;
  }

  // Update the servicesByCategory with a new value
  updateServicesByCategory(servicesByCategory: { [categoryId: string]: SuperGeneralSvRes[] }): void {
    this._servicesByCategory$.next(servicesByCategory);
    this.updateServicesArray();
  }

  get platforms$() {
    return this._platforms$.asObservable();
  }

  get categories$() {
    return this._categories$.asObservable();
  }

  // Check if a specific service is loading
  isServiceLoading(serviceId: number): boolean {
    return this._loadingServiceIds.has(serviceId);
  }

  // Trigger a search
  search(term: string): void {
    this._search$.next(term);
  }

  // Get platforms and categories with services
  getPlatformsWithServices(): Observable<SuperPlatformRes[]> {
    if (!isPlatformBrowser(this.platformId)) {
      return of([]);
    }

    this._loading$.next(true);
    return this.http
      .get<SuperPlatformRes[]>(`${this.configService.apiUrl}/platforms/super`)
      .pipe(
        map(platforms => {
          // Sort platforms by sort field
          platforms = platforms.sort((a, b) => a.sort - b.sort);

          // Process platforms and categories
          platforms.forEach(platform => {
            // Sort categories by sort field
            platform.categories = [...platform.categories]
              .sort((a, b) => a.sort - b.sort);

            platform.categories.forEach(category => {
              category.hide = category.status !== Status.ACTIVATED;
              category.platformIcon = platform.icon;
            });
           // platform.hide = platform.categories.every(c => c.hide);
          });

          // Update BehaviorSubjects
          this._platforms$.next(platforms);

          // Extract all categories
          const allCategories: SuperCategoryRes[] = [];
          platforms.forEach(platform => {
            platform.categories.forEach(category => {
              allCategories.push(category);
            });
          });

          // Sort all categories by sort field
          allCategories.sort((a, b) => a.sort - b.sort);
          this._categories$.next(allCategories);

          // Group services by category
          const servicesByCategory: { [categoryId: string]: SuperGeneralSvRes[] } = {};
          allCategories.forEach(category => {
            if (!category.hide) {
              servicesByCategory[category.id.toString()] = category.services || [];
            }
          });
          this._servicesByCategory$.next(servicesByCategory);

          // Flatten all services into a single array
          const allServices: SuperGeneralSvRes[] = [];
          Object.values(servicesByCategory).forEach(services => {
            allServices.push(...services);
          });
          allServices.sort((a, b) => a.sort - b.sort);
          this._services$.next(allServices);

          return platforms;
        }),
        finalize(() => this._loading$.next(false))
      );
  }



  // Get a specific service by ID
  getServiceById(id: number): Observable<SuperGeneralSvRes> {
    this._loadingServiceIds.add(id);
    return this.http.get<SuperGeneralSvRes>(`${this.configService.apiUrl}/services/${id}`)
      .pipe(
        catchError(error => {
          console.error('Error getting service by ID:', error);
          this.toastService.showError(error?.message || 'Failed to load service details');
          return throwError(() => error);
        }),
        finalize(() => {
          this._loadingServiceIds.delete(id);
        })
      );
  }

  // Create a new service
  createService(service: SuperGeneralSvReq): Observable<SuperGeneralSvRes> {
    this._loadingAction$.next(true);
    return this.http
      .post<SuperGeneralSvRes>(`${this.configService.apiUrl}/services`, service)
      .pipe(
        tap(newService => {
          // Update services array
          const currentServices = this._services$.value;
          this._services$.next([...currentServices, newService]);

          // Update services by category
          const currentServicesByCategory = this._servicesByCategory$.value;
          // Add null check for category_id
          const categoryId = newService.category_id ? newService.category_id.toString() : null;
          if (categoryId && currentServicesByCategory[categoryId]) {
            currentServicesByCategory[categoryId] = [...currentServicesByCategory[categoryId], newService];
          } else if (categoryId) {
            currentServicesByCategory[categoryId] = [newService];
          }
          this._servicesByCategory$.next({...currentServicesByCategory});
        }),
        catchError(error => {
          console.error('Error creating service:', error);
          this.toastService.showError(error?.message || 'Failed to create service');
          return throwError(() => error);
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Update an existing service
  updateService(id: number, service: SuperGeneralSvReq): Observable<SuperGeneralSvRes> {
    this._loadingServiceIds.add(id);
    this._loadingAction$.next(true);
    return this.http
      .put<SuperGeneralSvRes>(`${this.configService.apiUrl}/services/${id}`, service)
      .pipe(
        tap(updatedService => {
          // Update services array
          const currentServices = this._services$.value;
          const index = currentServices.findIndex(s => s.id === id);
          if (index !== -1) {
            currentServices[index] = updatedService;
            this._services$.next([...currentServices]);
          }

          // Update services by category
          this.updateServiceInCategoryMap(updatedService);
        }),
        catchError(error => {
          console.error('Error updating service:', error);
          this.toastService.showError(error?.message || 'Failed to update service');
          return throwError(() => error);
        }),
        finalize(() => {
          this._loadingServiceIds.delete(id);
          this._loadingAction$.next(false);
        })
      );
  }

  // Delete a service
  deleteService(id: number): Observable<void> {
    this._loadingServiceIds.add(id);
    this._loadingAction$.next(true);

    // Find the service to get its category before deletion
    const serviceToDelete = this._services$.value.find(s => s.id === id);
    let categoryId: string | null = null;

    if (serviceToDelete) {
      categoryId = serviceToDelete.category_id.toString();
    }

    return this.http
      .delete<void>(`${this.configService.apiUrl}/services/${id}`)
      .pipe(
        tap(() => {
          // Update services array
          const currentServices = this._services$.value;
          this._services$.next(currentServices.filter(service => service.id !== id));

          // Update services by category
          if (categoryId) {
            const currentServicesByCategory = this._servicesByCategory$.value;
            if (currentServicesByCategory[categoryId]) {
              currentServicesByCategory[categoryId] = currentServicesByCategory[categoryId]
                .filter(service => service.id !== id);
              this._servicesByCategory$.next({...currentServicesByCategory});
            }
          }
        }),
        catchError(error => {
          console.error('Error deleting service:', error);
          this.toastService.showError(error?.message || 'Failed to delete service');
          return throwError(() => error);
        }),
        finalize(() => {
          this._loadingServiceIds.delete(id);
          this._loadingAction$.next(false);
        })
      );
  }

  // Activate a service
  activateService(id: number): Observable<SuperGeneralSvRes> {
    this._loadingServiceIds.add(id);
    this._loadingAction$.next(true);
    return this.http
      .put<SuperGeneralSvRes>(`${this.configService.apiUrl}/services/${id}/active`, {})
      .pipe(
        tap(updatedService => {
          // Update services array
          const currentServices = this._services$.value;
          const index = currentServices.findIndex(s => s.id === id);
          if (index !== -1) {
            currentServices[index] = updatedService;
            this._services$.next([...currentServices]);
          }

          // Update services by category
          this.updateServiceInCategoryMap(updatedService);
        }),
        finalize(() => {
          this._loadingServiceIds.delete(id);
          this._loadingAction$.next(false);
        })
      );
  }

  // Deactivate a service
  deactivateService(id: number): Observable<SuperGeneralSvRes> {
    this._loadingServiceIds.add(id);
    this._loadingAction$.next(true);
    return this.http
      .put<SuperGeneralSvRes>(`${this.configService.apiUrl}/services/${id}/deactivate`, {})
      .pipe(
        tap(updatedService => {
          // Update services array
          const currentServices = this._services$.value;
          const index = currentServices.findIndex(s => s.id === id);
          if (index !== -1) {
            currentServices[index] = updatedService;
            this._services$.next([...currentServices]);
          }

          // Update services by category
          this.updateServiceInCategoryMap(updatedService);
        }),
        finalize(() => {
          this._loadingServiceIds.delete(id);
          this._loadingAction$.next(false);
        })
      );
  }



  // Import services from file
  importServicesFromFile(file: File): Observable<SuperGeneralSvRes[]> {
    this._loadingAction$.next(true);
    const formData = new FormData();
    formData.append('file', file);

    return this.http
      .post<SuperGeneralSvRes[]>(`${this.configService.apiUrl}/services/import`, formData)
      .pipe(
        tap(importedServices => {
          // Update services array
          const currentServices = this._services$.value;
          this._services$.next([...currentServices, ...importedServices]);

          // Update services by category
          const currentServicesByCategory = this._servicesByCategory$.value;

          importedServices.forEach(service => {
            if (service.category_id) {
              const categoryId = service.category_id.toString();
              if (!currentServicesByCategory[categoryId]) {
                currentServicesByCategory[categoryId] = [];
              }
              currentServicesByCategory[categoryId].push(service);
            }
          });

          this._servicesByCategory$.next({...currentServicesByCategory});
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Get all providers
  getProviders(): Observable<ProviderRes[]> {
    return this.http.get<ProviderRes[]>(`${this.configService.apiUrl}/providers`)
      .pipe(
        catchError(error => {
          console.error('Error getting providers:', error);
          this.toastService.showError(error?.message || 'Failed to load providers');
          return throwError(() => error);
        })
      );
  }

  // Add a new provider
  addProvider(url: string, secretKey: string): Observable<ProviderRes> {
    this._loadingAction$.next(true);
    const requestBody = {
      url: url,
      secret_key: secretKey
    };

    return this.http.post<ProviderRes>(`${this.configService.apiUrl}/providers`, requestBody)
      .pipe(
        catchError(error => {
          console.error('Error adding provider:', error);
          this.toastService.showError(error?.message || 'Failed to add provider');
          return throwError(() => error);
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Change API key for a provider
  changeProviderApiKey(providerId: number, apiKey: string): Observable<any> {
    this._loadingAction$.next(true);
    return this.http.patch<any>(`${this.configService.apiUrl}/providers/${providerId}/api-key?api-key=${apiKey}`, {})
      .pipe(
        catchError(error => {
          console.error('Error changing provider API key:', error);
          this.toastService.showError(error?.message || 'Failed to change provider API key');
          return throwError(() => error);
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Change balance alert for a provider
  changeProviderBalanceAlert(providerId: number, balanceAlert: number): Observable<any> {
    this._loadingAction$.next(true);
    return this.http.patch<any>(`${this.configService.apiUrl}/providers/${providerId}/balance-alert?balance-alert=${balanceAlert}`, {})
      .pipe(
        catchError(error => {
          console.error('Error changing provider balance alert:', error);
          this.toastService.showError(error?.message || 'Failed to change provider balance alert');
          return throwError(() => error);
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Delete a provider
  deleteProvider(providerId: number): Observable<any> {
    this._loadingAction$.next(true);
    return this.http.delete<any>(`${this.configService.apiUrl}/providers/${providerId}`)
      .pipe(
        catchError(error => {
          console.error('Error deleting provider:', error);
          this.toastService.showError(error?.message || 'Failed to delete provider');
          return throwError(() => error);
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Get services from a specific provider
  getProviderServices(providerId: number): Observable<SMMServiceRes[]> {
    return this.http.get<SMMServiceRes[]>(`${this.configService.apiUrl}/services/${providerId}/smm`)
      .pipe(
        catchError(error => {
          console.error('Error getting provider services:', error);
          this.toastService.showError(error?.message || 'Failed to load provider services');
          return throwError(() => error);
        })
      );
  }

  // Delete a platform
  deletePlatform(id: number): Observable<void> {
    this._loadingAction$.next(true);
    return this.http
      .delete<void>(`${this.configService.apiUrl}/platforms/${id}`)
      .pipe(
        tap(() => {
          // Update platforms array
          const currentPlatforms = this._platforms$.value;
          this._platforms$.next(currentPlatforms.filter(platform => platform.id !== id));
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Reorder platform positions
  swapPlatformPositions(id1: number, id2: number): Observable<void> {
    this._loadingAction$.next(true);
    return this.http
      .patch<void>(`${this.configService.apiUrl}/platforms/reorder?id1=${id1}&id2=${id2}`, {})
      .pipe(
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Import services from provider
  importServices(providerId: number, serviceIds: string[]): Observable<SuperGeneralSvRes[]> {
    this._loadingAction$.next(true);

    return this.http
      .post<SuperGeneralSvRes[]>(
        `${this.configService.apiUrl}/services/import-from-provider`,
        { providerId, serviceIds }
      )
      .pipe(
        tap(importedServices => {
          // Update services array
          const currentServices = this._services$.value;
          this._services$.next([...currentServices, ...importedServices]);

          // Update services by category
          const currentServicesByCategory = this._servicesByCategory$.value;

          importedServices.forEach(service => {
            if (service.category_id) {
              const categoryId = service.category_id.toString();
              if (!currentServicesByCategory[categoryId]) {
                currentServicesByCategory[categoryId] = [];
              }
              currentServicesByCategory[categoryId].push(service);
            }
          });

          this._servicesByCategory$.next({...currentServicesByCategory});
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Search services
  searchServices(keyword: string): Observable<SuperGeneralSvRes[]> {
    if (keyword.trim() === '') {
      // If search term is empty, refresh all data
      return this.getPlatformsWithServices().pipe(
        map(() => this._services$.value)
      );
    }

    this._loading$.next(true);
    let params = new HttpParams().set('keyword', keyword);

    return this.http
      .get<SuperGeneralSvRes[]>(`${this.configService.apiUrl}/services/search`, { params })
      .pipe(
        tap(services => {
          this._services$.next(services);

          // Group services by category
          const servicesByCategory: { [categoryId: string]: SuperGeneralSvRes[] } = {};

          services.forEach(service => {
            if (service.category_id) {
              const categoryId = service.category_id.toString();
              if (!servicesByCategory[categoryId]) {
                servicesByCategory[categoryId] = [];
              }
              servicesByCategory[categoryId].push(service);
            }
          });

          this._servicesByCategory$.next(servicesByCategory);
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Helper method to update a service in the category map
  private updateServiceInCategoryMap(service: SuperGeneralSvRes): void {
    if (!service.category_id) {
      console.warn('Service has no category_id, skipping category map update');
      return;
    }

    const currentServicesByCategory = this._servicesByCategory$.value;
    const categoryId = service.category_id.toString();

    if (currentServicesByCategory[categoryId]) {
      const index = currentServicesByCategory[categoryId].findIndex(s => s.id === service.id);
      if (index !== -1) {
        currentServicesByCategory[categoryId][index] = service;
      } else {
        currentServicesByCategory[categoryId].push(service);
      }
    } else {
      currentServicesByCategory[categoryId] = [service];
    }

    this._servicesByCategory$.next({...currentServicesByCategory});
  }

  // Swap service sort order via API
  swapServiceSort(id1: number, id2: number): Observable<void> {
    this._loadingAction$.next(true);
    return this.http
      .patch<void>(`${this.configService.apiUrl}/services/swap-sort?id1=${id1}&id2=${id2}`, {})
      .pipe(
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Reorder service positions via API (reorder-based instead of swap-based)
  reorderServicePositions(id1: number, id2: number): Observable<void> {
    this._loadingAction$.next(true);
    return this.http
      .patch<void>(`${this.configService.apiUrl}/services/reorder?id1=${id1}&id2=${id2}`, {})
      .pipe(
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Change service category via API
  changeServiceCategory(serviceId: number, newCategoryId: number): Observable<void> {
    this._loadingAction$.next(true);
    return this.http
      .put<void>(`${this.configService.apiUrl}/services/${serviceId}/categories?newCategory=${newCategoryId}`, {})
      .pipe(
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Add special price (custom discount) for a service
  addCustomDiscount(userId: number, serviceId: number, type: DiscountType, customDiscount: number): Observable<SpecialPriceRes> {
    this._loadingAction$.next(true);

    const customDiscountReq: CustomDiscountServiceReq = {
      service_id: serviceId,
      type: type,
      custom_discount: customDiscount
    };

    return this.http
      .post<SpecialPriceRes>(`${this.configService.apiUrl}/users/${userId}/services/custom-discount`, customDiscountReq)
      .pipe(
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Duplicate a service
  duplicateService(id: number): Observable<SuperGeneralSvRes> {
    this._loadingServiceIds.add(id);
    this._loadingAction$.next(true);
    return this.http
      .post<SuperGeneralSvRes>(`${this.configService.apiUrl}/services/${id}/duplicate`, {})
      .pipe(
        tap(duplicatedService => {
          // Update services array
          const currentServices = this._services$.value;
          this._services$.next([...currentServices, duplicatedService]);

          // Update services by category
          const currentServicesByCategory = this._servicesByCategory$.value;
          const categoryId = duplicatedService.category_id.toString();
          if (currentServicesByCategory[categoryId]) {
            currentServicesByCategory[categoryId] = [...currentServicesByCategory[categoryId], duplicatedService];
          } else {
            currentServicesByCategory[categoryId] = [duplicatedService];
          }
          this._servicesByCategory$.next({...currentServicesByCategory});
        }),
        finalize(() => {
          this._loadingServiceIds.delete(id);
          this._loadingAction$.next(false);
        })
      );
  }

  // Swap category sort order via API
  swapCategorySort(id1: number, id2: number): Observable<void> {
    this._loadingAction$.next(true);
    return this.http
      .patch<void>(`${this.configService.apiUrl}/categories/swap-sort?id1=${id1}&id2=${id2}`, {})
      .pipe(
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Reorder a service within the same category
  reorderServiceInCategory(categoryId: string, oldIndex: number, newIndex: number): void {
    console.log(`Reordering service in category ${categoryId} from index ${oldIndex} to ${newIndex}`);

    try {
      // Get the current services by category
      const currentServicesByCategory = {...this._servicesByCategory$.value};

      // Get the services for the specified category
      const categoryServices = [...(currentServicesByCategory[categoryId] || [])];

      console.log('Category services:', categoryServices);

      // Validate indices
      if (categoryServices.length === 0) {
        console.error('Category has no services');
        return;
      }

      if (oldIndex < 0 || oldIndex >= categoryServices.length) {
        console.error(`Invalid old index: ${oldIndex}, max: ${categoryServices.length - 1}`);
        return;
      }

      if (newIndex < 0 || newIndex >= categoryServices.length) {
        console.error(`Invalid new index: ${newIndex}, max: ${categoryServices.length - 1}`);
        return;
      }

      // If the indices are the same, no need to reorder
      if (oldIndex === newIndex) {
        console.log('Indices are the same, no need to reorder');
        return;
      }

      // Get the service being moved (for logging purposes)
      // const movedService = categoryServices[oldIndex];

      // Create a copy of the array before modification for API calls
      const originalServices = [...categoryServices];

      // Remove the service from its old position
      const [removedService] = categoryServices.splice(oldIndex, 1);

      if (!removedService) {
        console.error('Failed to remove service from its old position');
        return;
      }

      // Insert the service at its new position
      categoryServices.splice(newIndex, 0, removedService);

      // Update the services by category
      currentServicesByCategory[categoryId] = categoryServices;

      // Update the BehaviorSubject
      this._servicesByCategory$.next(currentServicesByCategory);

      // Update the services array
      this.updateServicesArray();

      // Handle API calls for updating the sort order
      if (Math.abs(oldIndex - newIndex) === 1) {
        // For adjacent items, just do a single swap
        const service1 = categoryServices[newIndex];
        const service2 = categoryServices[oldIndex < newIndex ? newIndex - 1 : newIndex + 1];

        if (service1 && service2) {
          this.swapServiceSort(service1.id, service2.id).subscribe({
            next: () => console.log('Service sort order updated successfully'),
            error: (error) => console.error('Error updating service sort order:', error)
          });
        }
      } else {
        // For non-adjacent items, we need to make multiple API calls to update the sort order
        console.log('Handling non-adjacent swap with multiple API calls');

        // Determine the direction of movement
        const direction = oldIndex < newIndex ? 1 : -1;

        // Make a series of swaps to move the item to its final position
        const swapPromises: Promise<void>[] = [];

        // Create a temporary array to track the position of the moved service during swaps
        let currentPosition = oldIndex;
        const tempArray = [...originalServices];

        // Perform swaps until we reach the target position
        while (currentPosition !== newIndex) {
          const nextPosition = currentPosition + direction;

          // Swap the service with the adjacent service in the direction of movement
          const serviceToMove = tempArray[currentPosition];
          const adjacentService = tempArray[nextPosition];

          // Swap in the temporary array
          tempArray[currentPosition] = adjacentService;
          tempArray[nextPosition] = serviceToMove;

          // Create a promise for the API call
          const swapPromise = new Promise<void>((resolve, reject) => {
            this.swapServiceSort(serviceToMove.id, adjacentService.id).subscribe({
              next: () => {
                console.log(`Swapped service ${serviceToMove.id} with ${adjacentService.id}`);
                resolve();
              },
              error: (error) => {
                console.error(`Error swapping services ${serviceToMove.id} and ${adjacentService.id}:`, error);
                reject(error);
              }
            });
          });

          swapPromises.push(swapPromise);

          // Update the current position
          currentPosition = nextPosition;
        }

        // Execute all swaps in sequence
        Promise.all(swapPromises)
          .then(() => console.log('All service swaps completed successfully'))
          .catch(error => console.error('Error during service swaps:', error));
      }

      console.log('Services reordered successfully');
    } catch (error) {
      console.error('Error reordering service in category:', error);
    }
  }

  // Move a service from one category to another
  moveServiceToCategory(sourceCategory: string, targetCategory: string, sourceIndex: number, targetIndex: number): void {
    console.log(`Moving service from category ${sourceCategory}, index ${sourceIndex} to category ${targetCategory}, index ${targetIndex}`);

    try {
      // Get the current services by category
      const currentServicesByCategory = {...this._servicesByCategory$.value};

      // Get the services for the source and target categories
      const sourceServices = [...(currentServicesByCategory[sourceCategory] || [])];
      const targetServices = [...(currentServicesByCategory[targetCategory] || [])];

      console.log('Source services:', sourceServices);
      console.log('Target services:', targetServices);

      // Validate indices
      if (sourceServices.length === 0) {
        console.error('Source category has no services');
        return;
      }

      if (sourceIndex < 0 || sourceIndex >= sourceServices.length) {
        console.error(`Invalid source index: ${sourceIndex}, max: ${sourceServices.length - 1}`);
        return;
      }

      // Target index can be equal to the length of the array (to append at the end)
      if (targetIndex < 0 || targetIndex > targetServices.length) {
        console.error(`Invalid target index: ${targetIndex}, max: ${targetServices.length}`);
        return;
      }

      // Get the service to be moved
      const serviceToMove = sourceServices[sourceIndex];

      if (!serviceToMove) {
        console.error('Failed to find service in source category');
        return;
      }

      // Call the API to change the category
      this.changeServiceCategory(serviceToMove.id, parseInt(targetCategory)).subscribe({
        next: () => {
          console.log('Service category changed successfully');

          // Remove the service from the source category
          const [movedService] = sourceServices.splice(sourceIndex, 1);

          if (!movedService) {
            console.error('Failed to remove service from source category');
            return;
          }

          // Update the service's category_id
          movedService.category_id = parseInt(targetCategory);

          // Insert the service into the target category
          targetServices.splice(targetIndex, 0, movedService);

          // Update the services by category
          currentServicesByCategory[sourceCategory] = sourceServices;
          currentServicesByCategory[targetCategory] = targetServices;

          // Update the BehaviorSubject
          this._servicesByCategory$.next(currentServicesByCategory);

          // Update the services array
          this.updateServicesArray();

          // If there are services before and after the inserted position, update the sort order
          if (targetServices.length > 1) {
            const adjacentIndex = targetIndex > 0 ? targetIndex - 1 : targetIndex + 1;
            if (adjacentIndex < targetServices.length) {
              this.swapServiceSort(movedService.id, targetServices[adjacentIndex].id).subscribe({
                next: () => console.log('Service sort order updated successfully'),
                error: (error) => console.error('Error updating service sort order:', error)
              });
            }
          }

          console.log('Service moved successfully');
        },
        error: (error) => {
          console.error('Error changing service category:', error);
        }
      });
    } catch (error) {
      console.error('Error moving service to category:', error);
    }
  }

  // Update the services array after reordering or moving services
  private updateServicesArray(): void {
    const currentServicesByCategory = this._servicesByCategory$.value;

    // Flatten all services into a single array
    const allServices: SuperGeneralSvRes[] = [];
    Object.values(currentServicesByCategory).forEach(services => {
      allServices.push(...services);
    });

    // Update the services BehaviorSubject
    this._services$.next(allServices);
  }

  // Sort services in a category by price (Low to High)
  sortCategoryByPriceLowToHigh(categoryId: number): Observable<void> {
    this._loadingAction$.next(true);
    return this.http
      .put<void>(`${this.configService.apiUrl}/services/categories/${categoryId}/price-sort?direction=ASC`, {})
      .pipe(
        tap(() => {
          // Update local data after successful API call
          const currentServicesByCategory = {...this._servicesByCategory$.value};
          const categoryIdStr = categoryId.toString();

          if (currentServicesByCategory[categoryIdStr]) {
            // Sort the services by price (low to high)
            currentServicesByCategory[categoryIdStr] = [...currentServicesByCategory[categoryIdStr]]
              .sort((a, b) => a.price - b.price);

            // Update the BehaviorSubject
            this._servicesByCategory$.next(currentServicesByCategory);

            // Update the services array
            this.updateServicesArray();

            console.log('Category services sorted by price (Low to High)');
          }
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Sort services in a category by price (High to Low)
  sortCategoryByPriceHighToLow(categoryId: number): Observable<void> {
    this._loadingAction$.next(true);
    return this.http
      .put<void>(`${this.configService.apiUrl}/services/categories/${categoryId}/price-sort?direction=DESC`, {})
      .pipe(
        tap(() => {
          // Update local data after successful API call
          const currentServicesByCategory = {...this._servicesByCategory$.value};
          const categoryIdStr = categoryId.toString();

          if (currentServicesByCategory[categoryIdStr]) {
            // Sort the services by price (high to low)
            currentServicesByCategory[categoryIdStr] = [...currentServicesByCategory[categoryIdStr]]
              .sort((a, b) => b.price - a.price);

            // Update the BehaviorSubject
            this._servicesByCategory$.next(currentServicesByCategory);

            // Update the services array
            this.updateServicesArray();

            console.log('Category services sorted by price (High to Low)');
          }
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Get special prices for a service
  getSpecialPricesForService(serviceId: number): Observable<SpecialPriceRes[]> {
    this._loadingAction$.next(true);
    const apiUrl = `${this.configService.apiUrl}/users/services/${serviceId}/custom-discount`;

    return this.http.get<SpecialPriceRes[]>(apiUrl)
      .pipe(

        catchError(error => {
          console.error('Error loading special prices:', error);
          return of([]);
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Delete a special price (custom discount)
  deleteSpecialPrice(specialPriceId: number): Observable<void> {
    this._loadingAction$.next(true);
    const apiUrl = `${this.configService.apiUrl}/users/services/custom-discount/${specialPriceId}`;

    return this.http.delete<void>(apiUrl)
      .pipe(
        catchError(error => {
          console.error('Error deleting special price:', error);
          throw error;
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Update a special price (custom discount)
  updateSpecialPrice(specialPriceId: number, type: DiscountType, customDiscount: number, serviceId?: number): Observable<SpecialPriceRes> {
    this._loadingAction$.next(true);
    const apiUrl = `${this.configService.apiUrl}/users/services/custom-discount/${specialPriceId}`;

    const updateRequest: any = {
      type: type,
      custom_discount: customDiscount
    };

    // Add service_id to the request if provided
    if (serviceId) {
      updateRequest.service_id = serviceId;
    }

    return this.http.put<SpecialPriceRes>(apiUrl, updateRequest)
      .pipe(
        catchError(error => {
          console.error('Error updating special price:', error);
          throw error;
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Get user data by ID
  getUserById(userId: number): Observable<any> {
    this._loadingAction$.next(true);
    return this.http.get<any>(`${this.configService.apiUrl}/users/${userId}`)
      .pipe(
        catchError(error => {
          console.error('Error loading user data:', error);
          return of(null);
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Save global discount for a user
  saveGlobalDiscount(userId: number, customDiscount: number): Observable<any> {
    this._loadingAction$.next(true);
    const requestBody = {
      custom_discount: customDiscount
    };

    return this.http.patch<any>(`${this.configService.apiUrl}/users/${userId}/custom-discount`, requestBody)
      .pipe(
        catchError(error => {
          console.error('Error saving global discount:', error);
          throw error;
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Get special prices for a user
  getSpecialPricesForUser(userId: number): Observable<SpecialPriceRes[]> {
    this._loadingAction$.next(true);
    return this.http.get<SpecialPriceRes[]>(`${this.configService.apiUrl}/users/${userId}/services/custom-discount`)
      .pipe(
        catchError(error => {
          console.error('Error loading special prices for user:', error);
          return of([]);
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }

  // Change price for a service by percentage
  changePrice(id: number, percent: number): Observable<any> {
    this._loadingServiceIds.add(id);
    this._loadingAction$.next(true);
    return this.http
      .patch<any>(`${this.configService.apiUrl}/services/${id}/price?percent=${percent}`, {})
      .pipe(
        tap(() => {
          // After successful price change, get the updated service to update local state
          this.getServiceById(id).subscribe(updatedService => {
            // Update services array
            const currentServices = this._services$.value;
            const index = currentServices.findIndex(s => s.id === id);
            if (index !== -1) {
              currentServices[index] = updatedService;
              this._services$.next([...currentServices]);
            }

            // Update services by category
            this.updateServiceInCategoryMap(updatedService);
          });
        }),
        catchError(error => {
          console.error('Error changing service price:', error);
          throw error;
        }),
        finalize(() => {
          this._loadingServiceIds.delete(id);
          this._loadingAction$.next(false);
        })
      );
  }

  // Add a new platform
  addPlatform(platformReq: PlatformReq): Observable<SuperCategoryRes> {
    this._loadingAction$.next(true);
    return this.http
      .post<SuperCategoryRes>(`${this.configService.apiUrl}/platforms`, platformReq)
      .pipe(finalize(() => this._loadingAction$.next(false)));
  }

  // Update an existing platform
  updatePlatform(platformReq: PlatformReq & { id: number }): Observable<SuperCategoryRes> {
    this._loadingAction$.next(true);
    return this.http
      .put<SuperCategoryRes>(`${this.configService.apiUrl}/platforms/${platformReq.id}`, platformReq)
      .pipe(finalize(() => this._loadingAction$.next(false)));
  }

  // Add a new category
  addCategory(category: CategoryReq): Observable<SuperCategoryRes> {
    this._loadingAction$.next(true);
    return this.http
      .post<SuperCategoryRes>(`${this.configService.apiUrl}/categories`, category)
      .pipe(finalize(() => this._loadingAction$.next(false)));
  }

  // Edit an existing category
  editCategory(id: number, category: CategoryReq): Observable<SuperCategoryRes> {
    this._loadingAction$.next(true);
    return this.http
      .put<SuperCategoryRes>(`${this.configService.apiUrl}/categories/${id}`, category)
      .pipe(finalize(() => this._loadingAction$.next(false)));
  }

  // Change platform for a category
  changeCategoryPlatform(categoryId: number, newPlatformId: number): Observable<any> {
    this._loadingAction$.next(true);
    return this.http
      .put<any>(`${this.configService.apiUrl}/categories/${categoryId}/platform?newPlatform=${newPlatformId}`, {})
      .pipe(
        catchError(error => {
          console.error('Error changing category platform:', error);
          throw error;
        }),
        finalize(() => this._loadingAction$.next(false))
      );
  }
}
