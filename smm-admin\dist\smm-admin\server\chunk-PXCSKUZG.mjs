import './polyfills.server.mjs';
import{A as B1,Ab as z4,B as Y2,Ca as v,D as X2,Dc as C4,Ea as U1,F as z1,Fa as c1,G as r2,Ga as E,H as x,Hb as L2,I as g,Ia as m2,J as F,K as w,Kb as K2,L as d,La as h1,M as a4,Ma as u1,Na as z2,Nb as J2,O as B,P as q1,Q as P,R as M,Ra as o4,S as n4,Sa as j,Ta as p1,Tb as h4,Ua as r4,Va as f4,Vb as u4,X as q,aa as b,ab as h2,ba as t4,bb as u2,bc as p4,d as x1,dc as G,ea as U,ec as L4,fa as T,g as l4,h as E1,ic as d4,j as b1,ja as f2,kb as N,l as I1,lb as j1,n as Z2,nc as d2,o as $2,ob as m4,p as o2,pa as i4,pb as p2,ta as k,ua as h,w as s4,xc as M4,y as O1,ya as Q,z as R1}from"./chunk-XR72HQ3V.mjs";import{a as H,b as m1,h as u}from"./chunk-2FGBTQRU.mjs";var N1=class{},g4=(()=>{let c=class c extends N1{getTranslation(e){return E1({})}};u(c,"\u0275fac",(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})()),u(c,"\u0275prov",g({token:c,factory:c.\u0275fac}));let l=c;return l})(),W1=class{},v4=(()=>{let c=class c{handle(e){return e.key}};u(c,"\u0275fac",function(s){return new(s||c)}),u(c,"\u0275prov",g({token:c,factory:c.\u0275fac}));let l=c;return l})();function C2(l,c){if(l===c)return!0;if(l===null||c===null)return!1;if(l!==l&&c!==c)return!0;let a=typeof l,e=typeof c,s,n,t;if(a==e&&a=="object")if(Array.isArray(l)){if(!Array.isArray(c))return!1;if((s=l.length)==c.length){for(n=0;n<s;n++)if(!C2(l[n],c[n]))return!1;return!0}}else{if(Array.isArray(c))return!1;t=Object.create(null);for(n in l){if(!C2(l[n],c[n]))return!1;t[n]=!0}for(n in c)if(!(n in t)&&typeof c[n]<"u")return!1;return!0}return!1}function e1(l){return typeof l<"u"&&l!==null}function $1(l){return M2(l)&&!t3(l)&&l!==null}function M2(l){return typeof l=="object"}function t3(l){return Array.isArray(l)}function i3(l){return typeof l=="string"}function O7(l){return typeof l=="function"}function Q2(l,c){let a=Object.assign({},l);return M2(l)?(M2(l)&&M2(c)&&Object.keys(c).forEach(e=>{$1(c[e])?e in l?a[e]=Q2(l[e],c[e]):Object.assign(a,{[e]:c[e]}):Object.assign(a,{[e]:c[e]})}),a):Q2({},c)}function c3(l,c){let a=c.split(".");c="";do c+=a.shift(),e1(l)&&e1(l[c])&&($1(l[c])||t3(l[c])||!a.length)?(l=l[c],c=""):a.length?c+=".":l=void 0;while(a.length);return l}function R7(l,c,a){let e=c.split("."),s=l;for(let n=0;n<e.length;n++){let t=e[n];n===e.length-1?s[t]=a:((!s[t]||!$1(s[t]))&&(s[t]={}),s=s[t])}}var y1=class{},x4=(()=>{let c=class c extends y1{constructor(){super(...arguments);u(this,"templateMatcher",/{{\s?([^{}\s]*)\s?}}/g)}interpolate(s,n){if(i3(s))return this.interpolateString(s,n);if(O7(s))return this.interpolateFunction(s,n)}interpolateFunction(s,n){return s(n)}interpolateString(s,n){return n?s.replace(this.templateMatcher,(t,i)=>{let f=c3(n,i);return e1(f)?f:t}):s}};u(c,"\u0275fac",(()=>{let s;return function(t){return(s||(s=b(c)))(t||c)}})()),u(c,"\u0275prov",g({token:c,factory:c.\u0275fac}));let l=c;return l})(),S1=class{},b4=(()=>{let c=class c extends S1{compile(e,s){return e}compileTranslations(e,s){return e}};u(c,"\u0275fac",(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})()),u(c,"\u0275prov",g({token:c,factory:c.\u0275fac}));let l=c;return l})(),Z1=class{constructor(){u(this,"defaultLang");u(this,"currentLang",this.defaultLang);u(this,"translations",{});u(this,"langs",[]);u(this,"onTranslationChange",new T);u(this,"onLangChange",new T);u(this,"onDefaultLangChange",new T)}},e3=new w("ISOLATE_TRANSLATE_SERVICE"),l3=new w("USE_DEFAULT_LANG"),s3=new w("DEFAULT_LANGUAGE"),a3=new w("USE_EXTEND"),G1=l=>b1(l)?l:E1(l),n3=(()=>{let c=class c{constructor(e,s,n,t,i,f=!0,r=!1,z=!1,L){u(this,"store");u(this,"currentLoader");u(this,"compiler");u(this,"parser");u(this,"missingTranslationHandler");u(this,"useDefaultLang");u(this,"extend");u(this,"loadingTranslations");u(this,"pending",!1);u(this,"_translationRequests",{});u(this,"lastUseLanguage",null);this.store=e,this.currentLoader=s,this.compiler=n,this.parser=t,this.missingTranslationHandler=i,this.useDefaultLang=f,this.extend=z,r&&(this.store=new Z1),L&&this.setDefaultLang(L)}get onTranslationChange(){return this.store.onTranslationChange}get onLangChange(){return this.store.onLangChange}get onDefaultLangChange(){return this.store.onDefaultLangChange}get defaultLang(){return this.store.defaultLang}set defaultLang(e){this.store.defaultLang=e}get currentLang(){return this.store.currentLang}set currentLang(e){this.store.currentLang=e}get langs(){return this.store.langs}set langs(e){this.store.langs=e}get translations(){return this.store.translations}set translations(e){this.store.translations=e}setDefaultLang(e){if(e===this.defaultLang)return;let s=this.retrieveTranslations(e);typeof s<"u"?(this.defaultLang==null&&(this.defaultLang=e),s.pipe(O1(1)).subscribe(()=>{this.changeDefaultLang(e)})):this.changeDefaultLang(e)}getDefaultLang(){return this.defaultLang}use(e){if(this.lastUseLanguage=e,e===this.currentLang)return E1(this.translations[e]);this.currentLang||(this.currentLang=e);let s=this.retrieveTranslations(e);return b1(s)?(s.pipe(O1(1)).subscribe(()=>{this.changeLang(e)}),s):(this.changeLang(e),E1(this.translations[e]))}changeLang(e){e===this.lastUseLanguage&&(this.currentLang=e,this.onLangChange.emit({lang:e,translations:this.translations[e]}),this.defaultLang==null&&this.changeDefaultLang(e))}retrieveTranslations(e){if(typeof this.translations[e]>"u"||this.extend)return this._translationRequests[e]=this._translationRequests[e]||this.loadAndCompileTranslations(e),this._translationRequests[e]}getTranslation(e){return this.loadAndCompileTranslations(e)}loadAndCompileTranslations(e){this.pending=!0;let s=this.currentLoader.getTranslation(e).pipe(Y2(1),O1(1));return this.loadingTranslations=s.pipe(I1(n=>this.compiler.compileTranslations(n,e)),Y2(1),O1(1)),this.loadingTranslations.subscribe({next:n=>{this.translations[e]=this.extend&&this.translations[e]?H(H({},n),this.translations[e]):n,this.updateLangs(),this.pending=!1},error:n=>{this.pending=!1}}),s}setTranslation(e,s,n=!1){let t=this.compiler.compileTranslations(s,e);(n||this.extend)&&this.translations[e]?this.translations[e]=Q2(this.translations[e],t):this.translations[e]=t,this.updateLangs(),this.onTranslationChange.emit({lang:e,translations:this.translations[e]})}getLangs(){return this.langs}addLangs(e){let s=e.filter(n=>!this.langs.includes(n));s.length>0&&(this.langs=[...this.langs,...s])}updateLangs(){this.addLangs(Object.keys(this.translations))}getParsedResultForKey(e,s,n){let t;if(e&&(t=this.runInterpolation(c3(e,s),n)),t===void 0&&this.defaultLang!=null&&this.defaultLang!==this.currentLang&&this.useDefaultLang&&(t=this.runInterpolation(c3(this.translations[this.defaultLang],s),n)),t===void 0){let i={key:s,translateService:this};typeof n<"u"&&(i.interpolateParams=n),t=this.missingTranslationHandler.handle(i)}return t!==void 0?t:s}runInterpolation(e,s){if(t3(e))return e.map(n=>this.runInterpolation(n,s));if($1(e)){let n={};for(let t in e){let i=this.runInterpolation(e[t],s);i!==void 0&&(n[t]=i)}return n}else return this.parser.interpolate(e,s)}getParsedResult(e,s,n){if(s instanceof Array){let t={},i=!1;for(let r of s)t[r]=this.getParsedResultForKey(e,r,n),i=i||b1(t[r]);if(!i)return t;let f=s.map(r=>G1(t[r]));return o2(f).pipe(I1(r=>{let z={};return r.forEach((L,p)=>{z[s[p]]=L}),z}))}return this.getParsedResultForKey(e,s,n)}get(e,s){if(!e1(e)||!e.length)throw new Error('Parameter "key" is required and cannot be empty');return this.pending?this.loadingTranslations.pipe(s4(n=>G1(this.getParsedResult(n,e,s)))):G1(this.getParsedResult(this.translations[this.currentLang],e,s))}getStreamOnTranslationChange(e,s){if(!e1(e)||!e.length)throw new Error('Parameter "key" is required and cannot be empty');return Z2($2(()=>this.get(e,s)),this.onTranslationChange.pipe(X2(n=>{let t=this.getParsedResult(n.translations,e,s);return G1(t)})))}stream(e,s){if(!e1(e)||!e.length)throw new Error('Parameter "key" required');return Z2($2(()=>this.get(e,s)),this.onLangChange.pipe(X2(n=>{let t=this.getParsedResult(n.translations,e,s);return G1(t)})))}instant(e,s){if(!e1(e)||e.length===0)throw new Error('Parameter "key" is required and cannot be empty');let n=this.getParsedResult(this.translations[this.currentLang],e,s);return b1(n)?Array.isArray(e)?e.reduce((t,i)=>(t[i]=i,t),{}):e:n}set(e,s,n=this.currentLang){R7(this.translations[n],e,i3(s)?this.compiler.compile(s,n):this.compiler.compileTranslations(s,n)),this.updateLangs(),this.onTranslationChange.emit({lang:n,translations:this.translations[n]})}changeDefaultLang(e){this.defaultLang=e,this.onDefaultLangChange.emit({lang:e,translations:this.translations[e]})}reloadLang(e){return this.resetLang(e),this.loadAndCompileTranslations(e)}resetLang(e){delete this._translationRequests[e],delete this.translations[e]}getBrowserLang(){if(typeof window>"u"||!window.navigator)return;let e=this.getBrowserCultureLang();return e?e.split(/[-_]/)[0]:void 0}getBrowserCultureLang(){if(!(typeof window>"u"||typeof window.navigator>"u"))return window.navigator.languages?window.navigator.languages[0]:window.navigator.language||window.navigator.browserLanguage||window.navigator.userLanguage}};u(c,"\u0275fac",function(s){return new(s||c)(d(Z1),d(N1),d(S1),d(y1),d(W1),d(l3),d(e3),d(a3),d(s3))}),u(c,"\u0275prov",g({token:c,factory:c.\u0275fac,providedIn:"root"}));let l=c;return l})();var es=(()=>{let c=class c{constructor(e,s){u(this,"translate");u(this,"_ref");u(this,"value","");u(this,"lastKey",null);u(this,"lastParams",[]);u(this,"onTranslationChange");u(this,"onLangChange");u(this,"onDefaultLangChange");this.translate=e,this._ref=s}updateValue(e,s,n){let t=i=>{this.value=i!==void 0?i:e,this.lastKey=e,this._ref.markForCheck()};if(n){let i=this.translate.getParsedResult(n,e,s);b1(i)?i.subscribe(t):t(i)}this.translate.get(e,s).subscribe(t)}transform(e,...s){if(!e||!e.length)return e;if(C2(e,this.lastKey)&&C2(s,this.lastParams))return this.value;let n;if(e1(s[0])&&s.length)if(i3(s[0])&&s[0].length){let t=s[0].replace(/(')?([a-zA-Z0-9_]+)(')?(\s)?:/g,'"$2":').replace(/:(\s)?(')(.*?)(')/g,':"$3"');try{n=JSON.parse(t)}catch(i){throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${s[0]}`)}}else $1(s[0])&&(n=s[0]);return this.lastKey=e,this.lastParams=s,this.updateValue(e,n),this._dispose(),this.onTranslationChange||(this.onTranslationChange=this.translate.onTranslationChange.subscribe(t=>{this.lastKey&&t.lang===this.translate.currentLang&&(this.lastKey=null,this.updateValue(e,n,t.translations))})),this.onLangChange||(this.onLangChange=this.translate.onLangChange.subscribe(t=>{this.lastKey&&(this.lastKey=null,this.updateValue(e,n,t.translations))})),this.onDefaultLangChange||(this.onDefaultLangChange=this.translate.onDefaultLangChange.subscribe(()=>{this.lastKey&&(this.lastKey=null,this.updateValue(e,n))})),this.value}_dispose(){typeof this.onTranslationChange<"u"&&(this.onTranslationChange.unsubscribe(),this.onTranslationChange=void 0),typeof this.onLangChange<"u"&&(this.onLangChange.unsubscribe(),this.onLangChange=void 0),typeof this.onDefaultLangChange<"u"&&(this.onDefaultLangChange.unsubscribe(),this.onDefaultLangChange=void 0)}ngOnDestroy(){this._dispose()}};u(c,"\u0275fac",function(s){return new(s||c)(h(n3,16),h(L2,16))}),u(c,"\u0275pipe",n4({name:"translate",type:c,pure:!1,standalone:!0})),u(c,"\u0275prov",g({token:c,factory:c.\u0275fac}));let l=c;return l})();var ls=(()=>{let c=class c{static forRoot(e={}){return{ngModule:c,providers:[e.loader||{provide:N1,useClass:g4},e.compiler||{provide:S1,useClass:b4},e.parser||{provide:y1,useClass:x4},e.missingTranslationHandler||{provide:W1,useClass:v4},Z1,{provide:e3,useValue:e.isolate},{provide:l3,useValue:e.useDefaultLang},{provide:a3,useValue:e.extend},{provide:s3,useValue:e.defaultLanguage},n3]}}static forChild(e={}){return{ngModule:c,providers:[e.loader||{provide:N1,useClass:g4},e.compiler||{provide:S1,useClass:b4},e.parser||{provide:y1,useClass:x4},e.missingTranslationHandler||{provide:W1,useClass:v4},{provide:e3,useValue:e.isolate},{provide:l3,useValue:e.useDefaultLang},{provide:a3,useValue:e.extend},{provide:s3,useValue:e.defaultLanguage},n3]}}};u(c,"\u0275fac",function(s){return new(s||c)}),u(c,"\u0275mod",P({type:c})),u(c,"\u0275inj",F({}));let l=c;return l})();var o3=(l,c,a)=>({sm:l,md:c,lg:a}),B7=(l,c)=>({"bg-white bg-opacity-70":l,"bg-transparent":c});function q7(l,c){if(l&1&&(h1(0,"div",7),h2(1),u1()),l&2){let a=p1(2);k(),u2(a.message)}}function U7(l,c){if(l&1&&(h1(0,"div",3)(1,"div",4),z2(2,"div",5),U1(3,q7,2,1,"div",6),u1()()),l&2){let a=p1();k(2),E("ngClass",p2(2,o3,a.size==="sm",a.size==="md",a.size==="lg")),k(),E("ngIf",a.message)}}function j7(l,c){if(l&1&&(h1(0,"div",10),h2(1),u1()),l&2){let a=p1(2);k(),u2(a.message)}}function G7(l,c){if(l&1&&(h1(0,"div",8)(1,"div",4),z2(2,"div",5),U1(3,j7,2,1,"div",9),u1()()),l&2){let a=p1();E("ngClass",m4(3,B7,!a.transparent,a.transparent)),k(2),E("ngClass",p2(6,o3,a.size==="sm",a.size==="md",a.size==="lg")),k(),E("ngIf",a.message)}}function W7(l,c){if(l&1&&(h1(0,"div",10),h2(1),u1()),l&2){let a=p1(2);k(),u2(a.message)}}function Z7(l,c){if(l&1&&(h1(0,"div",11)(1,"div",4),z2(2,"div",5),U1(3,W7,2,1,"div",9),u1()()),l&2){let a=p1();k(2),E("ngClass",p2(2,o3,a.size==="sm",a.size==="md",a.size==="lg")),k(),E("ngIf",a.message)}}var is=(()=>{let c=class c{constructor(){this.size="md",this.overlay=!1,this.fullScreen=!1,this.message="",this.transparent=!1}};c.\u0275fac=function(s){return new(s||c)},c.\u0275cmp=q1({type:c,selectors:[["app-loading"]],inputs:{size:"size",overlay:"overlay",fullScreen:"fullScreen",message:"message",transparent:"transparent"},standalone:!0,features:[j1],decls:3,vars:3,consts:[["class","fixed inset-0 bg-white bg-opacity-90 flex justify-center items-center z-50",4,"ngIf"],["class","absolute inset-0 flex justify-center items-center z-10",3,"ngClass",4,"ngIf"],["class","flex justify-center items-center",4,"ngIf"],[1,"fixed","inset-0","bg-white","bg-opacity-90","flex","justify-center","items-center","z-50"],[1,"flex","flex-col","items-center"],[1,"loading-spinner",3,"ngClass"],["class","mt-4 text-gray-800 font-medium",4,"ngIf"],[1,"mt-4","text-gray-800","font-medium"],[1,"absolute","inset-0","flex","justify-center","items-center","z-10",3,"ngClass"],["class","mt-2 text-gray-800 font-medium",4,"ngIf"],[1,"mt-2","text-gray-800","font-medium"],[1,"flex","justify-center","items-center"]],template:function(s,n){s&1&&U1(0,U7,4,6,"div",0)(1,G7,4,10,"div",1)(2,Z7,4,6,"div",2),s&2&&(E("ngIf",n.fullScreen),k(),E("ngIf",n.overlay&&!n.fullScreen),k(),E("ngIf",!n.overlay&&!n.fullScreen))},dependencies:[p4,h4,u4],styles:[".loading-spinner[_ngcontent-%COMP%]{border-radius:50%;border:3px solid rgba(var(--primary-rgb, 48, 176, 199),.2);border-top-color:var(--primary, #30B0C7);animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite}.loading-spinner.sm[_ngcontent-%COMP%]{width:1.5rem;height:1.5rem}.loading-spinner.md[_ngcontent-%COMP%]{width:2.5rem;height:2.5rem}.loading-spinner.lg[_ngcontent-%COMP%]{width:4rem;height:4rem}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}"]});let l=c;return l})();function $7(l,c,a){return(c=X7(c))in l?Object.defineProperty(l,c,{value:a,enumerable:!0,configurable:!0,writable:!0}):l[c]=a,l}function N4(l,c){var a=Object.keys(l);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(l);c&&(e=e.filter(function(s){return Object.getOwnPropertyDescriptor(l,s).enumerable})),a.push.apply(a,e)}return a}function o(l){for(var c=1;c<arguments.length;c++){var a=arguments[c]!=null?arguments[c]:{};c%2?N4(Object(a),!0).forEach(function(e){$7(l,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(a)):N4(Object(a)).forEach(function(e){Object.defineProperty(l,e,Object.getOwnPropertyDescriptor(a,e))})}return l}function Y7(l,c){if(typeof l!="object"||!l)return l;var a=l[Symbol.toPrimitive];if(a!==void 0){var e=a.call(l,c||"default");if(typeof e!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(c==="string"?String:Number)(l)}function X7(l){var c=Y7(l,"string");return typeof c=="symbol"?c:c+""}var y4=()=>{},k3={},K4={},J4=null,Q4={mark:y4,measure:y4};try{typeof window<"u"&&(k3=window),typeof document<"u"&&(K4=document),typeof MutationObserver<"u"&&(J4=MutationObserver),typeof performance<"u"&&(Q4=performance)}catch{}var{userAgent:S4=""}=k3.navigator||{},s1=k3,C=K4,w4=J4,g2=Q4,rs=!!s1.document,Y=!!C.documentElement&&!!C.head&&typeof C.addEventListener=="function"&&typeof C.createElement=="function",c0=~S4.indexOf("MSIE")||~S4.indexOf("Trident/"),K7=/fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\-\ ]/,J7=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i,e0={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"}},Q7={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},l0=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone"],y="classic",S2="duotone",c9="sharp",e9="sharp-duotone",s0=[y,S2,c9,e9],l9={classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"}},s9={"Font Awesome 6 Free":{900:"fas",400:"far"},"Font Awesome 6 Pro":{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},"Font Awesome 6 Brands":{400:"fab",normal:"fab"},"Font Awesome 6 Duotone":{900:"fad",400:"fadr",normal:"fadr",300:"fadl",100:"fadt"},"Font Awesome 6 Sharp":{900:"fass",400:"fasr",normal:"fasr",300:"fasl",100:"fast"},"Font Awesome 6 Sharp Duotone":{900:"fasds",400:"fasdr",normal:"fasdr",300:"fasdl",100:"fasdt"}},a9=new Map([["classic",{defaultShortPrefixId:"fas",defaultStyleId:"solid",styleIds:["solid","regular","light","thin","brands"],futureStyleIds:[],defaultFontWeight:900}],["sharp",{defaultShortPrefixId:"fass",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["duotone",{defaultShortPrefixId:"fad",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp-duotone",{defaultShortPrefixId:"fasds",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}]]),n9={classic:{solid:"fas",regular:"far",light:"fal",thin:"fat",brands:"fab"},duotone:{solid:"fad",regular:"fadr",light:"fadl",thin:"fadt"},sharp:{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"},"sharp-duotone":{solid:"fasds",regular:"fasdr",light:"fasdl",thin:"fasdt"}},t9=["fak","fa-kit","fakd","fa-kit-duotone"],A4={kit:{fak:"kit","fa-kit":"kit"},"kit-duotone":{fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"}},i9=["kit"],o9={kit:{"fa-kit":"fak"},"kit-duotone":{"fa-kit-duotone":"fakd"}},r9=["fak","fakd"],f9={kit:{fak:"fa-kit"},"kit-duotone":{fakd:"fa-kit-duotone"}},V4={kit:{kit:"fak"},"kit-duotone":{"kit-duotone":"fakd"}},v2={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},m9=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone"],z9=["fak","fa-kit","fakd","fa-kit-duotone"],h9={"Font Awesome Kit":{400:"fak",normal:"fak"},"Font Awesome Kit Duotone":{400:"fakd",normal:"fakd"}},u9={classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"}},p9={classic:["fas","far","fal","fat","fad"],duotone:["fadr","fadl","fadt"],sharp:["fass","fasr","fasl","fast"],"sharp-duotone":["fasds","fasdr","fasdl","fasdt"]},u3={classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"}},L9=["fa-solid","fa-regular","fa-light","fa-thin","fa-duotone","fa-brands"],p3=["fa","fas","far","fal","fat","fad","fadr","fadl","fadt","fab","fass","fasr","fasl","fast","fasds","fasdr","fasdl","fasdt",...m9,...L9],d9=["solid","regular","light","thin","duotone","brands"],a0=[1,2,3,4,5,6,7,8,9,10],M9=a0.concat([11,12,13,14,15,16,17,18,19,20]),C9=[...Object.keys(p9),...d9,"2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",v2.GROUP,v2.SWAP_OPACITY,v2.PRIMARY,v2.SECONDARY].concat(a0.map(l=>"".concat(l,"x"))).concat(M9.map(l=>"w-".concat(l))),g9={"Font Awesome 5 Free":{900:"fas",400:"far"},"Font Awesome 5 Pro":{900:"fas",400:"far",normal:"far",300:"fal"},"Font Awesome 5 Brands":{400:"fab",normal:"fab"},"Font Awesome 5 Duotone":{900:"fad"}},Z="___FONT_AWESOME___",L3=16,n0="fa",t0="svg-inline--fa",d1="data-fa-i2svg",d3="data-fa-pseudo-element",v9="data-fa-pseudo-element-pending",D3="data-prefix",_3="data-icon",H4="fontawesome-i2svg",x9="async",b9=["HTML","HEAD","STYLE","SCRIPT"],i0=(()=>{try{return process.env.NODE_ENV==="production"}catch{return!1}})();function c2(l){return new Proxy(l,{get(c,a){return a in c?c[a]:c[y]}})}var o0=o({},e0);o0[y]=o(o(o(o({},{"fa-duotone":"duotone"}),e0[y]),A4.kit),A4["kit-duotone"]);var N9=c2(o0),M3=o({},n9);M3[y]=o(o(o(o({},{duotone:"fad"}),M3[y]),V4.kit),V4["kit-duotone"]);var k4=c2(M3),C3=o({},u3);C3[y]=o(o({},C3[y]),f9.kit);var F3=c2(C3),g3=o({},u9);g3[y]=o(o({},g3[y]),o9.kit);var fs=c2(g3),y9=K7,r0="fa-layers-text",S9=J7,w9=o({},l9),ms=c2(w9),A9=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],r3=Q7,V9=[...i9,...C9],X1=s1.FontAwesomeConfig||{};function H9(l){var c=C.querySelector("script["+l+"]");if(c)return c.getAttribute(l)}function k9(l){return l===""?!0:l==="false"?!1:l==="true"?!0:l}C&&typeof C.querySelector=="function"&&[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(c=>{let[a,e]=c,s=k9(H9(a));s!=null&&(X1[e]=s)});var f0={styleDefault:"solid",familyDefault:y,cssPrefix:n0,replacementClass:t0,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};X1.familyPrefix&&(X1.cssPrefix=X1.familyPrefix);var V1=o(o({},f0),X1);V1.autoReplaceSvg||(V1.observeMutations=!1);var m={};Object.keys(f0).forEach(l=>{Object.defineProperty(m,l,{enumerable:!0,set:function(c){V1[l]=c,K1.forEach(a=>a(m))},get:function(){return V1[l]}})});Object.defineProperty(m,"familyPrefix",{enumerable:!0,set:function(l){V1.cssPrefix=l,K1.forEach(c=>c(m))},get:function(){return V1.cssPrefix}});s1.FontAwesomeConfig=m;var K1=[];function D9(l){return K1.push(l),()=>{K1.splice(K1.indexOf(l),1)}}var l1=L3,I={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function _9(l){if(!l||!Y)return;let c=C.createElement("style");c.setAttribute("type","text/css"),c.innerHTML=l;let a=C.head.childNodes,e=null;for(let s=a.length-1;s>-1;s--){let n=a[s],t=(n.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(t)>-1&&(e=n)}return C.head.insertBefore(c,e),l}var F9="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";function J1(){let l=12,c="";for(;l-- >0;)c+=F9[Math.random()*62|0];return c}function H1(l){let c=[];for(let a=(l||[]).length>>>0;a--;)c[a]=l[a];return c}function P3(l){return l.classList?H1(l.classList):(l.getAttribute("class")||"").split(" ").filter(c=>c)}function m0(l){return"".concat(l).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function P9(l){return Object.keys(l||{}).reduce((c,a)=>c+"".concat(a,'="').concat(m0(l[a]),'" '),"").trim()}function w2(l){return Object.keys(l||{}).reduce((c,a)=>c+"".concat(a,": ").concat(l[a].trim(),";"),"")}function T3(l){return l.size!==I.size||l.x!==I.x||l.y!==I.y||l.rotate!==I.rotate||l.flipX||l.flipY}function T9(l){let{transform:c,containerWidth:a,iconWidth:e}=l,s={transform:"translate(".concat(a/2," 256)")},n="translate(".concat(c.x*32,", ").concat(c.y*32,") "),t="scale(".concat(c.size/16*(c.flipX?-1:1),", ").concat(c.size/16*(c.flipY?-1:1),") "),i="rotate(".concat(c.rotate," 0 0)"),f={transform:"".concat(n," ").concat(t," ").concat(i)},r={transform:"translate(".concat(e/2*-1," -256)")};return{outer:s,inner:f,path:r}}function E9(l){let{transform:c,width:a=L3,height:e=L3,startCentered:s=!1}=l,n="";return s&&c0?n+="translate(".concat(c.x/l1-a/2,"em, ").concat(c.y/l1-e/2,"em) "):s?n+="translate(calc(-50% + ".concat(c.x/l1,"em), calc(-50% + ").concat(c.y/l1,"em)) "):n+="translate(".concat(c.x/l1,"em, ").concat(c.y/l1,"em) "),n+="scale(".concat(c.size/l1*(c.flipX?-1:1),", ").concat(c.size/l1*(c.flipY?-1:1),") "),n+="rotate(".concat(c.rotate,"deg) "),n}var I9=`:root, :host {
  --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Free";
  --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free";
  --fa-font-light: normal 300 1em/1 "Font Awesome 6 Pro";
  --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Pro";
  --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";
  --fa-font-duotone-regular: normal 400 1em/1 "Font Awesome 6 Duotone";
  --fa-font-duotone-light: normal 300 1em/1 "Font Awesome 6 Duotone";
  --fa-font-duotone-thin: normal 100 1em/1 "Font Awesome 6 Duotone";
  --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";
  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 6 Sharp Duotone";
  --fa-font-sharp-duotone-regular: normal 400 1em/1 "Font Awesome 6 Sharp Duotone";
  --fa-font-sharp-duotone-light: normal 300 1em/1 "Font Awesome 6 Sharp Duotone";
  --fa-font-sharp-duotone-thin: normal 100 1em/1 "Font Awesome 6 Sharp Duotone";
}

svg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {
  overflow: visible;
  box-sizing: content-box;
}

.svg-inline--fa {
  display: var(--fa-display, inline-block);
  height: 1em;
  overflow: visible;
  vertical-align: -0.125em;
}
.svg-inline--fa.fa-2xs {
  vertical-align: 0.1em;
}
.svg-inline--fa.fa-xs {
  vertical-align: 0em;
}
.svg-inline--fa.fa-sm {
  vertical-align: -0.0714285705em;
}
.svg-inline--fa.fa-lg {
  vertical-align: -0.2em;
}
.svg-inline--fa.fa-xl {
  vertical-align: -0.25em;
}
.svg-inline--fa.fa-2xl {
  vertical-align: -0.3125em;
}
.svg-inline--fa.fa-pull-left {
  margin-right: var(--fa-pull-margin, 0.3em);
  width: auto;
}
.svg-inline--fa.fa-pull-right {
  margin-left: var(--fa-pull-margin, 0.3em);
  width: auto;
}
.svg-inline--fa.fa-li {
  width: var(--fa-li-width, 2em);
  top: 0.25em;
}
.svg-inline--fa.fa-fw {
  width: var(--fa-fw-width, 1.25em);
}

.fa-layers svg.svg-inline--fa {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
}

.fa-layers-counter, .fa-layers-text {
  display: inline-block;
  position: absolute;
  text-align: center;
}

.fa-layers {
  display: inline-block;
  height: 1em;
  position: relative;
  text-align: center;
  vertical-align: -0.125em;
  width: 1em;
}
.fa-layers svg.svg-inline--fa {
  transform-origin: center center;
}

.fa-layers-text {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transform-origin: center center;
}

.fa-layers-counter {
  background-color: var(--fa-counter-background-color, #ff253a);
  border-radius: var(--fa-counter-border-radius, 1em);
  box-sizing: border-box;
  color: var(--fa-inverse, #fff);
  line-height: var(--fa-counter-line-height, 1);
  max-width: var(--fa-counter-max-width, 5em);
  min-width: var(--fa-counter-min-width, 1.5em);
  overflow: hidden;
  padding: var(--fa-counter-padding, 0.25em 0.5em);
  right: var(--fa-right, 0);
  text-overflow: ellipsis;
  top: var(--fa-top, 0);
  transform: scale(var(--fa-counter-scale, 0.25));
  transform-origin: top right;
}

.fa-layers-bottom-right {
  bottom: var(--fa-bottom, 0);
  right: var(--fa-right, 0);
  top: auto;
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: bottom right;
}

.fa-layers-bottom-left {
  bottom: var(--fa-bottom, 0);
  left: var(--fa-left, 0);
  right: auto;
  top: auto;
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: bottom left;
}

.fa-layers-top-right {
  top: var(--fa-top, 0);
  right: var(--fa-right, 0);
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: top right;
}

.fa-layers-top-left {
  left: var(--fa-left, 0);
  right: auto;
  top: var(--fa-top, 0);
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: top left;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-2xs {
  font-size: 0.625em;
  line-height: 0.1em;
  vertical-align: 0.225em;
}

.fa-xs {
  font-size: 0.75em;
  line-height: 0.0833333337em;
  vertical-align: 0.125em;
}

.fa-sm {
  font-size: 0.875em;
  line-height: 0.0714285718em;
  vertical-align: 0.0535714295em;
}

.fa-lg {
  font-size: 1.25em;
  line-height: 0.05em;
  vertical-align: -0.075em;
}

.fa-xl {
  font-size: 1.5em;
  line-height: 0.0416666682em;
  vertical-align: -0.125em;
}

.fa-2xl {
  font-size: 2em;
  line-height: 0.03125em;
  vertical-align: -0.1875em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  list-style-type: none;
  margin-left: var(--fa-li-margin, 2.5em);
  padding-left: 0;
}
.fa-ul > li {
  position: relative;
}

.fa-li {
  left: calc(-1 * var(--fa-li-width, 2em));
  position: absolute;
  text-align: center;
  width: var(--fa-li-width, 2em);
  line-height: inherit;
}

.fa-border {
  border-color: var(--fa-border-color, #eee);
  border-radius: var(--fa-border-radius, 0.1em);
  border-style: var(--fa-border-style, solid);
  border-width: var(--fa-border-width, 0.08em);
  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);
}

.fa-pull-left {
  float: left;
  margin-right: var(--fa-pull-margin, 0.3em);
}

.fa-pull-right {
  float: right;
  margin-left: var(--fa-pull-margin, 0.3em);
}

.fa-beat {
  animation-name: fa-beat;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-bounce {
  animation-name: fa-bounce;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
}

.fa-fade {
  animation-name: fa-fade;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.fa-beat-fade {
  animation-name: fa-beat-fade;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.fa-flip {
  animation-name: fa-flip;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-shake {
  animation-name: fa-shake;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-spin {
  animation-name: fa-spin;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 2s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-spin-reverse {
  --fa-animation-direction: reverse;
}

.fa-pulse,
.fa-spin-pulse {
  animation-name: fa-spin;
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, steps(8));
}

@media (prefers-reduced-motion: reduce) {
  .fa-beat,
.fa-bounce,
.fa-fade,
.fa-beat-fade,
.fa-flip,
.fa-pulse,
.fa-shake,
.fa-spin,
.fa-spin-pulse {
    animation-delay: -1ms;
    animation-duration: 1ms;
    animation-iteration-count: 1;
    transition-delay: 0s;
    transition-duration: 0s;
  }
}
@keyframes fa-beat {
  0%, 90% {
    transform: scale(1);
  }
  45% {
    transform: scale(var(--fa-beat-scale, 1.25));
  }
}
@keyframes fa-bounce {
  0% {
    transform: scale(1, 1) translateY(0);
  }
  10% {
    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
  }
  30% {
    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
  }
  50% {
    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
  }
  57% {
    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
  }
  64% {
    transform: scale(1, 1) translateY(0);
  }
  100% {
    transform: scale(1, 1) translateY(0);
  }
}
@keyframes fa-fade {
  50% {
    opacity: var(--fa-fade-opacity, 0.4);
  }
}
@keyframes fa-beat-fade {
  0%, 100% {
    opacity: var(--fa-beat-fade-opacity, 0.4);
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(var(--fa-beat-fade-scale, 1.125));
  }
}
@keyframes fa-flip {
  50% {
    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
  }
}
@keyframes fa-shake {
  0% {
    transform: rotate(-15deg);
  }
  4% {
    transform: rotate(15deg);
  }
  8%, 24% {
    transform: rotate(-18deg);
  }
  12%, 28% {
    transform: rotate(18deg);
  }
  16% {
    transform: rotate(-22deg);
  }
  20% {
    transform: rotate(22deg);
  }
  32% {
    transform: rotate(-12deg);
  }
  36% {
    transform: rotate(12deg);
  }
  40%, 100% {
    transform: rotate(0deg);
  }
}
@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.fa-rotate-90 {
  transform: rotate(90deg);
}

.fa-rotate-180 {
  transform: rotate(180deg);
}

.fa-rotate-270 {
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  transform: scale(1, -1);
}

.fa-flip-both,
.fa-flip-horizontal.fa-flip-vertical {
  transform: scale(-1, -1);
}

.fa-rotate-by {
  transform: rotate(var(--fa-rotate-angle, 0));
}

.fa-stack {
  display: inline-block;
  vertical-align: middle;
  height: 2em;
  position: relative;
  width: 2.5em;
}

.fa-stack-1x,
.fa-stack-2x {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
  z-index: var(--fa-stack-z-index, auto);
}

.svg-inline--fa.fa-stack-1x {
  height: 1em;
  width: 1.25em;
}
.svg-inline--fa.fa-stack-2x {
  height: 2em;
  width: 2.5em;
}

.fa-inverse {
  color: var(--fa-inverse, #fff);
}

.sr-only,
.fa-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.sr-only-focusable:not(:focus),
.fa-sr-only-focusable:not(:focus) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.svg-inline--fa .fa-primary {
  fill: var(--fa-primary-color, currentColor);
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa .fa-secondary {
  fill: var(--fa-secondary-color, currentColor);
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-primary {
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-secondary {
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa mask .fa-primary,
.svg-inline--fa mask .fa-secondary {
  fill: black;
}`;function z0(){let l=n0,c=t0,a=m.cssPrefix,e=m.replacementClass,s=I9;if(a!==l||e!==c){let n=new RegExp("\\.".concat(l,"\\-"),"g"),t=new RegExp("\\--".concat(l,"\\-"),"g"),i=new RegExp("\\.".concat(c),"g");s=s.replace(n,".".concat(a,"-")).replace(t,"--".concat(a,"-")).replace(i,".".concat(e))}return s}var D4=!1;function f3(){m.autoAddCss&&!D4&&(_9(z0()),D4=!0)}var O9={mixout(){return{dom:{css:z0,insertCss:f3}}},hooks(){return{beforeDOMElementCreation(){f3()},beforeI2svg(){f3()}}}},$=s1||{};$[Z]||($[Z]={});$[Z].styles||($[Z].styles={});$[Z].hooks||($[Z].hooks={});$[Z].shims||($[Z].shims=[]);var O=$[Z],h0=[],u0=function(){C.removeEventListener("DOMContentLoaded",u0),N2=1,h0.map(l=>l())},N2=!1;Y&&(N2=(C.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(C.readyState),N2||C.addEventListener("DOMContentLoaded",u0));function R9(l){Y&&(N2?setTimeout(l,0):h0.push(l))}function e2(l){let{tag:c,attributes:a={},children:e=[]}=l;return typeof l=="string"?m0(l):"<".concat(c," ").concat(P9(a),">").concat(e.map(e2).join(""),"</").concat(c,">")}function _4(l,c,a){if(l&&l[c]&&l[c][a])return{prefix:c,iconName:a,icon:l[c][a]}}var B9=function(c,a){return function(e,s,n,t){return c.call(a,e,s,n,t)}},m3=function(c,a,e,s){var n=Object.keys(c),t=n.length,i=s!==void 0?B9(a,s):a,f,r,z;for(e===void 0?(f=1,z=c[n[0]]):(f=0,z=e);f<t;f++)r=n[f],z=i(z,c[r],r,c);return z};function q9(l){let c=[],a=0,e=l.length;for(;a<e;){let s=l.charCodeAt(a++);if(s>=55296&&s<=56319&&a<e){let n=l.charCodeAt(a++);(n&64512)==56320?c.push(((s&1023)<<10)+(n&1023)+65536):(c.push(s),a--)}else c.push(s)}return c}function v3(l){let c=q9(l);return c.length===1?c[0].toString(16):null}function U9(l,c){let a=l.length,e=l.charCodeAt(c),s;return e>=55296&&e<=56319&&a>c+1&&(s=l.charCodeAt(c+1),s>=56320&&s<=57343)?(e-55296)*1024+s-56320+65536:e}function F4(l){return Object.keys(l).reduce((c,a)=>{let e=l[a];return!!e.icon?c[e.iconName]=e.icon:c[a]=e,c},{})}function x3(l,c){let a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},{skipHooks:e=!1}=a,s=F4(c);typeof O.hooks.addPack=="function"&&!e?O.hooks.addPack(l,F4(c)):O.styles[l]=o(o({},O.styles[l]||{}),s),l==="fas"&&x3("fa",c)}var{styles:Q1,shims:j9}=O,p0=Object.keys(F3),G9=p0.reduce((l,c)=>(l[c]=Object.keys(F3[c]),l),{}),E3=null,L0={},d0={},M0={},C0={},g0={};function W9(l){return~V9.indexOf(l)}function Z9(l,c){let a=c.split("-"),e=a[0],s=a.slice(1).join("-");return e===l&&s!==""&&!W9(s)?s:null}var v0=()=>{let l=e=>m3(Q1,(s,n,t)=>(s[t]=m3(n,e,{}),s),{});L0=l((e,s,n)=>(s[3]&&(e[s[3]]=n),s[2]&&s[2].filter(i=>typeof i=="number").forEach(i=>{e[i.toString(16)]=n}),e)),d0=l((e,s,n)=>(e[n]=n,s[2]&&s[2].filter(i=>typeof i=="string").forEach(i=>{e[i]=n}),e)),g0=l((e,s,n)=>{let t=s[2];return e[n]=n,t.forEach(i=>{e[i]=n}),e});let c="far"in Q1||m.autoFetchSvg,a=m3(j9,(e,s)=>{let n=s[0],t=s[1],i=s[2];return t==="far"&&!c&&(t="fas"),typeof n=="string"&&(e.names[n]={prefix:t,iconName:i}),typeof n=="number"&&(e.unicodes[n.toString(16)]={prefix:t,iconName:i}),e},{names:{},unicodes:{}});M0=a.names,C0=a.unicodes,E3=A2(m.styleDefault,{family:m.familyDefault})};D9(l=>{E3=A2(l.styleDefault,{family:m.familyDefault})});v0();function I3(l,c){return(L0[l]||{})[c]}function $9(l,c){return(d0[l]||{})[c]}function L1(l,c){return(g0[l]||{})[c]}function x0(l){return M0[l]||{prefix:null,iconName:null}}function Y9(l){let c=C0[l],a=I3("fas",l);return c||(a?{prefix:"fas",iconName:a}:null)||{prefix:null,iconName:null}}function a1(){return E3}var b0=()=>({prefix:null,iconName:null,rest:[]});function X9(l){let c=y,a=p0.reduce((e,s)=>(e[s]="".concat(m.cssPrefix,"-").concat(s),e),{});return s0.forEach(e=>{(l.includes(a[e])||l.some(s=>G9[e].includes(s)))&&(c=e)}),c}function A2(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{family:a=y}=c,e=N9[a][l];if(a===S2&&!l)return"fad";let s=k4[a][l]||k4[a][e],n=l in O.styles?l:null;return s||n||null}function K9(l){let c=[],a=null;return l.forEach(e=>{let s=Z9(m.cssPrefix,e);s?a=s:e&&c.push(e)}),{iconName:a,rest:c}}function P4(l){return l.sort().filter((c,a,e)=>e.indexOf(c)===a)}function V2(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{skipLookups:a=!1}=c,e=null,s=p3.concat(z9),n=P4(l.filter(L=>s.includes(L))),t=P4(l.filter(L=>!p3.includes(L))),i=n.filter(L=>(e=L,!l0.includes(L))),[f=null]=i,r=X9(n),z=o(o({},K9(t)),{},{prefix:A2(f,{family:r})});return o(o(o({},z),ec({values:l,family:r,styles:Q1,config:m,canonical:z,givenPrefix:e})),J9(a,e,z))}function J9(l,c,a){let{prefix:e,iconName:s}=a;if(l||!e||!s)return{prefix:e,iconName:s};let n=c==="fa"?x0(s):{},t=L1(e,s);return s=n.iconName||t||s,e=n.prefix||e,e==="far"&&!Q1.far&&Q1.fas&&!m.autoFetchSvg&&(e="fas"),{prefix:e,iconName:s}}var Q9=s0.filter(l=>l!==y||l!==S2),cc=Object.keys(u3).filter(l=>l!==y).map(l=>Object.keys(u3[l])).flat();function ec(l){let{values:c,family:a,canonical:e,givenPrefix:s="",styles:n={},config:t={}}=l,i=a===S2,f=c.includes("fa-duotone")||c.includes("fad"),r=t.familyDefault==="duotone",z=e.prefix==="fad"||e.prefix==="fa-duotone";if(!i&&(f||r||z)&&(e.prefix="fad"),(c.includes("fa-brands")||c.includes("fab"))&&(e.prefix="fab"),!e.prefix&&Q9.includes(a)&&(Object.keys(n).find(p=>cc.includes(p))||t.autoFetchSvg)){let p=a9.get(a).defaultShortPrefixId;e.prefix=p,e.iconName=L1(e.prefix,e.iconName)||e.iconName}return(e.prefix==="fa"||s==="fa")&&(e.prefix=a1()||"fas"),e}var b3=class{constructor(){this.definitions={}}add(){for(var c=arguments.length,a=new Array(c),e=0;e<c;e++)a[e]=arguments[e];let s=a.reduce(this._pullDefinitions,{});Object.keys(s).forEach(n=>{this.definitions[n]=o(o({},this.definitions[n]||{}),s[n]),x3(n,s[n]);let t=F3[y][n];t&&x3(t,s[n]),v0()})}reset(){this.definitions={}}_pullDefinitions(c,a){let e=a.prefix&&a.iconName&&a.icon?{0:a}:a;return Object.keys(e).map(s=>{let{prefix:n,iconName:t,icon:i}=e[s],f=i[2];c[n]||(c[n]={}),f.length>0&&f.forEach(r=>{typeof r=="string"&&(c[n][r]=i)}),c[n][t]=i}),c}},T4=[],w1={},A1={},lc=Object.keys(A1);function sc(l,c){let{mixoutsTo:a}=c;return T4=l,w1={},Object.keys(A1).forEach(e=>{lc.indexOf(e)===-1&&delete A1[e]}),T4.forEach(e=>{let s=e.mixout?e.mixout():{};if(Object.keys(s).forEach(n=>{typeof s[n]=="function"&&(a[n]=s[n]),typeof s[n]=="object"&&Object.keys(s[n]).forEach(t=>{a[n]||(a[n]={}),a[n][t]=s[n][t]})}),e.hooks){let n=e.hooks();Object.keys(n).forEach(t=>{w1[t]||(w1[t]=[]),w1[t].push(n[t])})}e.provides&&e.provides(A1)}),a}function N3(l,c){for(var a=arguments.length,e=new Array(a>2?a-2:0),s=2;s<a;s++)e[s-2]=arguments[s];return(w1[l]||[]).forEach(t=>{c=t.apply(null,[c,...e])}),c}function M1(l){for(var c=arguments.length,a=new Array(c>1?c-1:0),e=1;e<c;e++)a[e-1]=arguments[e];(w1[l]||[]).forEach(n=>{n.apply(null,a)})}function n1(){let l=arguments[0],c=Array.prototype.slice.call(arguments,1);return A1[l]?A1[l].apply(null,c):void 0}function y3(l){l.prefix==="fa"&&(l.prefix="fas");let{iconName:c}=l,a=l.prefix||a1();if(c)return c=L1(a,c)||c,_4(N0.definitions,a,c)||_4(O.styles,a,c)}var N0=new b3,ac=()=>{m.autoReplaceSvg=!1,m.observeMutations=!1,M1("noAuto")},nc={i2svg:function(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Y?(M1("beforeI2svg",l),n1("pseudoElements2svg",l),n1("i2svg",l)):Promise.reject(new Error("Operation requires a DOM of some kind."))},watch:function(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{autoReplaceSvgRoot:c}=l;m.autoReplaceSvg===!1&&(m.autoReplaceSvg=!0),m.observeMutations=!0,R9(()=>{ic({autoReplaceSvgRoot:c}),M1("watch",l)})}},tc={icon:l=>{if(l===null)return null;if(typeof l=="object"&&l.prefix&&l.iconName)return{prefix:l.prefix,iconName:L1(l.prefix,l.iconName)||l.iconName};if(Array.isArray(l)&&l.length===2){let c=l[1].indexOf("fa-")===0?l[1].slice(3):l[1],a=A2(l[0]);return{prefix:a,iconName:L1(a,c)||c}}if(typeof l=="string"&&(l.indexOf("".concat(m.cssPrefix,"-"))>-1||l.match(y9))){let c=V2(l.split(" "),{skipLookups:!0});return{prefix:c.prefix||a1(),iconName:L1(c.prefix,c.iconName)||c.iconName}}if(typeof l=="string"){let c=a1();return{prefix:c,iconName:L1(c,l)||l}}}},V={noAuto:ac,config:m,dom:nc,parse:tc,library:N0,findIconDefinition:y3,toHtml:e2},ic=function(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{autoReplaceSvgRoot:c=C}=l;(Object.keys(O.styles).length>0||m.autoFetchSvg)&&Y&&m.autoReplaceSvg&&V.dom.i2svg({node:c})};function H2(l,c){return Object.defineProperty(l,"abstract",{get:c}),Object.defineProperty(l,"html",{get:function(){return l.abstract.map(a=>e2(a))}}),Object.defineProperty(l,"node",{get:function(){if(!Y)return;let a=C.createElement("div");return a.innerHTML=l.html,a.children}}),l}function oc(l){let{children:c,main:a,mask:e,attributes:s,styles:n,transform:t}=l;if(T3(t)&&a.found&&!e.found){let{width:i,height:f}=a,r={x:i/f/2,y:.5};s.style=w2(o(o({},n),{},{"transform-origin":"".concat(r.x+t.x/16,"em ").concat(r.y+t.y/16,"em")}))}return[{tag:"svg",attributes:s,children:c}]}function rc(l){let{prefix:c,iconName:a,children:e,attributes:s,symbol:n}=l,t=n===!0?"".concat(c,"-").concat(m.cssPrefix,"-").concat(a):n;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:o(o({},s),{},{id:t}),children:e}]}]}function O3(l){let{icons:{main:c,mask:a},prefix:e,iconName:s,transform:n,symbol:t,title:i,maskId:f,titleId:r,extra:z,watchable:L=!1}=l,{width:p,height:S}=a.found?a:c,J=r9.includes(e),f1=[m.replacementClass,s?"".concat(m.cssPrefix,"-").concat(s):""].filter(v1=>z.classes.indexOf(v1)===-1).filter(v1=>v1!==""||!!v1).concat(z.classes).join(" "),D={children:[],attributes:o(o({},z.attributes),{},{"data-prefix":e,"data-icon":s,class:f1,role:z.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(p," ").concat(S)})},R=J&&!~z.classes.indexOf("fa-fw")?{width:"".concat(p/S*16*.0625,"em")}:{};L&&(D.attributes[d1]=""),i&&(D.children.push({tag:"title",attributes:{id:D.attributes["aria-labelledby"]||"title-".concat(r||J1())},children:[i]}),delete D.attributes.title);let A=o(o({},D),{},{prefix:e,iconName:s,main:c,mask:a,maskId:f,transform:n,symbol:t,styles:o(o({},R),z.styles)}),{children:_,attributes:g1}=a.found&&c.found?n1("generateAbstractMask",A)||{children:[],attributes:{}}:n1("generateAbstractIcon",A)||{children:[],attributes:{}};return A.children=_,A.attributes=g1,t?rc(A):oc(A)}function E4(l){let{content:c,width:a,height:e,transform:s,title:n,extra:t,watchable:i=!1}=l,f=o(o(o({},t.attributes),n?{title:n}:{}),{},{class:t.classes.join(" ")});i&&(f[d1]="");let r=o({},t.styles);T3(s)&&(r.transform=E9({transform:s,startCentered:!0,width:a,height:e}),r["-webkit-transform"]=r.transform);let z=w2(r);z.length>0&&(f.style=z);let L=[];return L.push({tag:"span",attributes:f,children:[c]}),n&&L.push({tag:"span",attributes:{class:"sr-only"},children:[n]}),L}function fc(l){let{content:c,title:a,extra:e}=l,s=o(o(o({},e.attributes),a?{title:a}:{}),{},{class:e.classes.join(" ")}),n=w2(e.styles);n.length>0&&(s.style=n);let t=[];return t.push({tag:"span",attributes:s,children:[c]}),a&&t.push({tag:"span",attributes:{class:"sr-only"},children:[a]}),t}var{styles:z3}=O;function S3(l){let c=l[0],a=l[1],[e]=l.slice(4),s=null;return Array.isArray(e)?s={tag:"g",attributes:{class:"".concat(m.cssPrefix,"-").concat(r3.GROUP)},children:[{tag:"path",attributes:{class:"".concat(m.cssPrefix,"-").concat(r3.SECONDARY),fill:"currentColor",d:e[0]}},{tag:"path",attributes:{class:"".concat(m.cssPrefix,"-").concat(r3.PRIMARY),fill:"currentColor",d:e[1]}}]}:s={tag:"path",attributes:{fill:"currentColor",d:e}},{found:!0,width:c,height:a,icon:s}}var mc={found:!1,width:512,height:512};function zc(l,c){!i0&&!m.showMissingIcons&&l&&console.error('Icon with name "'.concat(l,'" and prefix "').concat(c,'" is missing.'))}function w3(l,c){let a=c;return c==="fa"&&m.styleDefault!==null&&(c=a1()),new Promise((e,s)=>{if(a==="fa"){let n=x0(l)||{};l=n.iconName||l,c=n.prefix||c}if(l&&c&&z3[c]&&z3[c][l]){let n=z3[c][l];return e(S3(n))}zc(l,c),e(o(o({},mc),{},{icon:m.showMissingIcons&&l?n1("missingIconAbstract")||{}:{}}))})}var I4=()=>{},A3=m.measurePerformance&&g2&&g2.mark&&g2.measure?g2:{mark:I4,measure:I4},Y1='FA "6.7.2"',hc=l=>(A3.mark("".concat(Y1," ").concat(l," begins")),()=>y0(l)),y0=l=>{A3.mark("".concat(Y1," ").concat(l," ends")),A3.measure("".concat(Y1," ").concat(l),"".concat(Y1," ").concat(l," begins"),"".concat(Y1," ").concat(l," ends"))},R3={begin:hc,end:y0},x2=()=>{};function O4(l){return typeof(l.getAttribute?l.getAttribute(d1):null)=="string"}function uc(l){let c=l.getAttribute?l.getAttribute(D3):null,a=l.getAttribute?l.getAttribute(_3):null;return c&&a}function pc(l){return l&&l.classList&&l.classList.contains&&l.classList.contains(m.replacementClass)}function Lc(){return m.autoReplaceSvg===!0?b2.replace:b2[m.autoReplaceSvg]||b2.replace}function dc(l){return C.createElementNS("http://www.w3.org/2000/svg",l)}function Mc(l){return C.createElement(l)}function S0(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{ceFn:a=l.tag==="svg"?dc:Mc}=c;if(typeof l=="string")return C.createTextNode(l);let e=a(l.tag);return Object.keys(l.attributes||[]).forEach(function(n){e.setAttribute(n,l.attributes[n])}),(l.children||[]).forEach(function(n){e.appendChild(S0(n,{ceFn:a}))}),e}function Cc(l){let c=" ".concat(l.outerHTML," ");return c="".concat(c,"Font Awesome fontawesome.com "),c}var b2={replace:function(l){let c=l[0];if(c.parentNode)if(l[1].forEach(a=>{c.parentNode.insertBefore(S0(a),c)}),c.getAttribute(d1)===null&&m.keepOriginalSource){let a=C.createComment(Cc(c));c.parentNode.replaceChild(a,c)}else c.remove()},nest:function(l){let c=l[0],a=l[1];if(~P3(c).indexOf(m.replacementClass))return b2.replace(l);let e=new RegExp("".concat(m.cssPrefix,"-.*"));if(delete a[0].attributes.id,a[0].attributes.class){let n=a[0].attributes.class.split(" ").reduce((t,i)=>(i===m.replacementClass||i.match(e)?t.toSvg.push(i):t.toNode.push(i),t),{toNode:[],toSvg:[]});a[0].attributes.class=n.toSvg.join(" "),n.toNode.length===0?c.removeAttribute("class"):c.setAttribute("class",n.toNode.join(" "))}let s=a.map(n=>e2(n)).join(`
`);c.setAttribute(d1,""),c.innerHTML=s}};function R4(l){l()}function w0(l,c){let a=typeof c=="function"?c:x2;if(l.length===0)a();else{let e=R4;m.mutateApproach===x9&&(e=s1.requestAnimationFrame||R4),e(()=>{let s=Lc(),n=R3.begin("mutate");l.map(s),n(),a()})}}var B3=!1;function A0(){B3=!0}function V3(){B3=!1}var y2=null;function B4(l){if(!w4||!m.observeMutations)return;let{treeCallback:c=x2,nodeCallback:a=x2,pseudoElementsCallback:e=x2,observeMutationsRoot:s=C}=l;y2=new w4(n=>{if(B3)return;let t=a1();H1(n).forEach(i=>{if(i.type==="childList"&&i.addedNodes.length>0&&!O4(i.addedNodes[0])&&(m.searchPseudoElements&&e(i.target),c(i.target)),i.type==="attributes"&&i.target.parentNode&&m.searchPseudoElements&&e(i.target.parentNode),i.type==="attributes"&&O4(i.target)&&~A9.indexOf(i.attributeName))if(i.attributeName==="class"&&uc(i.target)){let{prefix:f,iconName:r}=V2(P3(i.target));i.target.setAttribute(D3,f||t),r&&i.target.setAttribute(_3,r)}else pc(i.target)&&a(i.target)})}),Y&&y2.observe(s,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}function gc(){y2&&y2.disconnect()}function vc(l){let c=l.getAttribute("style"),a=[];return c&&(a=c.split(";").reduce((e,s)=>{let n=s.split(":"),t=n[0],i=n.slice(1);return t&&i.length>0&&(e[t]=i.join(":").trim()),e},{})),a}function xc(l){let c=l.getAttribute("data-prefix"),a=l.getAttribute("data-icon"),e=l.innerText!==void 0?l.innerText.trim():"",s=V2(P3(l));return s.prefix||(s.prefix=a1()),c&&a&&(s.prefix=c,s.iconName=a),s.iconName&&s.prefix||(s.prefix&&e.length>0&&(s.iconName=$9(s.prefix,l.innerText)||I3(s.prefix,v3(l.innerText))),!s.iconName&&m.autoFetchSvg&&l.firstChild&&l.firstChild.nodeType===Node.TEXT_NODE&&(s.iconName=l.firstChild.data)),s}function bc(l){let c=H1(l.attributes).reduce((s,n)=>(s.name!=="class"&&s.name!=="style"&&(s[n.name]=n.value),s),{}),a=l.getAttribute("title"),e=l.getAttribute("data-fa-title-id");return m.autoA11y&&(a?c["aria-labelledby"]="".concat(m.replacementClass,"-title-").concat(e||J1()):(c["aria-hidden"]="true",c.focusable="false")),c}function Nc(){return{iconName:null,title:null,titleId:null,prefix:null,transform:I,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}}}function q4(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{styleParser:!0},{iconName:a,prefix:e,rest:s}=xc(l),n=bc(l),t=N3("parseNodeAttributes",{},l),i=c.styleParser?vc(l):[];return o({iconName:a,title:l.getAttribute("title"),titleId:l.getAttribute("data-fa-title-id"),prefix:e,transform:I,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:s,styles:i,attributes:n}},t)}var{styles:yc}=O;function V0(l){let c=m.autoReplaceSvg==="nest"?q4(l,{styleParser:!1}):q4(l);return~c.extra.classes.indexOf(r0)?n1("generateLayersText",l,c):n1("generateSvgReplacementMutation",l,c)}function Sc(){return[...t9,...p3]}function U4(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(!Y)return Promise.resolve();let a=C.documentElement.classList,e=z=>a.add("".concat(H4,"-").concat(z)),s=z=>a.remove("".concat(H4,"-").concat(z)),n=m.autoFetchSvg?Sc():l0.concat(Object.keys(yc));n.includes("fa")||n.push("fa");let t=[".".concat(r0,":not([").concat(d1,"])")].concat(n.map(z=>".".concat(z,":not([").concat(d1,"])"))).join(", ");if(t.length===0)return Promise.resolve();let i=[];try{i=H1(l.querySelectorAll(t))}catch{}if(i.length>0)e("pending"),s("complete");else return Promise.resolve();let f=R3.begin("onTree"),r=i.reduce((z,L)=>{try{let p=V0(L);p&&z.push(p)}catch(p){i0||p.name==="MissingIcon"&&console.error(p)}return z},[]);return new Promise((z,L)=>{Promise.all(r).then(p=>{w0(p,()=>{e("active"),e("complete"),s("pending"),typeof c=="function"&&c(),f(),z()})}).catch(p=>{f(),L(p)})})}function wc(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;V0(l).then(a=>{a&&w0([a],c)})}function Ac(l){return function(c){let a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=(c||{}).icon?c:y3(c||{}),{mask:s}=a;return s&&(s=(s||{}).icon?s:y3(s||{})),l(e,o(o({},a),{},{mask:s}))}}var Vc=function(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{transform:a=I,symbol:e=!1,mask:s=null,maskId:n=null,title:t=null,titleId:i=null,classes:f=[],attributes:r={},styles:z={}}=c;if(!l)return;let{prefix:L,iconName:p,icon:S}=l;return H2(o({type:"icon"},l),()=>(M1("beforeDOMElementCreation",{iconDefinition:l,params:c}),m.autoA11y&&(t?r["aria-labelledby"]="".concat(m.replacementClass,"-title-").concat(i||J1()):(r["aria-hidden"]="true",r.focusable="false")),O3({icons:{main:S3(S),mask:s?S3(s.icon):{found:!1,width:null,height:null,icon:{}}},prefix:L,iconName:p,transform:o(o({},I),a),symbol:e,title:t,maskId:n,titleId:i,extra:{attributes:r,styles:z,classes:f}})))},Hc={mixout(){return{icon:Ac(Vc)}},hooks(){return{mutationObserverCallbacks(l){return l.treeCallback=U4,l.nodeCallback=wc,l}}},provides(l){l.i2svg=function(c){let{node:a=C,callback:e=()=>{}}=c;return U4(a,e)},l.generateSvgReplacementMutation=function(c,a){let{iconName:e,title:s,titleId:n,prefix:t,transform:i,symbol:f,mask:r,maskId:z,extra:L}=a;return new Promise((p,S)=>{Promise.all([w3(e,t),r.iconName?w3(r.iconName,r.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then(J=>{let[f1,D]=J;p([c,O3({icons:{main:f1,mask:D},prefix:t,iconName:e,transform:i,symbol:f,maskId:z,title:s,titleId:n,extra:L,watchable:!0})])}).catch(S)})},l.generateAbstractIcon=function(c){let{children:a,attributes:e,main:s,transform:n,styles:t}=c,i=w2(t);i.length>0&&(e.style=i);let f;return T3(n)&&(f=n1("generateAbstractTransformGrouping",{main:s,transform:n,containerWidth:s.width,iconWidth:s.width})),a.push(f||s.icon),{children:a,attributes:e}}}},kc={mixout(){return{layer(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{classes:a=[]}=c;return H2({type:"layer"},()=>{M1("beforeDOMElementCreation",{assembler:l,params:c});let e=[];return l(s=>{Array.isArray(s)?s.map(n=>{e=e.concat(n.abstract)}):e=e.concat(s.abstract)}),[{tag:"span",attributes:{class:["".concat(m.cssPrefix,"-layers"),...a].join(" ")},children:e}]})}}}},Dc={mixout(){return{counter(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{title:a=null,classes:e=[],attributes:s={},styles:n={}}=c;return H2({type:"counter",content:l},()=>(M1("beforeDOMElementCreation",{content:l,params:c}),fc({content:l.toString(),title:a,extra:{attributes:s,styles:n,classes:["".concat(m.cssPrefix,"-layers-counter"),...e]}})))}}}},_c={mixout(){return{text(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{transform:a=I,title:e=null,classes:s=[],attributes:n={},styles:t={}}=c;return H2({type:"text",content:l},()=>(M1("beforeDOMElementCreation",{content:l,params:c}),E4({content:l,transform:o(o({},I),a),title:e,extra:{attributes:n,styles:t,classes:["".concat(m.cssPrefix,"-layers-text"),...s]}})))}}},provides(l){l.generateLayersText=function(c,a){let{title:e,transform:s,extra:n}=a,t=null,i=null;if(c0){let f=parseInt(getComputedStyle(c).fontSize,10),r=c.getBoundingClientRect();t=r.width/f,i=r.height/f}return m.autoA11y&&!e&&(n.attributes["aria-hidden"]="true"),Promise.resolve([c,E4({content:c.innerHTML,width:t,height:i,transform:s,title:e,extra:n,watchable:!0})])}}},Fc=new RegExp('"',"ug"),j4=[1105920,1112319],G4=o(o(o(o({},{FontAwesome:{normal:"fas",400:"fas"}}),s9),g9),h9),H3=Object.keys(G4).reduce((l,c)=>(l[c.toLowerCase()]=G4[c],l),{}),Pc=Object.keys(H3).reduce((l,c)=>{let a=H3[c];return l[c]=a[900]||[...Object.entries(a)][0][1],l},{});function Tc(l){let c=l.replace(Fc,""),a=U9(c,0),e=a>=j4[0]&&a<=j4[1],s=c.length===2?c[0]===c[1]:!1;return{value:v3(s?c[0]:c),isSecondary:e||s}}function Ec(l,c){let a=l.replace(/^['"]|['"]$/g,"").toLowerCase(),e=parseInt(c),s=isNaN(e)?"normal":e;return(H3[a]||{})[s]||Pc[a]}function W4(l,c){let a="".concat(v9).concat(c.replace(":","-"));return new Promise((e,s)=>{if(l.getAttribute(a)!==null)return e();let t=H1(l.children).filter(p=>p.getAttribute(d3)===c)[0],i=s1.getComputedStyle(l,c),f=i.getPropertyValue("font-family"),r=f.match(S9),z=i.getPropertyValue("font-weight"),L=i.getPropertyValue("content");if(t&&!r)return l.removeChild(t),e();if(r&&L!=="none"&&L!==""){let p=i.getPropertyValue("content"),S=Ec(f,z),{value:J,isSecondary:f1}=Tc(p),D=r[0].startsWith("FontAwesome"),R=I3(S,J),A=R;if(D){let _=Y9(J);_.iconName&&_.prefix&&(R=_.iconName,S=_.prefix)}if(R&&!f1&&(!t||t.getAttribute(D3)!==S||t.getAttribute(_3)!==A)){l.setAttribute(a,A),t&&l.removeChild(t);let _=Nc(),{extra:g1}=_;g1.attributes[d3]=c,w3(R,S).then(v1=>{let E7=O3(o(o({},_),{},{icons:{main:v1,mask:b0()},prefix:S,iconName:A,extra:g1,watchable:!0})),W2=C.createElementNS("http://www.w3.org/2000/svg","svg");c==="::before"?l.insertBefore(W2,l.firstChild):l.appendChild(W2),W2.outerHTML=E7.map(I7=>e2(I7)).join(`
`),l.removeAttribute(a),e()}).catch(s)}else e()}else e()})}function Ic(l){return Promise.all([W4(l,"::before"),W4(l,"::after")])}function Oc(l){return l.parentNode!==document.head&&!~b9.indexOf(l.tagName.toUpperCase())&&!l.getAttribute(d3)&&(!l.parentNode||l.parentNode.tagName!=="svg")}function Z4(l){if(Y)return new Promise((c,a)=>{let e=H1(l.querySelectorAll("*")).filter(Oc).map(Ic),s=R3.begin("searchPseudoElements");A0(),Promise.all(e).then(()=>{s(),V3(),c()}).catch(()=>{s(),V3(),a()})})}var Rc={hooks(){return{mutationObserverCallbacks(l){return l.pseudoElementsCallback=Z4,l}}},provides(l){l.pseudoElements2svg=function(c){let{node:a=C}=c;m.searchPseudoElements&&Z4(a)}}},$4=!1,Bc={mixout(){return{dom:{unwatch(){A0(),$4=!0}}}},hooks(){return{bootstrap(){B4(N3("mutationObserverCallbacks",{}))},noAuto(){gc()},watch(l){let{observeMutationsRoot:c}=l;$4?V3():B4(N3("mutationObserverCallbacks",{observeMutationsRoot:c}))}}}},Y4=l=>{let c={size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0};return l.toLowerCase().split(" ").reduce((a,e)=>{let s=e.toLowerCase().split("-"),n=s[0],t=s.slice(1).join("-");if(n&&t==="h")return a.flipX=!0,a;if(n&&t==="v")return a.flipY=!0,a;if(t=parseFloat(t),isNaN(t))return a;switch(n){case"grow":a.size=a.size+t;break;case"shrink":a.size=a.size-t;break;case"left":a.x=a.x-t;break;case"right":a.x=a.x+t;break;case"up":a.y=a.y-t;break;case"down":a.y=a.y+t;break;case"rotate":a.rotate=a.rotate+t;break}return a},c)},qc={mixout(){return{parse:{transform:l=>Y4(l)}}},hooks(){return{parseNodeAttributes(l,c){let a=c.getAttribute("data-fa-transform");return a&&(l.transform=Y4(a)),l}}},provides(l){l.generateAbstractTransformGrouping=function(c){let{main:a,transform:e,containerWidth:s,iconWidth:n}=c,t={transform:"translate(".concat(s/2," 256)")},i="translate(".concat(e.x*32,", ").concat(e.y*32,") "),f="scale(".concat(e.size/16*(e.flipX?-1:1),", ").concat(e.size/16*(e.flipY?-1:1),") "),r="rotate(".concat(e.rotate," 0 0)"),z={transform:"".concat(i," ").concat(f," ").concat(r)},L={transform:"translate(".concat(n/2*-1," -256)")},p={outer:t,inner:z,path:L};return{tag:"g",attributes:o({},p.outer),children:[{tag:"g",attributes:o({},p.inner),children:[{tag:a.icon.tag,children:a.icon.children,attributes:o(o({},a.icon.attributes),p.path)}]}]}}}},h3={x:0,y:0,width:"100%",height:"100%"};function X4(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return l.attributes&&(l.attributes.fill||c)&&(l.attributes.fill="black"),l}function Uc(l){return l.tag==="g"?l.children:[l]}var jc={hooks(){return{parseNodeAttributes(l,c){let a=c.getAttribute("data-fa-mask"),e=a?V2(a.split(" ").map(s=>s.trim())):b0();return e.prefix||(e.prefix=a1()),l.mask=e,l.maskId=c.getAttribute("data-fa-mask-id"),l}}},provides(l){l.generateAbstractMask=function(c){let{children:a,attributes:e,main:s,mask:n,maskId:t,transform:i}=c,{width:f,icon:r}=s,{width:z,icon:L}=n,p=T9({transform:i,containerWidth:z,iconWidth:f}),S={tag:"rect",attributes:o(o({},h3),{},{fill:"white"})},J=r.children?{children:r.children.map(X4)}:{},f1={tag:"g",attributes:o({},p.inner),children:[X4(o({tag:r.tag,attributes:o(o({},r.attributes),p.path)},J))]},D={tag:"g",attributes:o({},p.outer),children:[f1]},R="mask-".concat(t||J1()),A="clip-".concat(t||J1()),_={tag:"mask",attributes:o(o({},h3),{},{id:R,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[S,D]},g1={tag:"defs",children:[{tag:"clipPath",attributes:{id:A},children:Uc(L)},_]};return a.push(g1,{tag:"rect",attributes:o({fill:"currentColor","clip-path":"url(#".concat(A,")"),mask:"url(#".concat(R,")")},h3)}),{children:a,attributes:e}}}},Gc={provides(l){let c=!1;s1.matchMedia&&(c=s1.matchMedia("(prefers-reduced-motion: reduce)").matches),l.missingIconAbstract=function(){let a=[],e={fill:"currentColor"},s={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};a.push({tag:"path",attributes:o(o({},e),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});let n=o(o({},s),{},{attributeName:"opacity"}),t={tag:"circle",attributes:o(o({},e),{},{cx:"256",cy:"364",r:"28"}),children:[]};return c||t.children.push({tag:"animate",attributes:o(o({},s),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:o(o({},n),{},{values:"1;0;1;1;0;1;"})}),a.push(t),a.push({tag:"path",attributes:o(o({},e),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:c?[]:[{tag:"animate",attributes:o(o({},n),{},{values:"1;0;0;0;0;1;"})}]}),c||a.push({tag:"path",attributes:o(o({},e),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:o(o({},n),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:a}}}},Wc={hooks(){return{parseNodeAttributes(l,c){let a=c.getAttribute("data-fa-symbol"),e=a===null?!1:a===""?!0:a;return l.symbol=e,l}}}},Zc=[O9,Hc,kc,Dc,_c,Rc,Bc,qc,jc,Gc,Wc];sc(Zc,{mixoutsTo:V});var zs=V.noAuto,hs=V.config,us=V.library,ps=V.dom,H0=V.parse,Ls=V.findIconDefinition,ds=V.toHtml,k0=V.icon,Ms=V.layer,$c=V.text,Yc=V.counter;var Xc=["*"],Kc=l=>{throw new Error(`Could not find icon with iconName=${l.iconName} and prefix=${l.prefix} in the icon library.`)},Jc=()=>{throw new Error("Property `icon` is required for `fa-icon`/`fa-duotone-icon` components.")},Qc=l=>{let c={[`fa-${l.animation}`]:l.animation!=null&&!l.animation.startsWith("spin"),"fa-spin":l.animation==="spin"||l.animation==="spin-reverse","fa-spin-pulse":l.animation==="spin-pulse"||l.animation==="spin-pulse-reverse","fa-spin-reverse":l.animation==="spin-reverse"||l.animation==="spin-pulse-reverse","fa-pulse":l.animation==="spin-pulse"||l.animation==="spin-pulse-reverse","fa-fw":l.fixedWidth,"fa-border":l.border,"fa-inverse":l.inverse,"fa-layers-counter":l.counter,"fa-flip-horizontal":l.flip==="horizontal"||l.flip==="both","fa-flip-vertical":l.flip==="vertical"||l.flip==="both",[`fa-${l.size}`]:l.size!==null,[`fa-rotate-${l.rotate}`]:l.rotate!==null,[`fa-pull-${l.pull}`]:l.pull!==null,[`fa-stack-${l.stackItemSize}`]:l.stackItemSize!=null};return Object.keys(c).map(a=>c[a]?a:null).filter(a=>a)},ce=l=>l.prefix!==void 0&&l.iconName!==void 0,ee=(l,c)=>ce(l)?l:typeof l=="string"?{prefix:c,iconName:l}:{prefix:l[0],iconName:l[1]},le=(()=>{let c=class c{constructor(){this.defaultPrefix="fas",this.fallbackIcon=null}};c.\u0275fac=function(s){return new(s||c)},c.\u0275prov=g({token:c,factory:c.\u0275fac,providedIn:"root"});let l=c;return l})(),q3=(()=>{let c=class c{constructor(){this.definitions={}}addIcons(...e){for(let s of e){s.prefix in this.definitions||(this.definitions[s.prefix]={}),this.definitions[s.prefix][s.iconName]=s;for(let n of s.icon[2])typeof n=="string"&&(this.definitions[s.prefix][n]=s)}}addIconPacks(...e){for(let s of e){let n=Object.keys(s).map(t=>s[t]);this.addIcons(...n)}}getIconDefinition(e,s){return e in this.definitions&&s in this.definitions[e]?this.definitions[e][s]:null}};c.\u0275fac=function(s){return new(s||c)},c.\u0275prov=g({token:c,factory:c.\u0275fac,providedIn:"root"});let l=c;return l})(),se=(()=>{let c=class c{constructor(){this.stackItemSize="1x"}ngOnChanges(e){if("size"in e)throw new Error('fa-icon is not allowed to customize size when used inside fa-stack. Set size on the enclosing fa-stack instead: <fa-stack size="4x">...</fa-stack>.')}};c.\u0275fac=function(s){return new(s||c)},c.\u0275dir=M({type:c,selectors:[["fa-icon","stackItemSize",""],["fa-duotone-icon","stackItemSize",""]],inputs:{stackItemSize:"stackItemSize",size:"size"},standalone:!0,features:[q]});let l=c;return l})(),ae=(()=>{let c=class c{constructor(e,s){this.renderer=e,this.elementRef=s}ngOnInit(){this.renderer.addClass(this.elementRef.nativeElement,"fa-stack")}ngOnChanges(e){"size"in e&&(e.size.currentValue!=null&&this.renderer.addClass(this.elementRef.nativeElement,`fa-${e.size.currentValue}`),e.size.previousValue!=null&&this.renderer.removeClass(this.elementRef.nativeElement,`fa-${e.size.previousValue}`))}};c.\u0275fac=function(s){return new(s||c)(h(Q),h(U))},c.\u0275cmp=q1({type:c,selectors:[["fa-stack"]],inputs:{size:"size"},standalone:!0,features:[q,j1],ngContentSelectors:Xc,decls:1,vars:0,template:function(s,n){s&1&&(r4(),f4(0))},encapsulation:2});let l=c;return l})(),xs=(()=>{let c=class c{set spin(e){this.animation=e?"spin":void 0}set pulse(e){this.animation=e?"spin-pulse":void 0}constructor(e,s,n,t,i){this.sanitizer=e,this.config=s,this.iconLibrary=n,this.stackItem=t,this.classes=[],i!=null&&t==null&&console.error('FontAwesome: fa-icon and fa-duotone-icon elements must specify stackItemSize attribute when wrapped into fa-stack. Example: <fa-icon stackItemSize="2x"></fa-icon>.')}ngOnChanges(e){if(this.icon==null&&this.config.fallbackIcon==null){Jc();return}if(e){let s=this.icon!=null?this.icon:this.config.fallbackIcon,n=this.findIconDefinition(s);if(n!=null){let t=this.buildParams();this.renderIcon(n,t)}}}render(){this.ngOnChanges({})}findIconDefinition(e){let s=ee(e,this.config.defaultPrefix);if("icon"in s)return s;let n=this.iconLibrary.getIconDefinition(s.prefix,s.iconName);return n!=null?n:(Kc(s),null)}buildParams(){let e={flip:this.flip,animation:this.animation,border:this.border,inverse:this.inverse,size:this.size||null,pull:this.pull||null,rotate:this.rotate||null,fixedWidth:typeof this.fixedWidth=="boolean"?this.fixedWidth:this.config.fixedWidth,stackItemSize:this.stackItem!=null?this.stackItem.stackItemSize:null},s=typeof this.transform=="string"?H0.transform(this.transform):this.transform;return{title:this.title,transform:s,classes:[...Qc(e),...this.classes],mask:this.mask!=null?this.findIconDefinition(this.mask):null,styles:this.styles!=null?this.styles:{},symbol:this.symbol,attributes:{role:this.a11yRole}}}renderIcon(e,s){let n=k0(e,s);this.renderedIconHTML=this.sanitizer.bypassSecurityTrustHtml(n.html.join(`
`))}};c.\u0275fac=function(s){return new(s||c)(h(M4),h(le),h(q3),h(se,8),h(ae,8))},c.\u0275cmp=q1({type:c,selectors:[["fa-icon"]],hostAttrs:[1,"ng-fa-icon"],hostVars:2,hostBindings:function(s,n){s&2&&(o4("innerHTML",n.renderedIconHTML,i4),c1("title",n.title))},inputs:{icon:"icon",title:"title",animation:"animation",spin:"spin",pulse:"pulse",mask:"mask",styles:"styles",flip:"flip",size:"size",pull:"pull",border:"border",inverse:"inverse",symbol:"symbol",rotate:"rotate",fixedWidth:"fixedWidth",classes:"classes",transform:"transform",a11yRole:"a11yRole"},standalone:!0,features:[q,j1],decls:0,vars:0,template:function(s,n){},encapsulation:2});let l=c;return l})();var D0=(()=>{let c=class c{};c.\u0275fac=function(s){return new(s||c)},c.\u0275mod=P({type:c}),c.\u0275inj=F({});let l=c;return l})();var _0={prefix:"fas",iconName:"trash-can",icon:[448,512,[61460,"trash-alt"],"f2ed","M135.2 17.7C140.6 6.8 151.7 0 163.8 0L284.2 0c12.1 0 23.2 6.8 28.6 17.7L320 32l96 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 96C14.3 96 0 81.7 0 64S14.3 32 32 32l96 0 7.2-14.3zM32 128l384 0 0 320c0 35.3-28.7 64-64 64L96 512c-35.3 0-64-28.7-64-64l0-320zm96 64c-8.8 0-16 7.2-16 16l0 224c0 8.8 7.2 16 16 16s16-7.2 16-16l0-224c0-8.8-7.2-16-16-16zm96 0c-8.8 0-16 7.2-16 16l0 224c0 8.8 7.2 16 16 16s16-7.2 16-16l0-224c0-8.8-7.2-16-16-16zm96 0c-8.8 0-16 7.2-16 16l0 224c0 8.8 7.2 16 16 16s16-7.2 16-16l0-224c0-8.8-7.2-16-16-16z"]};var te={prefix:"fas",iconName:"file-lines",icon:[384,512,[128441,128462,61686,"file-alt","file-text"],"f15c","M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-288-128 0c-17.7 0-32-14.3-32-32L224 0 64 0zM256 0l0 128 128 0L256 0zM112 256l160 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-160 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l160 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-160 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l160 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-160 0c-8.8 0-16-7.2-16-16s7.2-16 16-16z"]},F0=te;var ie={prefix:"fas",iconName:"circle-minus",icon:[512,512,["minus-circle"],"f056","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM184 232l144 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-144 0c-13.3 0-24-10.7-24-24s10.7-24 24-24z"]},P0=ie;var T0={prefix:"fas",iconName:"comments",icon:[640,512,[128490,61670],"f086","M208 352c114.9 0 208-78.8 208-176S322.9 0 208 0S0 78.8 0 176c0 38.6 14.7 74.3 39.6 103.4c-3.5 9.4-8.7 17.7-14.2 24.7c-4.8 6.2-9.7 11-13.3 14.3c-1.8 1.6-3.3 2.9-4.3 3.7c-.5 .4-.9 .7-1.1 .8l-.2 .2s0 0 0 0s0 0 0 0C1 327.2-1.4 334.4 .8 340.9S9.1 352 16 352c21.8 0 43.8-5.6 62.1-12.5c9.2-3.5 17.8-7.4 25.2-11.4C134.1 343.3 169.8 352 208 352zM448 176c0 112.3-99.1 196.9-216.5 207C255.8 457.4 336.4 512 432 512c38.2 0 73.9-8.7 104.7-23.9c7.5 4 16 7.9 25.2 11.4c18.3 6.9 40.3 12.5 62.1 12.5c6.9 0 13.1-4.5 15.2-11.1c2.1-6.6-.2-13.8-5.8-17.9c0 0 0 0 0 0s0 0 0 0l-.2-.2c-.2-.2-.6-.4-1.1-.8c-1-.8-2.5-2-4.3-3.7c-3.6-3.3-8.5-8.1-13.3-14.3c-5.5-7-10.7-15.4-14.2-24.7c24.9-29 39.6-64.7 39.6-103.4c0-92.8-84.9-168.9-192.6-175.5c.4 5.1 .6 10.3 .6 15.5z"]};var E0={prefix:"fas",iconName:"user-check",icon:[640,512,[],"f4fc","M96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM0 482.3C0 383.8 79.8 304 178.3 304l91.4 0C368.2 304 448 383.8 448 482.3c0 16.4-13.3 29.7-29.7 29.7L29.7 512C13.3 512 0 498.7 0 482.3zM625 177L497 305c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L591 143c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"]};var I0={prefix:"fas",iconName:"table",icon:[512,512,[],"f0ce","M64 256l0-96 160 0 0 96L64 256zm0 64l160 0 0 96L64 416l0-96zm224 96l0-96 160 0 0 96-160 0zM448 256l-160 0 0-96 160 0 0 96zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"]};var O0={prefix:"fas",iconName:"bars",icon:[448,512,["navicon"],"f0c9","M0 96C0 78.3 14.3 64 32 64l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 128C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 288c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32L32 448c-17.7 0-32-14.3-32-32s14.3-32 32-32l384 0c17.7 0 32 14.3 32 32z"]};var oe={prefix:"fas",iconName:"heart-crack",icon:[512,512,[128148,"heart-broken"],"f7a9","M119.4 44.1c23.3-3.9 46.8-1.9 68.6 5.3l49.8 77.5-75.4 75.4c-1.5 1.5-2.4 3.6-2.3 5.8s1 4.2 2.6 5.7l112 104c2.9 2.7 7.4 2.9 10.5 .3s3.8-7 1.7-10.4l-60.4-98.1 90.7-75.6c2.6-2.1 3.5-5.7 2.4-8.8L296.8 61.8c28.5-16.7 62.4-23.2 95.7-17.6C461.5 55.6 512 115.2 512 185.1l0 5.8c0 41.5-17.2 81.2-47.6 109.5L283.7 469.1c-7.5 7-17.4 10.9-27.7 10.9s-20.2-3.9-27.7-10.9L47.6 300.4C17.2 272.1 0 232.4 0 190.9l0-5.8c0-69.9 50.5-129.5 119.4-141z"]},R0=oe;var B0={prefix:"fas",iconName:"lightbulb",icon:[384,512,[128161],"f0eb","M272 384c9.6-31.9 29.5-59.1 49.2-86.2c0 0 0 0 0 0c5.2-7.1 10.4-14.2 15.4-21.4c19.8-28.5 31.4-63 31.4-100.3C368 78.8 289.2 0 192 0S16 78.8 16 176c0 37.3 11.6 71.9 31.4 100.3c5 7.2 10.2 14.3 15.4 21.4c0 0 0 0 0 0c19.8 27.1 39.7 54.4 49.2 86.2l160 0zM192 512c44.2 0 80-35.8 80-80l0-16-160 0 0 16c0 44.2 35.8 80 80 80zM112 176c0 8.8-7.2 16-16 16s-16-7.2-16-16c0-61.9 50.1-112 112-112c8.8 0 16 7.2 16 16s-7.2 16-16 16c-44.2 0-80 35.8-80 80z"]};var U3={prefix:"fas",iconName:"circle-exclamation",icon:[512,512,["exclamation-circle"],"f06a","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-384c13.3 0 24 10.7 24 24l0 112c0 13.3-10.7 24-24 24s-24-10.7-24-24l0-112c0-13.3 10.7-24 24-24zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"]},q0=U3;var U0={prefix:"fas",iconName:"flag",icon:[448,512,[127988,61725],"f024","M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32L0 64 0 368 0 480c0 17.7 14.3 32 32 32s32-14.3 32-32l0-128 64.3-16.1c41.1-10.3 84.6-5.5 122.5 13.4c44.2 22.1 95.5 24.8 141.7 7.4l34.7-13c12.5-4.7 20.8-16.6 20.8-30l0-247.7c0-23-24.2-38-44.8-27.7l-9.6 4.8c-46.3 23.2-100.8 23.2-147.1 0c-35.1-17.6-75.4-22-113.5-12.5L64 48l0-16z"]};var k1={prefix:"fas",iconName:"list",icon:[512,512,["list-squares"],"f03a","M40 48C26.7 48 16 58.7 16 72l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24L40 48zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM16 232l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24l-48 0c-13.3 0-24 10.7-24 24zM40 368c-13.3 0-24 10.7-24 24l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24l-48 0z"]};var j0={prefix:"fas",iconName:"lock",icon:[448,512,[128274],"f023","M144 144l0 48 160 0 0-48c0-44.2-35.8-80-80-80s-80 35.8-80 80zM80 192l0-48C80 64.5 144.5 0 224 0s144 64.5 144 144l0 48 16 0c35.3 0 64 28.7 64 64l0 192c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 256c0-35.3 28.7-64 64-64l16 0z"]};var re={prefix:"fas",iconName:"pen-to-square",icon:[512,512,["edit"],"f044","M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 64C43 64 0 107 0 160L0 416c0 53 43 96 96 96l256 0c53 0 96-43 96-96l0-96c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 96c0 17.7-14.3 32-32 32L96 448c-17.7 0-32-14.3-32-32l0-256c0-17.7 14.3-32 32-32l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L96 64z"]},G0=re;var W0={prefix:"fas",iconName:"hourglass-half",icon:[384,512,["hourglass-2"],"f252","M32 0C14.3 0 0 14.3 0 32S14.3 64 32 64l0 11c0 42.4 16.9 83.1 46.9 113.1L146.7 256 78.9 323.9C48.9 353.9 32 394.6 32 437l0 11c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 256 0 32 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l0-11c0-42.4-16.9-83.1-46.9-113.1L237.3 256l67.9-67.9c30-30 46.9-70.7 46.9-113.1l0-11c17.7 0 32-14.3 32-32s-14.3-32-32-32L320 0 64 0 32 0zM96 75l0-11 192 0 0 11c0 19-5.6 37.4-16 53L112 128c-10.3-15.6-16-34-16-53zm16 309c3.5-5.3 7.6-10.3 12.1-14.9L192 301.3l67.9 67.9c4.6 4.6 8.6 9.6 12.1 14.9L112 384z"]};var Z0={prefix:"fas",iconName:"users",icon:[640,512,[],"f0c0","M144 0a80 80 0 1 1 0 160A80 80 0 1 1 144 0zM512 0a80 80 0 1 1 0 160A80 80 0 1 1 512 0zM0 298.7C0 239.8 47.8 192 106.7 192l42.7 0c15.9 0 31 3.5 44.6 9.7c-1.3 7.2-1.9 14.7-1.9 22.3c0 38.2 16.8 72.5 43.3 96c-.2 0-.4 0-.7 0L21.3 320C9.6 320 0 310.4 0 298.7zM405.3 320c-.2 0-.4 0-.7 0c26.6-23.5 43.3-57.8 43.3-96c0-7.6-.7-15-1.9-22.3c13.6-6.3 28.7-9.7 44.6-9.7l42.7 0C592.2 192 640 239.8 640 298.7c0 11.8-9.6 21.3-21.3 21.3l-213.3 0zM224 224a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zM128 485.3C128 411.7 187.7 352 261.3 352l117.3 0C452.3 352 512 411.7 512 485.3c0 14.7-11.9 26.7-26.7 26.7l-330.7 0c-14.7 0-26.7-11.9-26.7-26.7z"]};var fe={prefix:"fas",iconName:"hand",icon:[512,512,[129306,9995,"hand-paper"],"f256","M288 32c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-176c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 272c0 1.5 0 3.1 .1 4.6L67.6 283c-16-15.2-41.3-14.6-56.6 1.4s-14.6 41.3 1.4 56.6L124.8 448c43.1 41.1 100.4 64 160 64l19.2 0c97.2 0 176-78.8 176-176l0-208c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 112c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-176c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 176c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208z"]},$0=fe;var Y0={prefix:"fas",iconName:"plug",icon:[384,512,[128268],"f1e6","M96 0C78.3 0 64 14.3 64 32l0 96 64 0 0-96c0-17.7-14.3-32-32-32zM288 0c-17.7 0-32 14.3-32 32l0 96 64 0 0-96c0-17.7-14.3-32-32-32zM32 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l0 32c0 77.4 55 142 128 156.8l0 67.2c0 17.7 14.3 32 32 32s32-14.3 32-32l0-67.2C297 398 352 333.4 352 256l0-32c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 160z"]},X0={prefix:"fas",iconName:"chevron-up",icon:[512,512,[],"f077","M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"]};var K0={prefix:"fas",iconName:"angle-right",icon:[320,512,[8250],"f105","M278.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-160 160c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L210.7 256 73.4 118.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l160 160z"]};var J0={prefix:"fas",iconName:"folder",icon:[512,512,[128193,128447,61716,"folder-blank"],"f07b","M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"]};var Q0={prefix:"fas",iconName:"user",icon:[448,512,[128100,62144],"f007","M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304l-91.4 0z"]};var c6={prefix:"fas",iconName:"key",icon:[512,512,[128273],"f084","M336 352c97.2 0 176-78.8 176-176S433.2 0 336 0S160 78.8 160 176c0 18.7 2.9 36.8 8.3 53.7L7 391c-4.5 4.5-7 10.6-7 17l0 80c0 13.3 10.7 24 24 24l80 0c13.3 0 24-10.7 24-24l0-40 40 0c13.3 0 24-10.7 24-24l0-40 40 0c6.4 0 12.5-2.5 17-7l33.3-33.3c16.9 5.4 35 8.3 53.7 8.3zM376 96a40 40 0 1 1 0 80 40 40 0 1 1 0-80z"]};var e6={prefix:"fas",iconName:"globe",icon:[512,512,[127760],"f0ac","M352 256c0 22.2-1.2 43.6-3.3 64l-185.3 0c-2.2-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64l185.3 0c2.2 20.4 3.3 41.8 3.3 64zm28.8-64l123.1 0c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64l-123.1 0c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64zm112.6-32l-116.7 0c-10-63.9-29.8-117.4-55.3-151.6c78.3 20.7 142 77.5 171.9 151.6zm-149.1 0l-176.6 0c6.1-36.4 15.5-68.6 27-94.7c10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5c11.6 26 20.9 58.2 27 94.7zm-209 0L18.6 160C48.6 85.9 112.2 29.1 190.6 8.4C165.1 42.6 145.3 96.1 135.3 160zM8.1 192l123.1 0c-2.1 20.6-3.2 42-3.2 64s1.1 43.4 3.2 64L8.1 320C2.8 299.5 0 278.1 0 256s2.8-43.5 8.1-64zM194.7 446.6c-11.6-26-20.9-58.2-27-94.6l176.6 0c-6.1 36.4-15.5 68.6-27 94.6c-10.5 23.6-22.2 40.7-33.5 51.5C272.6 508.8 263.3 512 256 512s-16.6-3.2-27.8-13.8c-11.3-10.8-23-27.9-33.5-51.5zM135.3 352c10 63.9 29.8 117.4 55.3 151.6C112.2 482.9 48.6 426.1 18.6 352l116.7 0zm358.1 0c-30 74.1-93.6 130.9-171.9 151.6c25.5-34.2 45.2-87.7 55.3-151.6l116.7 0z"]};var l6={prefix:"fas",iconName:"money-bill-wave",icon:[576,512,[],"f53a","M0 112.5L0 422.3c0 18 10.1 35 27 41.3c87 32.5 174 10.3 261-11.9c79.8-20.3 159.6-40.7 239.3-18.9c23 6.3 48.7-9.5 48.7-33.4l0-309.9c0-18-10.1-35-27-41.3C462 15.9 375 38.1 288 60.3C208.2 80.6 128.4 100.9 48.7 79.1C25.6 72.8 0 88.6 0 112.5zM288 352c-44.2 0-80-43-80-96s35.8-96 80-96s80 43 80 96s-35.8 96-80 96zM64 352c35.3 0 64 28.7 64 64l-64 0 0-64zm64-208c0 35.3-28.7 64-64 64l0-64 64 0zM512 304l0 64-64 0c0-35.3 28.7-64 64-64zM448 96l64 0 0 64c-35.3 0-64-28.7-64-64z"]},s6={prefix:"fas",iconName:"chart-area",icon:[512,512,["area-chart"],"f1fe","M64 64c0-17.7-14.3-32-32-32S0 46.3 0 64L0 400c0 44.2 35.8 80 80 80l400 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 416c-8.8 0-16-7.2-16-16L64 64zm96 288l288 0c17.7 0 32-14.3 32-32l0-68.2c0-7.6-2.7-15-7.7-20.8l-65.8-76.8c-12.1-14.2-33.7-15-46.9-1.8l-21 21c-10 10-26.4 9.2-35.4-1.6l-39.2-47c-12.6-15.1-35.7-15.4-48.7-.6L135.9 215c-5.1 5.8-7.9 13.3-7.9 21.1l0 84c0 17.7 14.3 32 32 32z"]};var a6={prefix:"fas",iconName:"ban",icon:[512,512,[128683,"cancel"],"f05e","M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z"]};var n6={prefix:"fas",iconName:"star",icon:[576,512,[11088,61446],"f005","M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"]};var me={prefix:"fas",iconName:"arrow-pointer",icon:[320,512,["mouse-pointer"],"f245","M0 55.2L0 426c0 12.2 9.9 22 22 22c6.3 0 12.4-2.7 16.6-7.5L121.2 346l58.1 116.3c7.9 15.8 27.1 22.2 42.9 14.3s22.2-27.1 14.3-42.9L179.8 320l118.1 0c12.2 0 22.1-9.9 22.1-22.1c0-6.3-2.7-12.3-7.4-16.5L38.6 37.9C34.3 34.1 28.9 32 23.2 32C10.4 32 0 42.4 0 55.2z"]},t6=me;var i6={prefix:"fas",iconName:"server",icon:[512,512,[],"f233","M64 32C28.7 32 0 60.7 0 96l0 64c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-64c0-35.3-28.7-64-64-64L64 32zm280 72a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm48 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0zM64 288c-35.3 0-64 28.7-64 64l0 64c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-64c0-35.3-28.7-64-64-64L64 288zm280 72a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm56 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"]};var ze={prefix:"fas",iconName:"right-to-bracket",icon:[512,512,["sign-in-alt"],"f2f6","M217.9 105.9L340.7 228.7c7.2 7.2 11.3 17.1 11.3 27.3s-4.1 20.1-11.3 27.3L217.9 406.1c-6.4 6.4-15 9.9-24 9.9c-18.7 0-33.9-15.2-33.9-33.9l0-62.1L32 320c-17.7 0-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32l128 0 0-62.1c0-18.7 15.2-33.9 33.9-33.9c9 0 17.6 3.6 24 9.9zM352 416l64 0c17.7 0 32-14.3 32-32l0-256c0-17.7-14.3-32-32-32l-64 0c-17.7 0-32-14.3-32-32s14.3-32 32-32l64 0c53 0 96 43 96 96l0 256c0 53-43 96-96 96l-64 0c-17.7 0-32-14.3-32-32s14.3-32 32-32z"]},o6=ze;var he={prefix:"fas",iconName:"user-group",icon:[640,512,[128101,"user-friends"],"f500","M96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM0 482.3C0 383.8 79.8 304 178.3 304l91.4 0C368.2 304 448 383.8 448 482.3c0 16.4-13.3 29.7-29.7 29.7L29.7 512C13.3 512 0 498.7 0 482.3zM609.3 512l-137.8 0c5.4-9.4 8.6-20.3 8.6-32l0-8c0-60.7-27.1-115.2-69.8-151.8c2.4-.1 4.7-.2 7.1-.2l61.4 0C567.8 320 640 392.2 640 481.3c0 17-13.8 30.7-30.7 30.7zM432 256c-31 0-59-12.6-79.3-32.9C372.4 196.5 384 163.6 384 128c0-26.8-6.6-52.1-18.3-74.3C384.3 40.1 407.2 32 432 32c61.9 0 112 50.1 112 112s-50.1 112-112 112z"]},r6=he;var f6={prefix:"fas",iconName:"headset",icon:[512,512,[],"f590","M256 48C141.1 48 48 141.1 48 256l0 40c0 13.3-10.7 24-24 24s-24-10.7-24-24l0-40C0 114.6 114.6 0 256 0S512 114.6 512 256l0 144.1c0 48.6-39.4 88-88.1 88L313.6 488c-8.3 14.3-23.8 24-41.6 24l-32 0c-26.5 0-48-21.5-48-48s21.5-48 48-48l32 0c17.8 0 33.3 9.7 41.6 24l110.4 .1c22.1 0 40-17.9 40-40L464 256c0-114.9-93.1-208-208-208zM144 208l16 0c17.7 0 32 14.3 32 32l0 112c0 17.7-14.3 32-32 32l-16 0c-35.3 0-64-28.7-64-64l0-48c0-35.3 28.7-64 64-64zm224 0c35.3 0 64 28.7 64 64l0 48c0 35.3-28.7 64-64 64l-16 0c-17.7 0-32-14.3-32-32l0-112c0-17.7 14.3-32 32-32l16 0z"]};var m6={prefix:"fas",iconName:"user-pen",icon:[640,512,["user-edit"],"f4ff","M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512l293.1 0c-3.1-8.8-3.7-18.4-1.4-27.8l15-60.1c2.8-11.3 8.6-21.5 16.8-29.7l40.3-40.3c-32.1-31-75.7-50.1-123.9-50.1l-91.4 0zm435.5-68.3c-15.6-15.6-40.9-15.6-56.6 0l-29.4 29.4 71 71 29.4-29.4c15.6-15.6 15.6-40.9 0-56.6l-14.4-14.4zM375.9 417c-4.1 4.1-7 9.2-8.4 14.9l-15 60.1c-1.4 5.5 .2 11.2 4.2 15.2s9.7 5.6 15.2 4.2l60.1-15c5.6-1.4 10.8-4.3 14.9-8.4L576.1 358.7l-71-71L375.9 417z"]};var z6={prefix:"fas",iconName:"gift",icon:[512,512,[127873],"f06b","M190.5 68.8L225.3 128l-1.3 0-72 0c-22.1 0-40-17.9-40-40s17.9-40 40-40l2.2 0c14.9 0 28.8 7.9 36.3 20.8zM64 88c0 14.4 3.5 28 9.6 40L32 128c-17.7 0-32 14.3-32 32l0 64c0 17.7 14.3 32 32 32l448 0c17.7 0 32-14.3 32-32l0-64c0-17.7-14.3-32-32-32l-41.6 0c6.1-12 9.6-25.6 9.6-40c0-48.6-39.4-88-88-88l-2.2 0c-31.9 0-61.5 16.9-77.7 44.4L256 85.5l-24.1-41C215.7 16.9 186.1 0 154.2 0L152 0C103.4 0 64 39.4 64 88zm336 0c0 22.1-17.9 40-40 40l-72 0-1.3 0 34.8-59.2C329.1 55.9 342.9 48 357.8 48l2.2 0c22.1 0 40 17.9 40 40zM32 288l0 176c0 26.5 21.5 48 48 48l144 0 0-224L32 288zM288 512l144 0c26.5 0 48-21.5 48-48l0-176-192 0 0 224z"]};var h6={prefix:"fas",iconName:"chart-bar",icon:[512,512,["bar-chart"],"f080","M32 32c17.7 0 32 14.3 32 32l0 336c0 8.8 7.2 16 16 16l400 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L80 480c-44.2 0-80-35.8-80-80L0 64C0 46.3 14.3 32 32 32zm96 96c0-17.7 14.3-32 32-32l192 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-192 0c-17.7 0-32-14.3-32-32zm32 64l128 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32zm0 96l256 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-256 0c-17.7 0-32-14.3-32-32s14.3-32 32-32z"]};var u6={prefix:"fas",iconName:"image",icon:[512,512,[],"f03e","M0 96C0 60.7 28.7 32 64 32l384 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zM323.8 202.5c-4.5-6.6-11.9-10.5-19.8-10.5s-15.4 3.9-19.8 10.5l-87 127.6L170.7 297c-4.6-5.7-11.5-9-18.7-9s-14.2 3.3-18.7 9l-64 80c-5.8 7.2-6.9 17.1-2.9 25.4s12.4 13.6 21.6 13.6l96 0 32 0 208 0c8.9 0 17.1-4.9 21.2-12.8s3.6-17.4-1.4-24.7l-120-176zM112 192a48 48 0 1 0 0-96 48 48 0 1 0 0 96z"]};var ue={prefix:"fas",iconName:"table-columns",icon:[512,512,["columns"],"f0db","M0 96C0 60.7 28.7 32 64 32l384 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zm64 64l0 256 160 0 0-256L64 160zm384 0l-160 0 0 256 160 0 0-256z"]},p6=ue;var pe={prefix:"fas",iconName:"circle-play",icon:[512,512,[61469,"play-circle"],"f144","M0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM188.3 147.1c-7.6 4.2-12.3 12.3-12.3 20.9l0 176c0 8.7 4.7 16.7 12.3 20.9s16.8 4.1 24.3-.5l144-88c7.1-4.4 11.5-12.1 11.5-20.5s-4.4-16.1-11.5-20.5l-144-88c-7.4-4.5-16.7-4.7-24.3-.5z"]},L6=pe;var Le={prefix:"fas",iconName:"circle-check",icon:[512,512,[61533,"check-circle"],"f058","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"]},d6=Le;var M6={prefix:"fas",iconName:"file-import",icon:[512,512,["arrow-right-to-file"],"f56f","M128 64c0-35.3 28.7-64 64-64L352 0l0 128c0 17.7 14.3 32 32 32l128 0 0 288c0 35.3-28.7 64-64 64l-256 0c-35.3 0-64-28.7-64-64l0-112 174.1 0-39 39c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l39 39L128 288l0-224zm0 224l0 48L24 336c-13.3 0-24-10.7-24-24s10.7-24 24-24l104 0zM512 128l-128 0L384 0 512 128z"]};var C6={prefix:"fas",iconName:"palette",icon:[512,512,[127912],"f53f","M512 256c0 .9 0 1.8 0 2.7c-.4 36.5-33.6 61.3-70.1 61.3L344 320c-26.5 0-48 21.5-48 48c0 3.4 .4 6.7 1 9.9c2.1 10.2 6.5 20 10.8 29.9c6.1 13.8 12.1 27.5 12.1 42c0 31.8-21.6 60.7-53.4 62c-3.5 .1-7 .2-10.6 .2C114.6 512 0 397.4 0 256S114.6 0 256 0S512 114.6 512 256zM128 288a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm0-96a32 32 0 1 0 0-64 32 32 0 1 0 0 64zM288 96a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm96 96a32 32 0 1 0 0-64 32 32 0 1 0 0 64z"]};var de={prefix:"fas",iconName:"arrow-down-wide-short",icon:[576,512,["sort-amount-asc","sort-amount-down"],"f160","M151.6 469.6C145.5 476.2 137 480 128 480s-17.5-3.8-23.6-10.4l-88-96c-11.9-13-11.1-33.3 2-45.2s33.3-11.1 45.2 2L96 365.7 96 64c0-17.7 14.3-32 32-32s32 14.3 32 32l0 301.7 32.4-35.4c11.9-13 32.2-13.9 45.2-2s13.9 32.2 2 45.2l-88 96zM320 480c-17.7 0-32-14.3-32-32s14.3-32 32-32l32 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-32 0zm0-128c-17.7 0-32-14.3-32-32s14.3-32 32-32l96 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-96 0zm0-128c-17.7 0-32-14.3-32-32s14.3-32 32-32l160 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-160 0zm0-128c-17.7 0-32-14.3-32-32s14.3-32 32-32l224 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L320 96z"]};var g6=de;var v6={prefix:"fas",iconName:"envelope-open",icon:[512,512,[62135],"f2b6","M64 208.1L256 65.9 448 208.1l0 47.4L289.5 373c-9.7 7.2-21.4 11-33.5 11s-23.8-3.9-33.5-11L64 255.5l0-47.4zM256 0c-12.1 0-23.8 3.9-33.5 11L25.9 156.7C9.6 168.8 0 187.8 0 208.1L0 448c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-239.9c0-20.3-9.6-39.4-25.9-51.4L289.5 11C279.8 3.9 268.1 0 256 0z"]};var Me={prefix:"fas",iconName:"arrows-rotate",icon:[512,512,[128472,"refresh","sync"],"f021","M105.1 202.6c7.7-21.8 20.2-42.3 37.8-59.8c62.5-62.5 163.8-62.5 226.3 0L386.3 160 352 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l111.5 0c0 0 0 0 0 0l.4 0c17.7 0 32-14.3 32-32l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 35.2L414.4 97.6c-87.5-87.5-229.3-87.5-316.8 0C73.2 122 55.6 150.7 44.8 181.4c-5.9 16.7 2.9 34.9 19.5 40.8s34.9-2.9 40.8-19.5zM39 289.3c-5 1.5-9.8 4.2-13.7 8.2c-4 4-6.7 8.8-8.1 14c-.3 1.2-.6 2.5-.8 3.8c-.3 1.7-.4 3.4-.4 5.1L16 432c0 17.7 14.3 32 32 32s32-14.3 32-32l0-35.1 17.6 17.5c0 0 0 0 0 0c87.5 87.4 229.3 87.4 316.7 0c24.4-24.4 42.1-53.1 52.9-83.8c5.9-16.7-2.9-34.9-19.5-40.8s-34.9 2.9-40.8 19.5c-7.7 21.8-20.2 42.3-37.8 59.8c-62.5 62.5-163.8 62.5-226.3 0l-.1-.1L125.6 352l34.4 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L48.4 288c-1.6 0-3.2 .1-4.8 .3s-3.1 .5-4.6 1z"]},x6=Me;var Ce={prefix:"fas",iconName:"shield-halved",icon:[512,512,["shield-alt"],"f3ed","M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0zm0 66.8l0 378.1C394 378 431.1 230.1 432 141.4L256 66.8s0 0 0 0z"]},b6=Ce;var ge={prefix:"fas",iconName:"rectangle-ad",icon:[576,512,["ad"],"f641","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l448 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zM229.5 173.3l72 144c5.9 11.9 1.1 26.3-10.7 32.2s-26.3 1.1-32.2-10.7L253.2 328l-90.3 0-5.4 10.7c-5.9 11.9-20.3 16.7-32.2 10.7s-16.7-20.3-10.7-32.2l72-144c4.1-8.1 12.4-13.3 21.5-13.3s17.4 5.1 21.5 13.3zM208 237.7L186.8 280l42.3 0L208 237.7zM392 256a24 24 0 1 0 0 48 24 24 0 1 0 0-48zm24-43.9l0-28.1c0-13.3 10.7-24 24-24s24 10.7 24 24l0 96 0 48c0 13.3-10.7 24-24 24c-6.6 0-12.6-2.7-17-7c-9.4 4.5-19.9 7-31 7c-39.8 0-72-32.2-72-72s32.2-72 72-72c8.4 0 16.5 1.4 24 4.1z"]},N6=ge;var y6={prefix:"fas",iconName:"sort",icon:[320,512,["unsorted"],"f0dc","M137.4 41.4c12.5-12.5 32.8-12.5 45.3 0l128 128c9.2 9.2 11.9 22.9 6.9 34.9s-16.6 19.8-29.6 19.8L32 224c-12.9 0-24.6-7.8-29.6-19.8s-2.2-25.7 6.9-34.9l128-128zm0 429.3l-128-128c-9.2-9.2-11.9-22.9-6.9-34.9s16.6-19.8 29.6-19.8l256 0c12.9 0 24.6 7.8 29.6 19.8s2.2 25.7-6.9 34.9l-128 128c-12.5 12.5-32.8 12.5-45.3 0z"]};var S6={prefix:"fas",iconName:"language",icon:[640,512,[],"f1ab","M0 128C0 92.7 28.7 64 64 64l192 0 48 0 16 0 256 0c35.3 0 64 28.7 64 64l0 256c0 35.3-28.7 64-64 64l-256 0-16 0-48 0L64 448c-35.3 0-64-28.7-64-64L0 128zm320 0l0 256 256 0 0-256-256 0zM178.3 175.9c-3.2-7.2-10.4-11.9-18.3-11.9s-15.1 4.7-18.3 11.9l-64 144c-4.5 10.1 .1 21.9 10.2 26.4s21.9-.1 26.4-10.2l8.9-20.1 73.6 0 8.9 20.1c4.5 10.1 16.3 14.6 26.4 10.2s14.6-16.3 10.2-26.4l-64-144zM160 233.2L179 276l-38 0 19-42.8zM448 164c11 0 20 9 20 20l0 4 44 0 16 0c11 0 20 9 20 20s-9 20-20 20l-2 0-1.6 4.5c-8.9 24.4-22.4 46.6-39.6 65.4c.9 .6 1.8 1.1 2.7 1.6l18.9 11.3c9.5 5.7 12.5 18 6.9 27.4s-18 12.5-27.4 6.9l-18.9-11.3c-4.5-2.7-8.8-5.5-13.1-8.5c-10.6 7.5-21.9 14-34 19.4l-3.6 1.6c-10.1 4.5-21.9-.1-26.4-10.2s.1-21.9 10.2-26.4l3.6-1.6c6.4-2.9 12.6-6.1 18.5-9.8l-12.2-12.2c-7.8-7.8-7.8-20.5 0-28.3s20.5-7.8 28.3 0l14.6 14.6 .5 .5c12.4-13.1 22.5-28.3 29.8-45L448 228l-72 0c-11 0-20-9-20-20s9-20 20-20l52 0 0-4c0-11 9-20 20-20z"]};var w6={prefix:"fas",iconName:"filter",icon:[512,512,[],"f0b0","M3.9 54.9C10.5 40.9 24.5 32 40 32l432 0c15.5 0 29.5 8.9 36.1 22.9s4.6 30.5-5.2 42.5L320 320.9 320 448c0 12.1-6.8 23.2-17.7 28.6s-23.8 4.3-33.5-3l-64-48c-8.1-6-12.8-15.5-12.8-25.6l0-79.1L9 97.3C-.7 85.4-2.8 68.8 3.9 54.9z"]};var ve={prefix:"fas",iconName:"up-down-left-right",icon:[512,512,["arrows-alt"],"f0b2","M278.6 9.4c-12.5-12.5-32.8-12.5-45.3 0l-64 64c-9.2 9.2-11.9 22.9-6.9 34.9s16.6 19.8 29.6 19.8l32 0 0 96-96 0 0-32c0-12.9-7.8-24.6-19.8-29.6s-25.7-2.2-34.9 6.9l-64 64c-12.5 12.5-12.5 32.8 0 45.3l64 64c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6l0-32 96 0 0 96-32 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l64 64c12.5 12.5 32.8 12.5 45.3 0l64-64c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8l-32 0 0-96 96 0 0 32c0 12.9 7.8 24.6 19.8 29.6s25.7 2.2 34.9-6.9l64-64c12.5-12.5 12.5-32.8 0-45.3l-64-64c-9.2-9.2-22.9-11.9-34.9-6.9s-19.8 16.6-19.8 29.6l0 32-96 0 0-96 32 0c12.9 0 24.6-7.8 29.6-19.8s2.2-25.7-6.9-34.9l-64-64z"]},A6=ve;var V6={prefix:"fas",iconName:"puzzle-piece",icon:[512,512,[129513],"f12e","M192 104.8c0-9.2-5.8-17.3-13.2-22.8C167.2 73.3 160 61.3 160 48c0-26.5 28.7-48 64-48s64 21.5 64 48c0 13.3-7.2 25.3-18.8 34c-7.4 5.5-13.2 13.6-13.2 22.8c0 12.8 10.4 23.2 23.2 23.2l56.8 0c26.5 0 48 21.5 48 48l0 56.8c0 12.8 10.4 23.2 23.2 23.2c9.2 0 17.3-5.8 22.8-13.2c8.7-11.6 20.7-18.8 34-18.8c26.5 0 48 28.7 48 64s-21.5 64-48 64c-13.3 0-25.3-7.2-34-18.8c-5.5-7.4-13.6-13.2-22.8-13.2c-12.8 0-23.2 10.4-23.2 23.2L384 464c0 26.5-21.5 48-48 48l-56.8 0c-12.8 0-23.2-10.4-23.2-23.2c0-9.2 5.8-17.3 13.2-22.8c11.6-8.7 18.8-20.7 18.8-34c0-26.5-28.7-48-64-48s-64 21.5-64 48c0 13.3 7.2 25.3 18.8 34c7.4 5.5 13.2 13.6 13.2 22.8c0 12.8-10.4 23.2-23.2 23.2L48 512c-26.5 0-48-21.5-48-48L0 343.2C0 330.4 10.4 320 23.2 320c9.2 0 17.3 5.8 22.8 13.2C54.7 344.8 66.7 352 80 352c26.5 0 48-28.7 48-64s-21.5-64-48-64c-13.3 0-25.3 7.2-34 18.8C40.5 250.2 32.4 256 23.2 256C10.4 256 0 245.6 0 232.8L0 176c0-26.5 21.5-48 48-48l120.8 0c12.8 0 23.2-10.4 23.2-23.2z"]};var H6={prefix:"fas",iconName:"code",icon:[640,512,[],"f121","M392.8 1.2c-17-4.9-34.7 5-39.6 22l-128 448c-4.9 17 5 34.7 22 39.6s34.7-5 39.6-22l128-448c4.9-17-5-34.7-22-39.6zm80.6 120.1c-12.5 12.5-12.5 32.8 0 45.3L562.7 256l-89.4 89.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l112-112c12.5-12.5 12.5-32.8 0-45.3l-112-112c-12.5-12.5-32.8-12.5-45.3 0zm-306.7 0c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3l112 112c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256l89.4-89.4c12.5-12.5 12.5-32.8 0-45.3z"]};var k6={prefix:"fas",iconName:"chart-pie",icon:[576,512,["pie-chart"],"f200","M304 240l0-223.4c0-9 7-16.6 16-16.6C443.7 0 544 100.3 544 224c0 9-7.6 16-16.6 16L304 240zM32 272C32 150.7 122.1 50.3 239 34.3c9.2-1.3 17 6.1 17 15.4L256 288 412.5 444.5c6.7 6.7 6.2 17.7-1.5 23.1C371.8 495.6 323.8 512 272 512C139.5 512 32 404.6 32 272zm526.4 16c9.3 0 16.6 7.8 15.4 17c-7.7 55.9-34.6 105.6-73.9 142.3c-6 5.6-15.4 5.2-21.2-.7L320 288l238.4 0z"]};var D6={prefix:"fas",iconName:"chart-line",icon:[512,512,["line-chart"],"f201","M64 64c0-17.7-14.3-32-32-32S0 46.3 0 64L0 400c0 44.2 35.8 80 80 80l400 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 416c-8.8 0-16-7.2-16-16L64 64zm406.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L320 210.7l-57.4-57.4c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L240 221.3l57.4 57.4c12.5 12.5 32.8 12.5 45.3 0l128-128z"]};var _6={prefix:"fas",iconName:"arrow-right",icon:[448,512,[8594],"f061","M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"]};var xe={prefix:"fas",iconName:"screwdriver-wrench",icon:[512,512,["tools"],"f7d9","M78.6 5C69.1-2.4 55.6-1.5 47 7L7 47c-8.5 8.5-9.4 22-2.1 31.6l80 104c4.5 5.9 11.6 9.4 19 9.4l54.1 0 109 109c-14.7 29-10 65.4 14.3 89.6l112 112c12.5 12.5 32.8 12.5 45.3 0l64-64c12.5-12.5 12.5-32.8 0-45.3l-112-112c-24.2-24.2-60.6-29-89.6-14.3l-109-109 0-54.1c0-7.5-3.5-14.5-9.4-19L78.6 5zM19.9 396.1C7.2 408.8 0 426.1 0 444.1C0 481.6 30.4 512 67.9 512c18 0 35.3-7.2 48-19.9L233.7 374.3c-7.8-20.9-9-43.6-3.6-65.1l-61.7-61.7L19.9 396.1zM512 144c0-10.5-1.1-20.7-3.2-30.5c-2.4-11.2-16.1-14.1-24.2-6l-63.9 63.9c-3 3-7.1 4.7-11.3 4.7L352 176c-8.8 0-16-7.2-16-16l0-57.4c0-4.2 1.7-8.3 4.7-11.3l63.9-63.9c8.1-8.1 5.2-21.8-6-24.2C388.7 1.1 378.5 0 368 0C288.5 0 224 64.5 224 144l0 .8 85.3 85.3c36-9.1 75.8 .5 104 28.7L429 274.5c49-23 83-72.8 83-130.5zM56 432a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"]},F6=xe;var P6={prefix:"fas",iconName:"heart",icon:[512,512,[128153,128154,128155,128156,128420,129293,129294,129505,9829,10084,61578],"f004","M47.6 300.4L228.3 469.1c7.5 7 17.4 10.9 27.7 10.9s20.2-3.9 27.7-10.9L464.4 300.4c30.4-28.3 47.6-68 47.6-109.5v-5.8c0-69.9-50.5-129.5-119.4-141C347 36.5 300.6 51.4 268 84L256 96 244 84c-32.6-32.6-79-47.5-124.6-39.9C50.5 55.6 0 115.2 0 185.1v5.8c0 41.5 17.2 81.2 47.6 109.5z"]};var T6={prefix:"fas",iconName:"cube",icon:[512,512,[],"f1b2","M234.5 5.7c13.9-5 29.1-5 43.1 0l192 68.6C495 83.4 512 107.5 512 134.6l0 242.9c0 27-17 51.2-42.5 60.3l-192 68.6c-13.9 5-29.1 5-43.1 0l-192-68.6C17 428.6 0 404.5 0 377.4L0 134.6c0-27 17-51.2 42.5-60.3l192-68.6zM256 66L82.3 128 256 190l173.7-62L256 66zm32 368.6l160-57.1 0-188L288 246.6l0 188z"]};var E6={prefix:"fas",iconName:"circle",icon:[512,512,[128308,128309,128992,128993,128994,128995,128996,9679,9898,9899,11044,61708,61915],"f111","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512z"]};var I6={prefix:"fas",iconName:"wallet",icon:[512,512,[],"f555","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-224c0-35.3-28.7-64-64-64L80 128c-8.8 0-16-7.2-16-16s7.2-16 16-16l368 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L64 32zM416 272a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"]};var O6={prefix:"fas",iconName:"wrench",icon:[512,512,[128295],"f0ad","M352 320c88.4 0 160-71.6 160-160c0-15.3-2.2-30.1-6.2-44.2c-3.1-10.8-16.4-13.2-24.3-5.3l-76.8 76.8c-3 3-7.1 4.7-11.3 4.7L336 192c-8.8 0-16-7.2-16-16l0-57.4c0-4.2 1.7-8.3 4.7-11.3l76.8-76.8c7.9-7.9 5.4-21.2-5.3-24.3C382.1 2.2 367.3 0 352 0C263.6 0 192 71.6 192 160c0 19.1 3.4 37.5 9.5 54.5L19.9 396.1C7.2 408.8 0 426.1 0 444.1C0 481.6 30.4 512 67.9 512c18 0 35.3-7.2 48-19.9L297.5 310.5c17 6.2 35.4 9.5 54.5 9.5zM80 408a24 24 0 1 1 0 48 24 24 0 1 1 0-48z"]};var be={prefix:"fas",iconName:"circle-question",icon:[512,512,[62108,"question-circle"],"f059","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM169.8 165.3c7.9-22.3 29.1-37.3 52.8-37.3l58.3 0c34.9 0 63.1 28.3 63.1 63.1c0 22.6-12.1 43.5-31.7 54.8L280 264.4c-.2 13-10.9 23.6-24 23.6c-13.3 0-24-10.7-24-24l0-13.5c0-8.6 4.6-16.5 12.1-20.8l44.3-25.4c4.7-2.7 7.6-7.7 7.6-13.1c0-8.4-6.8-15.1-15.1-15.1l-58.3 0c-3.4 0-6.4 2.1-7.5 5.3l-.4 1.2c-4.4 12.5-18.2 19-30.6 14.6s-19-18.2-14.6-30.6l.4-1.2zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"]},R6=be;var B6={prefix:"fas",iconName:"tags",icon:[512,512,[],"f02c","M345 39.1L472.8 168.4c52.4 53 52.4 138.2 0 191.2L360.8 472.9c-9.3 9.4-24.5 9.5-33.9 .2s-9.5-24.5-.2-33.9L438.6 325.9c33.9-34.3 33.9-89.4 0-123.7L310.9 72.9c-9.3-9.4-9.2-24.6 .2-33.9s24.6-9.2 33.9 .2zM0 229.5L0 80C0 53.5 21.5 32 48 32l149.5 0c17 0 33.3 6.7 45.3 18.7l168 168c25 25 25 65.5 0 90.5L277.3 442.7c-25 25-65.5 25-90.5 0l-168-168C6.7 262.7 0 246.5 0 229.5zM144 144a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]};var q6={prefix:"fas",iconName:"eye",icon:[576,512,[128065],"f06e","M288 32c-80.8 0-145.5 36.8-192.6 80.6C48.6 156 17.3 208 2.5 243.7c-3.3 7.9-3.3 16.7 0 24.6C17.3 304 48.6 356 95.4 399.4C142.5 443.2 207.2 480 288 480s145.5-36.8 192.6-80.6c46.8-43.5 78.1-95.4 93-131.1c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C433.5 68.8 368.8 32 288 32zM144 256a144 144 0 1 1 288 0 144 144 0 1 1 -288 0zm144-64c0 35.3-28.7 64-64 64c-7.1 0-13.9-1.2-20.3-3.3c-5.5-1.8-11.9 1.6-11.7 7.4c.3 6.9 1.3 13.8 3.2 20.7c13.7 51.2 66.4 81.6 117.6 67.9s81.6-66.4 67.9-117.6c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3z"]};var U6={prefix:"fas",iconName:"pen",icon:[512,512,[128394],"f304","M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z"]};var j6={prefix:"fas",iconName:"window-maximize",icon:[512,512,[128470],"f2d0","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zM96 96l320 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L96 160c-17.7 0-32-14.3-32-32s14.3-32 32-32z"]};var Ne={prefix:"fas",iconName:"floppy-disk",icon:[448,512,[128190,128426,"save"],"f0c7","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-242.7c0-17-6.7-33.3-18.7-45.3L352 50.7C340 38.7 323.7 32 306.7 32L64 32zm0 96c0-17.7 14.3-32 32-32l192 0c17.7 0 32 14.3 32 32l0 64c0 17.7-14.3 32-32 32L96 224c-17.7 0-32-14.3-32-32l0-64zM224 288a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"]},G6=Ne;var W6={prefix:"fas",iconName:"phone",icon:[512,512,[128222,128379],"f095","M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z"]};var ye={prefix:"fas",iconName:"user-gear",icon:[640,512,["user-cog"],"f4fe","M224 0a128 128 0 1 1 0 256A128 128 0 1 1 224 0zM178.3 304l91.4 0c11.8 0 23.4 1.2 34.5 3.3c-2.1 18.5 7.4 35.6 21.8 44.8c-16.6 10.6-26.7 31.6-20 53.3c4 12.9 9.4 25.5 16.4 37.6s15.2 23.1 24.4 33c15.7 16.9 39.6 18.4 57.2 8.7l0 .9c0 9.2 2.7 18.5 7.9 26.3L29.7 512C13.3 512 0 498.7 0 482.3C0 383.8 79.8 304 178.3 304zM436 218.2c0-7 4.5-13.3 11.3-14.8c10.5-2.4 21.5-3.7 32.7-3.7s22.2 1.3 32.7 3.7c6.8 1.5 11.3 7.8 11.3 14.8l0 30.6c7.9 3.4 15.4 7.7 22.3 12.8l24.9-14.3c6.1-3.5 13.7-2.7 18.5 2.4c7.6 8.1 14.3 17.2 20.1 27.2s10.3 20.4 13.5 31c2.1 6.7-1.1 13.7-7.2 17.2l-25 14.4c.4 4 .7 8.1 .7 12.3s-.2 8.2-.7 12.3l25 14.4c6.1 3.5 9.2 10.5 7.2 17.2c-3.3 10.6-7.8 21-13.5 31s-12.5 19.1-20.1 27.2c-4.8 5.1-12.5 5.9-18.5 2.4l-24.9-14.3c-6.9 5.1-14.3 9.4-22.3 12.8l0 30.6c0 7-4.5 13.3-11.3 14.8c-10.5 2.4-21.5 3.7-32.7 3.7s-22.2-1.3-32.7-3.7c-6.8-1.5-11.3-7.8-11.3-14.8l0-30.5c-8-3.4-15.6-7.7-22.5-12.9l-24.7 14.3c-6.1 3.5-13.7 2.7-18.5-2.4c-7.6-8.1-14.3-17.2-20.1-27.2s-10.3-20.4-13.5-31c-2.1-6.7 1.1-13.7 7.2-17.2l24.8-14.3c-.4-4.1-.7-8.2-.7-12.4s.2-8.3 .7-12.4L343.8 325c-6.1-3.5-9.2-10.5-7.2-17.2c3.3-10.6 7.7-21 13.5-31s12.5-19.1 20.1-27.2c4.8-5.1 12.4-5.9 18.5-2.4l24.8 14.3c6.9-5.1 14.5-9.4 22.5-12.9l0-30.5zm92.1 133.5a48.1 48.1 0 1 0 -96.1 0 48.1 48.1 0 1 0 96.1 0z"]},Z6=ye;var $6={prefix:"fas",iconName:"trash",icon:[448,512,[],"f1f8","M135.2 17.7L128 32 32 32C14.3 32 0 46.3 0 64S14.3 96 32 96l384 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0-7.2-14.3C307.4 6.8 296.3 0 284.2 0L163.8 0c-12.1 0-23.2 6.8-28.6 17.7zM416 128L32 128 53.2 467c1.6 25.3 22.6 45 47.9 45l245.8 0c25.3 0 46.3-19.7 47.9-45L416 128z"]};var Y6={prefix:"fas",iconName:"headphones",icon:[512,512,[127911],"f025","M256 80C149.9 80 62.4 159.4 49.6 262c9.4-3.8 19.6-6 30.4-6c26.5 0 48 21.5 48 48l0 128c0 26.5-21.5 48-48 48c-44.2 0-80-35.8-80-80l0-16 0-48 0-48C0 146.6 114.6 32 256 32s256 114.6 256 256l0 48 0 48 0 16c0 44.2-35.8 80-80 80c-26.5 0-48-21.5-48-48l0-128c0-26.5 21.5-48 48-48c10.8 0 21 2.1 30.4 6C449.6 159.4 362.1 80 256 80z"]};var X6={prefix:"fas",iconName:"arrow-left",icon:[448,512,[8592],"f060","M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"]};var K6={prefix:"fas",iconName:"align-left",icon:[448,512,[],"f036","M288 64c0 17.7-14.3 32-32 32L32 96C14.3 96 0 81.7 0 64S14.3 32 32 32l224 0c17.7 0 32 14.3 32 32zm0 256c0 17.7-14.3 32-32 32L32 352c-17.7 0-32-14.3-32-32s14.3-32 32-32l224 0c17.7 0 32 14.3 32 32zM0 192c0-17.7 14.3-32 32-32l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 224c-17.7 0-32-14.3-32-32zM448 448c0 17.7-14.3 32-32 32L32 480c-17.7 0-32-14.3-32-32s14.3-32 32-32l384 0c17.7 0 32 14.3 32 32z"]};var Se={prefix:"fas",iconName:"up-right-from-square",icon:[512,512,["external-link-alt"],"f35d","M352 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9L370.7 96 201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L416 141.3l41.4 41.4c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6l0-128c0-17.7-14.3-32-32-32L352 0zM80 32C35.8 32 0 67.8 0 112L0 432c0 44.2 35.8 80 80 80l320 0c44.2 0 80-35.8 80-80l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 112c0 8.8-7.2 16-16 16L80 448c-8.8 0-16-7.2-16-16l0-320c0-8.8 7.2-16 16-16l112 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 32z"]},J6=Se;var we={prefix:"fas",iconName:"table-cells-large",icon:[512,512,["th-large"],"f009","M448 96l0 128-160 0 0-128 160 0zm0 192l0 128-160 0 0-128 160 0zM224 224L64 224 64 96l160 0 0 128zM64 288l160 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"]},Q6=we;var c8={prefix:"fas",iconName:"tag",icon:[448,512,[127991],"f02b","M0 80L0 229.5c0 17 6.7 33.3 18.7 45.3l176 176c25 25 65.5 25 90.5 0L418.7 317.3c25-25 25-65.5 0-90.5l-176-176c-12-12-28.3-18.7-45.3-18.7L48 32C21.5 32 0 53.5 0 80zm112 32a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"]},e8={prefix:"fas",iconName:"comment",icon:[512,512,[128489,61669],"f075","M512 240c0 114.9-114.6 208-256 208c-37.1 0-72.3-6.4-104.1-17.9c-11.9 8.7-31.3 20.6-54.3 30.6C73.6 471.1 44.7 480 16 480c-6.5 0-12.3-3.9-14.8-9.9c-2.5-6-1.1-12.8 3.4-17.4c0 0 0 0 0 0s0 0 0 0s0 0 0 0c0 0 0 0 0 0l.3-.3c.3-.3 .7-.7 1.3-1.4c1.1-1.2 2.8-3.1 4.9-5.7c4.1-5 9.6-12.4 15.2-21.6c10-16.6 19.5-38.4 21.4-62.9C17.7 326.8 0 285.1 0 240C0 125.1 114.6 32 256 32s256 93.1 256 208z"]};var l8={prefix:"fas",iconName:"envelope",icon:[512,512,[128386,9993,61443],"f0e0","M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48L48 64zM0 176L0 384c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-208L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z"]};var Ae={prefix:"fas",iconName:"circle-info",icon:[512,512,["info-circle"],"f05a","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336l24 0 0-64-24 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l48 0c13.3 0 24 10.7 24 24l0 88 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-80 0c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"]},s8=Ae;var a8={prefix:"fas",iconName:"check-double",icon:[448,512,[],"f560","M342.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L160 178.7l-57.4-57.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l80 80c12.5 12.5 32.8 12.5 45.3 0l160-160zm96 128c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L160 402.7 54.6 297.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l128 128c12.5 12.5 32.8 12.5 45.3 0l256-256z"]};var Ve={prefix:"fas",iconName:"arrow-rotate-left",icon:[512,512,[8634,"arrow-left-rotate","arrow-rotate-back","arrow-rotate-backward","undo"],"f0e2","M125.7 160l50.3 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L48 224c-17.7 0-32-14.3-32-32L16 64c0-17.7 14.3-32 32-32s32 14.3 32 32l0 51.2L97.6 97.6c87.5-87.5 229.3-87.5 316.8 0s87.5 229.3 0 316.8s-229.3 87.5-316.8 0c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0c62.5 62.5 163.8 62.5 226.3 0s62.5-163.8 0-226.3s-163.8-62.5-226.3 0L125.7 160z"]};var n8=Ve;var He={prefix:"fas",iconName:"gear",icon:[512,512,[9881,"cog"],"f013","M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"]},t8=He;var j3={prefix:"fas",iconName:"cart-shopping",icon:[576,512,[128722,"shopping-cart"],"f07a","M0 24C0 10.7 10.7 0 24 0L69.5 0c22 0 41.5 12.8 50.6 32l411 0c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3l-288.5 0 5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5L488 336c13.3 0 24 10.7 24 24s-10.7 24-24 24l-288.3 0c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5L24 48C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"]},i8=j3;var o8={prefix:"fas",iconName:"grip-vertical",icon:[320,512,[],"f58e","M40 352l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40zm192 0l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40zM40 320c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0zM232 192l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40zM40 160c-22.1 0-40-17.9-40-40L0 72C0 49.9 17.9 32 40 32l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0zM232 32l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40z"]};var r8={prefix:"fas",iconName:"clock",icon:[512,512,[128339,"clock-four"],"f017","M256 0a256 256 0 1 1 0 512A256 256 0 1 1 256 0zM232 120l0 136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2 280 120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"]};var f8={prefix:"fas",iconName:"coins",icon:[512,512,[],"f51e","M512 80c0 18-14.3 34.6-38.4 48c-29.1 16.1-72.5 27.5-122.3 30.9c-3.7-1.8-7.4-3.5-11.3-5C300.6 137.4 248.2 128 192 128c-8.3 0-16.4 .2-24.5 .6l-1.1-.6C142.3 114.6 128 98 128 80c0-44.2 86-80 192-80S512 35.8 512 80zM160.7 161.1c10.2-.7 20.7-1.1 31.3-1.1c62.2 0 117.4 12.3 152.5 31.4C369.3 204.9 384 221.7 384 240c0 4-.7 7.9-2.1 11.7c-4.6 13.2-17 25.3-35 35.5c0 0 0 0 0 0c-.1 .1-.3 .1-.4 .2c0 0 0 0 0 0s0 0 0 0c-.3 .2-.6 .3-.9 .5c-35 19.4-90.8 32-153.6 32c-59.6 0-112.9-11.3-148.2-29.1c-1.9-.9-3.7-1.9-5.5-2.9C14.3 274.6 0 258 0 240c0-34.8 53.4-64.5 128-75.4c10.5-1.5 21.4-2.7 32.7-3.5zM416 240c0-21.9-10.6-39.9-24.1-53.4c28.3-4.4 54.2-11.4 76.2-20.5c16.3-6.8 31.5-15.2 43.9-25.5l0 35.4c0 19.3-16.5 37.1-43.8 50.9c-14.6 7.4-32.4 13.7-52.4 18.5c.1-1.8 .2-3.5 .2-5.3zm-32 96c0 18-14.3 34.6-38.4 48c-1.8 1-3.6 1.9-5.5 2.9C304.9 404.7 251.6 416 192 416c-62.8 0-118.6-12.6-153.6-32C14.3 370.6 0 354 0 336l0-35.4c12.5 10.3 27.6 18.7 43.9 25.5C83.4 342.6 135.8 352 192 352s108.6-9.4 148.1-25.9c7.8-3.2 15.3-6.9 22.4-10.9c6.1-3.4 11.8-7.2 17.2-11.2c1.5-1.1 2.9-2.3 4.3-3.4l0 3.4 0 5.7 0 26.3zm32 0l0-32 0-25.9c19-4.2 36.5-9.5 52.1-16c16.3-6.8 31.5-15.2 43.9-25.5l0 35.4c0 10.5-5 21-14.9 30.9c-16.3 16.3-45 29.7-81.3 38.4c.1-1.7 .2-3.5 .2-5.3zM192 448c56.2 0 108.6-9.4 148.1-25.9c16.3-6.8 31.5-15.2 43.9-25.5l0 35.4c0 44.2-86 80-192 80S0 476.2 0 432l0-35.4c12.5 10.3 27.6 18.7 43.9 25.5C83.4 438.6 135.8 448 192 448z"]};var m8={prefix:"fas",iconName:"ellipsis-vertical",icon:[128,512,["ellipsis-v"],"f142","M64 360a56 56 0 1 0 0 112 56 56 0 1 0 0-112zm0-160a56 56 0 1 0 0 112 56 56 0 1 0 0-112zM120 96A56 56 0 1 0 8 96a56 56 0 1 0 112 0z"]};var ke={prefix:"fas",iconName:"right-long",icon:[512,512,["long-arrow-alt-right"],"f30b","M334.5 414c8.8 3.8 19 2 26-4.6l144-136c4.8-4.5 7.5-10.8 7.5-17.4s-2.7-12.9-7.5-17.4l-144-136c-7-6.6-17.2-8.4-26-4.6s-14.5 12.5-14.5 22l0 72L32 192c-17.7 0-32 14.3-32 32l0 64c0 17.7 14.3 32 32 32l288 0 0 72c0 9.6 5.7 18.2 14.5 22z"]},z8=ke;var De={prefix:"fas",iconName:"house",icon:[576,512,[127968,63498,63500,"home","home-alt","home-lg-alt"],"f015","M575.8 255.5c0 18-15 32.1-32 32.1l-32 0 .7 160.2c0 2.7-.2 5.4-.5 8.1l0 16.2c0 22.1-17.9 40-40 40l-16 0c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1L416 512l-24 0c-22.1 0-40-17.9-40-40l0-24 0-64c0-17.7-14.3-32-32-32l-64 0c-17.7 0-32 14.3-32 32l0 64 0 24c0 22.1-17.9 40-40 40l-24 0-31.9 0c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2l-16 0c-22.1 0-40-17.9-40-40l0-112c0-.9 0-1.9 .1-2.8l0-69.7-32 0c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z"]},h8=De;var _e={prefix:"fas",iconName:"arrow-up-wide-short",icon:[576,512,["sort-amount-up"],"f161","M151.6 42.4C145.5 35.8 137 32 128 32s-17.5 3.8-23.6 10.4l-88 96c-11.9 13-11.1 33.3 2 45.2s33.3 11.1 45.2-2L96 146.3 96 448c0 17.7 14.3 32 32 32s32-14.3 32-32l0-301.7 32.4 35.4c11.9 13 32.2 13.9 45.2 2s13.9-32.2 2-45.2l-88-96zM320 480l32 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32zm0-128l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0c-17.7 0-32 14.3-32 32s14.3 32 32 32zm0-128l160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0c-17.7 0-32 14.3-32 32s14.3 32 32 32zm0-128l224 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L320 32c-17.7 0-32 14.3-32 32s14.3 32 32 32z"]},u8=_e;var p8={prefix:"fas",iconName:"upload",icon:[512,512,[],"f093","M288 109.3L288 352c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-242.7-73.4 73.4c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l128-128c12.5-12.5 32.8-12.5 45.3 0l128 128c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L288 109.3zM64 352l128 0c0 35.3 28.7 64 64 64s64-28.7 64-64l128 0c35.3 0 64 28.7 64 64l0 32c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64l0-32c0-35.3 28.7-64 64-64zM432 456a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"]};var L8={prefix:"fas",iconName:"angle-down",icon:[448,512,[8964],"f107","M201.4 374.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 306.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z"]};var G3={prefix:"fas",iconName:"ellipsis",icon:[448,512,["ellipsis-h"],"f141","M8 256a56 56 0 1 1 112 0A56 56 0 1 1 8 256zm160 0a56 56 0 1 1 112 0 56 56 0 1 1 -112 0zm216-56a56 56 0 1 1 0 112 56 56 0 1 1 0-112z"]},d8=G3;var M8={prefix:"fas",iconName:"credit-card",icon:[576,512,[128179,62083,"credit-card-alt"],"f09d","M64 32C28.7 32 0 60.7 0 96l0 32 576 0 0-32c0-35.3-28.7-64-64-64L64 32zM576 224L0 224 0 416c0 35.3 28.7 64 64 64l448 0c35.3 0 64-28.7 64-64l0-192zM112 352l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm112 16c0-8.8 7.2-16 16-16l128 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-128 0c-8.8 0-16-7.2-16-16z"]};var C8={prefix:"fas",iconName:"bell",icon:[448,512,[128276,61602],"f0f3","M224 0c-17.7 0-32 14.3-32 32l0 19.2C119 66 64 130.6 64 208l0 18.8c0 47-17.3 92.4-48.5 127.6l-7.4 8.3c-8.4 9.4-10.4 22.9-5.3 34.4S19.4 416 32 416l384 0c12.6 0 24-7.4 29.2-18.9s3.1-25-5.3-34.4l-7.4-8.3C401.3 319.2 384 273.9 384 226.8l0-18.8c0-77.4-55-142-128-156.8L256 32c0-17.7-14.3-32-32-32zm45.3 493.3c12-12 18.7-28.3 18.7-45.3l-64 0-64 0c0 17 6.7 33.3 18.7 45.3s28.3 18.7 45.3 18.7s33.3-6.7 45.3-18.7z"]};var g8={prefix:"fas",iconName:"file",icon:[384,512,[128196,128459,61462],"f15b","M0 64C0 28.7 28.7 0 64 0L224 0l0 128c0 17.7 14.3 32 32 32l128 0 0 288c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 64zm384 64l-128 0L256 0 384 128z"]};var v8={prefix:"fas",iconName:"arrow-down",icon:[384,512,[8595],"f063","M169.4 470.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 370.8 224 64c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 306.7L54.6 265.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z"]};var Fe={prefix:"fas",iconName:"gauge-high",icon:[512,512,[62461,"tachometer-alt","tachometer-alt-fast"],"f625","M0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM288 96a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM256 416c35.3 0 64-28.7 64-64c0-17.4-6.9-33.1-18.1-44.6L366 161.7c5.3-12.1-.2-26.3-12.3-31.6s-26.3 .2-31.6 12.3L257.9 288c-.6 0-1.3 0-1.9 0c-35.3 0-64 28.7-64 64s28.7 64 64 64zM176 144a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM96 288a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm352-32a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]},x8=Fe;var b8={prefix:"fas",iconName:"play",icon:[384,512,[9654],"f04b","M73 39c-14.8-9.1-33.4-9.4-48.5-.9S0 62.6 0 80L0 432c0 17.4 9.4 33.4 24.5 41.9s33.7 8.1 48.5-.9L361 297c14.3-8.7 23-24.2 23-41s-8.7-32.2-23-41L73 39z"]};var Pe={prefix:"fas",iconName:"magnifying-glass",icon:[512,512,[128269,"search"],"f002","M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"]},k2=Pe;var N8={prefix:"fas",iconName:"chevron-down",icon:[512,512,[],"f078","M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"]};var y8={prefix:"fas",iconName:"arrow-up",icon:[384,512,[8593],"f062","M214.6 41.4c-12.5-12.5-32.8-12.5-45.3 0l-160 160c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L160 141.2 160 448c0 17.7 14.3 32 32 32s32-14.3 32-32l0-306.7L329.4 246.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3l-160-160z"]};var Te={prefix:"fas",iconName:"circle-user",icon:[512,512,[62142,"user-circle"],"f2bd","M399 384.2C376.9 345.8 335.4 320 288 320l-64 0c-47.4 0-88.9 25.8-111 64.2c35.2 39.2 86.2 63.8 143 63.8s107.8-24.7 143-63.8zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zm256 16a72 72 0 1 0 0-144 72 72 0 1 0 0 144z"]},S8=Te;var Ee={prefix:"fas",iconName:"circle-half-stroke",icon:[512,512,[9680,"adjust"],"f042","M448 256c0-106-86-192-192-192l0 384c106 0 192-86 192-192zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z"]},w8=Ee;var A8={prefix:"fas",iconName:"copy",icon:[448,512,[],"f0c5","M208 0L332.1 0c12.7 0 24.9 5.1 33.9 14.1l67.9 67.9c9 9 14.1 21.2 14.1 33.9L448 336c0 26.5-21.5 48-48 48l-192 0c-26.5 0-48-21.5-48-48l0-288c0-26.5 21.5-48 48-48zM48 128l80 0 0 64-64 0 0 256 192 0 0-32 64 0 0 48c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 176c0-26.5 21.5-48 48-48z"]};var V8={prefix:"fas",iconName:"plus",icon:[448,512,[10133,61543,"add"],"2b","M256 80c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 144L48 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l144 0 0 144c0 17.7 14.3 32 32 32s32-14.3 32-32l0-144 144 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-144 0 0-144z"]};var W3={prefix:"fas",iconName:"xmark",icon:[384,512,[128473,10005,10006,10060,215,"close","multiply","remove","times"],"f00d","M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"]};var H8=W3;var k8={prefix:"fas",iconName:"rocket",icon:[512,512,[],"f135","M156.6 384.9L125.7 354c-8.5-8.5-11.5-20.8-7.7-32.2c3-8.9 7-20.5 11.8-33.8L24 288c-8.6 0-16.6-4.6-20.9-12.1s-4.2-16.7 .2-24.1l52.5-88.5c13-21.9 36.5-35.3 61.9-35.3l82.3 0c2.4-4 4.8-7.7 7.2-11.3C289.1-4.1 411.1-8.1 483.9 5.3c11.6 2.1 20.6 11.2 22.8 22.8c13.4 72.9 9.3 194.8-111.4 276.7c-3.5 2.4-7.3 4.8-11.3 7.2l0 82.3c0 25.4-13.4 49-35.3 61.9l-88.5 52.5c-7.4 4.4-16.6 4.5-24.1 .2s-12.1-12.2-12.1-20.9l0-107.2c-14.1 4.9-26.4 8.9-35.7 11.9c-11.2 3.6-23.4 .5-31.8-7.8zM384 168a40 40 0 1 0 0-80 40 40 0 1 0 0 80z"]};var D8={prefix:"fas",iconName:"angle-up",icon:[448,512,[8963],"f106","M201.4 137.4c12.5-12.5 32.8-12.5 45.3 0l160 160c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L224 205.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l160-160z"]};var _8={prefix:"fas",iconName:"chevron-left",icon:[320,512,[9001],"f053","M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"]};var F8={prefix:"fas",iconName:"chevron-right",icon:[320,512,[9002],"f054","M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"]};var Ie={prefix:"fas",iconName:"percent",icon:[384,512,[62101,62785,"percentage"],"25","M374.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-320 320c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l320-320zM128 128A64 64 0 1 0 0 128a64 64 0 1 0 128 0zM384 384a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z"]},P8=Ie;var Oe={prefix:"fas",iconName:"thumbtack",icon:[384,512,[128204,128392,"thumb-tack"],"f08d","M32 32C32 14.3 46.3 0 64 0L320 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-29.5 0 11.4 148.2c36.7 19.9 65.7 53.2 79.5 94.7l1 3c3.3 9.8 1.6 20.5-4.4 28.8s-15.7 13.3-26 13.3L32 352c-10.3 0-19.9-4.9-26-13.3s-7.7-19.1-4.4-28.8l1-3c13.8-41.5 42.8-74.8 79.5-94.7L93.5 64 64 64C46.3 64 32 49.7 32 32zM160 384l64 0 0 96c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-96z"]},T8=Oe;var Re={prefix:"fas",iconName:"rotate",icon:[512,512,[128260,"sync-alt"],"f2f1","M142.9 142.9c-17.5 17.5-30.1 38-37.8 59.8c-5.9 16.7-24.2 25.4-40.8 19.5s-25.4-24.2-19.5-40.8C55.6 150.7 73.2 122 97.6 97.6c87.2-87.2 228.3-87.5 315.8-1L455 55c6.9-6.9 17.2-8.9 26.2-5.2s14.8 12.5 14.8 22.2l0 128c0 13.3-10.7 24-24 24l-8.4 0c0 0 0 0 0 0L344 224c-9.7 0-18.5-5.8-22.2-14.8s-1.7-19.3 5.2-26.2l41.1-41.1c-62.6-61.5-163.1-61.2-225.3 1zM16 312c0-13.3 10.7-24 24-24l7.6 0 .7 0L168 288c9.7 0 18.5 5.8 22.2 14.8s1.7 19.3-5.2 26.2l-41.1 41.1c62.6 61.5 163.1 61.2 225.3-1c17.5-17.5 30.1-38 37.8-59.8c5.9-16.7 24.2-25.4 40.8-19.5s25.4 24.2 19.5 40.8c-10.8 30.6-28.4 59.3-52.9 83.8c-87.2 87.2-228.3 87.5-315.8 1L57 457c-6.9 6.9-17.2 8.9-26.2 5.2S16 449.7 16 440l0-119.6 0-.7 0-7.6z"]},E8=Re,I8={prefix:"fas",iconName:"spinner",icon:[512,512,[],"f110","M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zm0 416a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm464-48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM142.9 437A48 48 0 1 0 75 369.1 48 48 0 1 0 142.9 437zm0-294.2A48 48 0 1 0 75 75a48 48 0 1 0 67.9 67.9zM369.1 437A48 48 0 1 0 437 369.1 48 48 0 1 0 369.1 437z"]},O8={prefix:"fas",iconName:"robot",icon:[640,512,[129302],"f544","M320 0c17.7 0 32 14.3 32 32l0 64 120 0c39.8 0 72 32.2 72 72l0 272c0 39.8-32.2 72-72 72l-304 0c-39.8 0-72-32.2-72-72l0-272c0-39.8 32.2-72 72-72l120 0 0-64c0-17.7 14.3-32 32-32zM208 384c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0zM264 256a40 40 0 1 0 -80 0 40 40 0 1 0 80 0zm152 40a40 40 0 1 0 0-80 40 40 0 1 0 0 80zM48 224l16 0 0 192-16 0c-26.5 0-48-21.5-48-48l0-96c0-26.5 21.5-48 48-48zm544 0c26.5 0 48 21.5 48 48l0 96c0 26.5-21.5 48-48 48l-16 0 0-192 16 0z"]};var Be={prefix:"fas",iconName:"clock-rotate-left",icon:[512,512,["history"],"f1da","M75 75L41 41C25.9 25.9 0 36.6 0 57.9L0 168c0 13.3 10.7 24 24 24l110.1 0c21.4 0 32.1-25.9 17-41l-30.8-30.8C155 85.5 203 64 256 64c106 0 192 86 192 192s-86 192-192 192c-40.8 0-78.6-12.7-109.7-34.4c-14.5-10.1-34.4-6.6-44.6 7.9s-6.6 34.4 7.9 44.6C151.2 495 201.7 512 256 512c141.4 0 256-114.6 256-256S397.4 0 256 0C185.3 0 121.3 28.7 75 75zm181 53c-13.3 0-24 10.7-24 24l0 104c0 6.4 2.5 12.5 7 17l72 72c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-65-65 0-94.1c0-13.3-10.7-24-24-24z"]},R8=Be;var B8={prefix:"fas",iconName:"shield",icon:[512,512,[128737,"shield-blank"],"f132","M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0z"]};var q8={prefix:"fas",iconName:"pen-nib",icon:[512,512,[10001],"f5ad","M368.4 18.3L312.7 74.1 437.9 199.3l55.7-55.7c21.9-21.9 21.9-57.3 0-79.2L447.6 18.3c-21.9-21.9-57.3-21.9-79.2 0zM288 94.6l-9.2 2.8L134.7 140.6c-19.9 6-35.7 21.2-42.3 41L3.8 445.8c-3.8 11.3-1 23.9 7.3 32.4L164.7 324.7c-3-6.3-4.7-13.3-4.7-20.7c0-26.5 21.5-48 48-48s48 21.5 48 48s-21.5 48-48 48c-7.4 0-14.4-1.7-20.7-4.7L33.7 500.9c8.6 8.3 21.1 11.2 32.4 7.3l264.3-88.6c19.7-6.6 35-22.4 41-42.3l43.2-144.1 2.7-9.2L288 94.6z"]};var U8={prefix:"fas",iconName:"moon",icon:[384,512,[127769,9214],"f186","M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z"]};var j8={prefix:"fas",iconName:"hashtag",icon:[448,512,[62098],"23","M181.3 32.4c17.4 2.9 29.2 19.4 26.3 36.8L197.8 128l95.1 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3s29.2 19.4 26.3 36.8L357.8 128l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0L325.8 320l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8l9.8-58.7-95.1 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8L90.2 384 32 384c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 21.3-128L64 192c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3zM187.1 192L165.8 320l95.1 0 21.3-128-95.1 0z"]};var qe={prefix:"fas",iconName:"up-down",icon:[256,512,[8597,11021,"arrows-alt-v"],"f338","M145.6 7.7C141 2.8 134.7 0 128 0s-13 2.8-17.6 7.7l-104 112c-6.5 7-8.2 17.2-4.4 25.9S14.5 160 24 160l56 0 0 192-56 0c-9.5 0-18.2 5.7-22 14.4s-2.1 18.9 4.4 25.9l104 112c4.5 4.9 10.9 7.7 17.6 7.7s13-2.8 17.6-7.7l104-112c6.5-7 8.2-17.2 4.4-25.9s-12.5-14.4-22-14.4l-56 0 0-192 56 0c9.5 0 18.2-5.7 22-14.4s2.1-18.9-4.4-25.9l-104-112z"]},G8=qe;var W8={prefix:"fas",iconName:"calendar",icon:[448,512,[128197,128198],"f133","M96 32l0 32L48 64C21.5 64 0 85.5 0 112l0 48 448 0 0-48c0-26.5-21.5-48-48-48l-48 0 0-32c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 32L160 64l0-32c0-17.7-14.3-32-32-32S96 14.3 96 32zM448 192L0 192 0 464c0 26.5 21.5 48 48 48l352 0c26.5 0 48-21.5 48-48l0-272z"]};var Z3={prefix:"fas",iconName:"circle-plus",icon:[512,512,["plus-circle"],"f055","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM232 344l0-64-64 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l64 0 0-64c0-13.3 10.7-24 24-24s24 10.7 24 24l0 64 64 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-64 0 0 64c0 13.3-10.7 24-24 24s-24-10.7-24-24z"]},Z8=Z3;var $8={prefix:"fas",iconName:"user-plus",icon:[640,512,[],"f234","M96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM0 482.3C0 383.8 79.8 304 178.3 304l91.4 0C368.2 304 448 383.8 448 482.3c0 16.4-13.3 29.7-29.7 29.7L29.7 512C13.3 512 0 498.7 0 482.3zM504 312l0-64-64 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l64 0 0-64c0-13.3 10.7-24 24-24s24 10.7 24 24l0 64 64 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-64 0 0 64c0 13.3-10.7 24-24 24s-24-10.7-24-24z"]},Y8={prefix:"fas",iconName:"check",icon:[448,512,[10003,10004],"f00c","M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z"]};var X8={prefix:"fas",iconName:"book-open",icon:[576,512,[128214,128366],"f518","M249.6 471.5c10.8 3.8 22.4-4.1 22.4-15.5l0-377.4c0-4.2-1.6-8.4-5-11C247.4 52 202.4 32 144 32C93.5 32 46.3 45.3 18.1 56.1C6.8 60.5 0 71.7 0 83.8L0 454.1c0 11.9 12.8 20.2 24.1 16.5C55.6 460.1 105.5 448 144 448c33.9 0 79 14 105.6 23.5zm76.8 0C353 462 398.1 448 432 448c38.5 0 88.4 12.1 119.9 22.6c11.3 3.8 24.1-4.6 24.1-16.5l0-370.3c0-12.1-6.8-23.3-18.1-27.6C529.7 45.3 482.5 32 432 32c-58.4 0-103.4 20-123 35.6c-3.3 2.6-5 6.8-5 11L304 456c0 11.4 11.7 19.3 22.4 15.5z"]};var Ue={prefix:"fas",iconName:"triangle-exclamation",icon:[512,512,[9888,"exclamation-triangle","warning"],"f071","M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480L40 480c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24l0 112c0 13.3 10.7 24 24 24s24-10.7 24-24l0-112c0-13.3-10.7-24-24-24zm32 224a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]},K8=Ue;var je={prefix:"fas",iconName:"right-left",icon:[512,512,["exchange-alt"],"f362","M32 96l320 0 0-64c0-12.9 7.8-24.6 19.8-29.6s25.7-2.2 34.9 6.9l96 96c6 6 9.4 14.1 9.4 22.6s-3.4 16.6-9.4 22.6l-96 96c-9.2 9.2-22.9 11.9-34.9 6.9s-19.8-16.6-19.8-29.6l0-64L32 160c-17.7 0-32-14.3-32-32s14.3-32 32-32zM480 352c17.7 0 32 14.3 32 32s-14.3 32-32 32l-320 0 0 64c0 12.9-7.8 24.6-19.8 29.6s-25.7 2.2-34.9-6.9l-96-96c-6-6-9.4-14.1-9.4-22.6s3.4-16.6 9.4-22.6l96-96c9.2-9.2 22.9-11.9 34.9-6.9s19.8 16.6 19.8 29.6l0 64 320 0z"]},J8=je,Q8={prefix:"fas",iconName:"paper-plane",icon:[512,512,[61913],"f1d8","M498.1 5.6c10.1 7 15.4 19.1 13.5 31.2l-64 416c-1.5 9.7-7.4 18.2-16 23s-18.9 5.4-28 1.6L284 427.7l-68.5 74.1c-8.9 9.7-22.9 12.9-35.2 8.1S160 493.2 160 480l0-83.6c0-4 1.5-7.8 4.2-10.8L331.8 202.8c5.8-6.3 5.6-16-.4-22s-15.7-6.4-22-.7L106 360.8 17.7 316.6C7.1 311.3 .3 300.7 0 288.9s5.9-22.8 16.1-28.7l448-256c10.7-6.1 23.9-5.5 34 1.4z"]};var Ge={prefix:"fas",iconName:"circle-xmark",icon:[512,512,[61532,"times-circle","xmark-circle"],"f057","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM175 175c9.4-9.4 24.6-9.4 33.9 0l47 47 47-47c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9l-47 47 47 47c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0l-47-47-47 47c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l47-47-47-47c-9.4-9.4-9.4-24.6 0-33.9z"]},c5=Ge;var e5={prefix:"fas",iconName:"exclamation",icon:[128,512,[10069,10071,61738],"21","M96 64c0-17.7-14.3-32-32-32S32 46.3 32 64l0 256c0 17.7 14.3 32 32 32s32-14.3 32-32L96 64zM64 480a40 40 0 1 0 0-80 40 40 0 1 0 0 80z"]};var l5={prefix:"fas",iconName:"dollar-sign",icon:[320,512,[128178,61781,"dollar","usd"],"24","M160 0c17.7 0 32 14.3 32 32l0 35.7c1.6 .2 3.1 .4 4.7 .7c.4 .1 .7 .1 1.1 .2l48 8.8c17.4 3.2 28.9 19.9 25.7 37.2s-19.9 28.9-37.2 25.7l-47.5-8.7c-31.3-4.6-58.9-1.5-78.3 6.2s-27.2 18.3-29 28.1c-2 10.7-.5 16.7 1.2 20.4c1.8 3.9 5.5 8.3 12.8 13.2c16.3 10.7 41.3 17.7 73.7 26.3l2.9 .8c28.6 7.6 63.6 16.8 89.6 33.8c14.2 9.3 27.6 21.9 35.9 39.5c8.5 17.9 10.3 37.9 6.4 59.2c-6.9 38-33.1 63.4-65.6 76.7c-13.7 5.6-28.6 9.2-44.4 11l0 33.4c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-34.9c-.4-.1-.9-.1-1.3-.2l-.2 0s0 0 0 0c-24.4-3.8-64.5-14.3-91.5-26.3c-16.1-7.2-23.4-26.1-16.2-42.2s26.1-23.4 42.2-16.2c20.9 9.3 55.3 18.5 75.2 21.6c31.9 4.7 58.2 2 76-5.3c16.9-6.9 24.6-16.9 26.8-28.9c1.9-10.6 .4-16.7-1.3-20.4c-1.9-4-5.6-8.4-13-13.3c-16.4-10.7-41.5-17.7-74-26.3l-2.8-.7s0 0 0 0C119.4 279.3 84.4 270 58.4 253c-14.2-9.3-27.5-22-35.8-39.6c-8.4-17.9-10.1-37.9-6.1-59.2C23.7 116 52.3 91.2 84.8 78.3c13.3-5.3 27.9-8.9 43.2-11L128 32c0-17.7 14.3-32 32-32z"]};var We={prefix:"fas",iconName:"users-gear",icon:[640,512,["users-cog"],"f509","M144 160A80 80 0 1 0 144 0a80 80 0 1 0 0 160zm368 0A80 80 0 1 0 512 0a80 80 0 1 0 0 160zM0 298.7C0 310.4 9.6 320 21.3 320l213.3 0c.2 0 .4 0 .7 0c-26.6-23.5-43.3-57.8-43.3-96c0-7.6 .7-15 1.9-22.3c-13.6-6.3-28.7-9.7-44.6-9.7l-42.7 0C47.8 192 0 239.8 0 298.7zM320 320c24 0 45.9-8.8 62.7-23.3c2.5-3.7 5.2-7.3 8-10.7c2.7-3.3 5.7-6.1 9-8.3C410 262.3 416 243.9 416 224c0-53-43-96-96-96s-96 43-96 96s43 96 96 96zm65.4 60.2c-10.3-5.9-18.1-16.2-20.8-28.2l-103.2 0C187.7 352 128 411.7 128 485.3c0 14.7 11.9 26.7 26.7 26.7l300.6 0c-2.1-5.2-3.2-10.9-3.2-16.4l0-3c-1.3-.7-2.7-1.5-4-2.3l-2.6 1.5c-16.8 9.7-40.5 8-54.7-9.7c-4.5-5.6-8.6-11.5-12.4-17.6l-.1-.2-.1-.2-2.4-4.1-.1-.2-.1-.2c-3.4-6.2-6.4-12.6-9-19.3c-8.2-21.2 2.2-42.6 19-52.3l2.7-1.5c0-.8 0-1.5 0-2.3s0-1.5 0-2.3l-2.7-1.5zM533.3 192l-42.7 0c-15.9 0-31 3.5-44.6 9.7c1.3 7.2 1.9 14.7 1.9 22.3c0 17.4-3.5 33.9-9.7 49c2.5 .9 4.9 2 7.1 3.3l2.6 1.5c1.3-.8 2.6-1.6 4-2.3l0-3c0-19.4 13.3-39.1 35.8-42.6c7.9-1.2 16-1.9 24.2-1.9s16.3 .6 24.2 1.9c22.5 3.5 35.8 23.2 35.8 42.6l0 3c1.3 .7 2.7 1.5 4 2.3l2.6-1.5c16.8-9.7 40.5-8 54.7 9.7c2.3 2.8 4.5 5.8 6.6 8.7c-2.1-57.1-49-102.7-106.6-102.7zm91.3 163.9c6.3-3.6 9.5-11.1 6.8-18c-2.1-5.5-4.6-10.8-7.4-15.9l-2.3-4c-3.1-5.1-6.5-9.9-10.2-14.5c-4.6-5.7-12.7-6.7-19-3l-2.9 1.7c-9.2 5.3-20.4 4-29.6-1.3s-16.1-14.5-16.1-25.1l0-3.4c0-7.3-4.9-13.8-12.1-14.9c-6.5-1-13.1-1.5-19.9-1.5s-13.4 .5-19.9 1.5c-7.2 1.1-12.1 7.6-12.1 14.9l0 3.4c0 10.6-6.9 19.8-16.1 25.1s-20.4 6.6-29.6 1.3l-2.9-1.7c-6.3-3.6-14.4-2.6-19 3c-3.7 4.6-7.1 9.5-10.2 14.6l-2.3 3.9c-2.8 5.1-5.3 10.4-7.4 15.9c-2.6 6.8 .5 14.3 6.8 17.9l2.9 1.7c9.2 5.3 13.7 15.8 13.7 26.4s-4.5 21.1-13.7 26.4l-3 1.7c-6.3 3.6-9.5 11.1-6.8 17.9c2.1 5.5 4.6 10.7 7.4 15.8l2.4 4.1c3 5.1 6.4 9.9 10.1 14.5c4.6 5.7 12.7 6.7 19 3l2.9-1.7c9.2-5.3 20.4-4 29.6 1.3s16.1 14.5 16.1 25.1l0 3.4c0 7.3 4.9 13.8 12.1 14.9c6.5 1 13.1 1.5 19.9 1.5s13.4-.5 19.9-1.5c7.2-1.1 12.1-7.6 12.1-14.9l0-3.4c0-10.6 6.9-19.8 16.1-25.1s20.4-6.6 29.6-1.3l2.9 1.7c6.3 3.6 14.4 2.6 19-3c3.7-4.6 7.1-9.4 10.1-14.5l2.4-4.2c2.8-5.1 5.3-10.3 7.4-15.8c2.6-6.8-.5-14.3-6.8-17.9l-3-1.7c-9.2-5.3-13.7-15.8-13.7-26.4s4.5-21.1 13.7-26.4l3-1.7zM472 384a40 40 0 1 1 80 0 40 40 0 1 1 -80 0z"]},s5=We;var a5={faHeart:P6,faChevronLeft:_8,faAngleDown:L8,faAngleRight:K0,faArrowLeft:X6,faBars:O0,faList:k1,faWallet:I6,faChartLine:D6,faMoon:U8,faLanguage:S6,faHeadset:f6,faArrowRight:_6,faSyncAlt:E8,faKey:c6,faSignInAlt:o6,faEye:q6,faCheckDouble:a8,faHistory:R8,faTrash:$6,faUsers:Z0,faPlay:b8,faFilter:w6,faBan:a6,faTimesCircle:c5,faEllipsisVertical:m8,faCheckCircle:d6,faCopy:A8,faImage:u6,faGripVertical:o8,faSortAmountDown:g6,faSortAmountUp:u8,faServer:i6,faHashtag:j8,faAlignLeft:K6,faLock:j0,faHeartBroken:R0,faBookOpen:X8,faChartArea:s6,faChartBar:h6,faChartPie:k6,faCheck:Y8,faChevronDown:N8,faChevronRight:F8,faChevronUp:X0,faAngleUp:D8,faArrowUp:y8,faCircle:E6,faColumns:p6,faDollarSign:l5,faPaperPlane:Q8,faExclamation:e5,faFile:g8,faExclamationTriangle:K8,faFlag:U0,faLongArrowAltRight:z8,faMousePointer:t6,faPercentage:P8,faPlusCircle:Z8,faSearch:k2,faTable:I0,faTachometerAlt:x8,faTag:c8,faTimes:H8,faCreditCard:M8,faQuestionCircle:R6,faHandPaper:$0,faCog:t8,faUserCircle:S8,faArrowDown:v8,faUser:Q0,faUserFriends:r6,faEnvelope:l8,faRefresh:x6,faComment:e8,faThLarge:Q6,faShoppingCart:i8,faTrashCan:_0,faEdit:G0,faCartShopping:j3,faShield:B8,faCirclePlus:Z3,faPlus:V8,faUserPen:m6,faPenNib:q8,faThumbTack:T8,faPen:U6,faBell:C8,faWindowMaximize:j6,faXmark:W3,faClock:r8,faHourglassHalf:W0,faCircleExclamation:U3,faCode:H6,faPhone:W6,faFileImport:M6,faInfoCircle:s8,faArrowsAltV:G8,faRocket:k8,faShieldAlt:b6,faEllipsis:G3,faArrowsAlt:A6,faSort:y6,faHeadphones:Y6,faSpinner:I8,faFolder:J0,faPlayCircle:L6,faAdjust:w8,faMinusCircle:P0,faCoins:f8,faExchangeAlt:J8,faMoneyBillWave:l6,faAd:N6,faCube:T6,faEllipsisH:d8,faExternalLinkAlt:J6,faPalette:C6,faPlug:Y0,faUserCog:Z6,faPuzzlePiece:V6,faUndo:n8,faLightbulb:B0,faGlobe:e6,faUserPlus:$8,faFileAlt:F0,faSearchAlt:k2,faTags:B6,faUserCheck:E0,faCalendar:W8,faComments:T0,faExclamationCircle:q0,faUpload:p8,faListAlt:k1,faThList:k1,faStar:n6,faHome:h8,faSave:G6,faEnvelopeOpen:v6,faRobot:O8,faTools:F6,faWrench:O6,faGift:z6,faUsersCog:s5};var n5={prefix:"fab",iconName:"cc-visa",icon:[576,512,[],"f1f0","M470.1 231.3s7.6 37.2 9.3 45H446c3.3-8.9 16-43.5 16-43.5-.2.3 3.3-9.1 5.3-14.9l2.8 13.4zM576 80v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V80c0-26.5 21.5-48 48-48h480c26.5 0 48 21.5 48 48zM152.5 331.2L215.7 176h-42.5l-39.3 106-4.3-21.5-14-71.4c-2.3-9.9-9.4-12.7-18.2-13.1H32.7l-.7 3.1c15.8 4 29.9 9.8 42.2 17.1l35.8 135h42.5zm94.4.2L272.1 176h-40.2l-25.1 155.4h40.1zm139.9-50.8c.2-17.7-10.6-31.2-33.7-42.3-14.1-7.1-22.7-11.9-22.7-19.2.2-6.6 7.3-13.4 23.1-13.4 13.1-.3 22.7 2.8 29.9 5.9l3.6 1.7 5.5-33.6c-7.9-3.1-20.5-6.6-36-6.6-39.7 0-67.6 21.2-67.8 51.4-.3 22.3 20 34.7 35.2 42.2 15.5 7.6 20.8 12.6 20.8 19.3-.2 10.4-12.6 15.2-24.1 15.2-16 0-24.6-2.5-37.7-8.3l-5.3-2.5-5.6 34.9c9.4 4.3 26.8 8.1 44.8 8.3 42.2.1 69.7-20.8 70-53zM528 331.4L495.6 176h-31.1c-9.6 0-16.9 2.8-21 12.9l-59.7 142.5H426s6.9-19.2 8.4-23.3H486c1.2 5.5 4.8 23.3 4.8 23.3H528z"]};var t5={prefix:"fab",iconName:"google",icon:[488,512,[],"f1a0","M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"]};var i5={prefix:"fab",iconName:"creative-commons",icon:[496,512,[],"f25e","M245.83 214.87l-33.22 17.28c-9.43-19.58-25.24-19.93-27.46-19.93-22.13 0-33.22 14.61-33.22 43.84 0 23.57 9.21 43.84 33.22 43.84 14.47 0 24.65-7.09 30.57-21.26l30.55 15.5c-6.17 11.51-25.69 38.98-65.1 38.98-22.6 0-73.96-10.32-73.96-77.05 0-58.69 43-77.06 72.63-77.06 30.72-.01 52.7 11.95 65.99 35.86zm143.05 0l-32.78 17.28c-9.5-19.77-25.72-19.93-27.9-19.93-22.14 0-33.22 14.61-33.22 43.84 0 23.55 9.23 43.84 33.22 43.84 14.45 0 24.65-7.09 30.54-21.26l31 15.5c-2.1 3.75-21.39 38.98-65.09 38.98-22.69 0-73.96-9.87-73.96-77.05 0-58.67 42.97-77.06 72.63-77.06 30.71-.01 52.58 11.95 65.56 35.86zM247.56 8.05C104.74 8.05 0 123.11 0 256.05c0 138.49 113.6 248 247.56 248 129.93 0 248.44-100.87 248.44-248 0-137.87-106.62-248-248.44-248zm.87 450.81c-112.54 0-203.7-93.04-203.7-202.81 0-105.42 85.43-203.27 203.72-203.27 112.53 0 202.82 89.46 202.82 203.26-.01 121.69-99.68 202.82-202.84 202.82z"]};var o5={prefix:"fab",iconName:"weixin",icon:[576,512,[],"f1d7","M385.2 167.6c6.4 0 12.6.3 18.8 1.1C387.4 90.3 303.3 32 207.7 32 100.5 32 13 104.8 13 197.4c0 53.4 29.3 97.5 77.9 131.6l-19.3 58.6 68-34.1c24.4 4.8 43.8 9.7 68.2 9.7 6.2 0 12.1-.3 18.3-.8-4-12.9-6.2-26.6-6.2-40.8-.1-84.9 72.9-154 165.3-154zm-104.5-52.9c14.5 0 24.2 9.7 24.2 24.4 0 14.5-9.7 24.2-24.2 24.2-14.8 0-29.3-9.7-29.3-24.2.1-14.7 14.6-24.4 29.3-24.4zm-136.4 48.6c-14.5 0-29.3-9.7-29.3-24.2 0-14.8 14.8-24.4 29.3-24.4 14.8 0 24.4 9.7 24.4 24.4 0 14.6-9.6 24.2-24.4 24.2zM563 319.4c0-77.9-77.9-141.3-165.4-141.3-92.7 0-165.4 63.4-165.4 141.3S305 460.7 397.6 460.7c19.3 0 38.9-5.1 58.6-9.9l53.4 29.3-14.8-48.6C534 402.1 563 363.2 563 319.4zm-219.1-24.5c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.8 0 24.4 9.7 24.4 19.3 0 10-9.7 19.6-24.4 19.6zm107.1 0c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.5 0 24.4 9.7 24.4 19.3.1 10-9.9 19.6-24.4 19.6z"]};var r5={prefix:"fab",iconName:"app-store",icon:[512,512,[],"f36f","M255.9 120.9l9.1-15.7c5.6-9.8 18.1-13.1 27.9-7.5 9.8 5.6 13.1 18.1 7.5 27.9l-87.5 151.5h63.3c20.5 0 32 24.1 23.1 40.8H113.8c-11.3 0-20.4-9.1-20.4-20.4 0-11.3 9.1-20.4 20.4-20.4h52l66.6-115.4-20.8-36.1c-5.6-9.8-2.3-22.2 7.5-27.9 9.8-5.6 22.2-2.3 27.9 7.5l8.9 15.7zm-78.7 218l-19.6 34c-5.6 9.8-18.1 13.1-27.9 7.5-9.8-5.6-13.1-18.1-7.5-27.9l14.6-25.2c16.4-5.1 29.8-1.2 40.4 11.6zm168.9-61.7h53.1c11.3 0 20.4 9.1 20.4 20.4 0 11.3-9.1 20.4-20.4 20.4h-29.5l19.9 34.5c5.6 9.8 2.3 22.2-7.5 27.9-9.8 5.6-22.2 2.3-27.9-7.5-33.5-58.1-58.7-101.6-75.4-130.6-17.1-29.5-4.9-59.1 7.2-69.1 13.4 23 33.4 57.7 60.1 104zM256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm216 248c0 118.7-96.1 216-216 216-118.7 0-216-96.1-216-216 0-118.7 96.1-216 216-216 118.7 0 216 96.1 216 216z"]},f5={prefix:"fab",iconName:"cc-mastercard",icon:[576,512,[],"f1f1","M482.9 410.3c0 6.8-4.6 11.7-11.2 11.7-6.8 0-11.2-5.2-11.2-11.7 0-6.5 4.4-11.7 11.2-11.7 6.6 0 11.2 5.2 11.2 11.7zm-310.8-11.7c-7.1 0-11.2 5.2-11.2 11.7 0 6.5 4.1 11.7 11.2 11.7 6.5 0 10.9-4.9 10.9-11.7-.1-6.5-4.4-11.7-10.9-11.7zm117.5-.3c-5.4 0-8.7 3.5-9.5 8.7h19.1c-.9-5.7-4.4-8.7-9.6-8.7zm107.8.3c-6.8 0-10.9 5.2-10.9 11.7 0 6.5 4.1 11.7 10.9 11.7 6.8 0 11.2-4.9 11.2-11.7 0-6.5-4.4-11.7-11.2-11.7zm105.9 26.1c0 .3.3.5.3 1.1 0 .3-.3.5-.3 1.1-.3.3-.3.5-.5.8-.3.3-.5.5-1.1.5-.3.3-.5.3-1.1.3-.3 0-.5 0-1.1-.3-.3 0-.5-.3-.8-.5-.3-.3-.5-.5-.5-.8-.3-.5-.3-.8-.3-1.1 0-.5 0-.8.3-1.1 0-.5.3-.8.5-1.1.3-.3.5-.3.8-.5.5-.3.8-.3 1.1-.3.5 0 .8 0 1.1.3.5.3.8.3 1.1.5s.2.6.5 1.1zm-2.2 1.4c.5 0 .5-.3.8-.3.3-.3.3-.5.3-.8 0-.3 0-.5-.3-.8-.3 0-.5-.3-1.1-.3h-1.6v3.5h.8V426h.3l1.1 1.4h.8l-1.1-1.3zM576 81v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V81c0-26.5 21.5-48 48-48h480c26.5 0 48 21.5 48 48zM64 220.6c0 76.5 62.1 138.5 138.5 138.5 27.2 0 53.9-8.2 76.5-23.1-72.9-59.3-72.4-171.2 0-230.5-22.6-15-49.3-23.1-76.5-23.1-76.4-.1-138.5 62-138.5 138.2zm224 108.8c70.5-55 70.2-162.2 0-217.5-70.2 55.3-70.5 162.6 0 217.5zm-142.3 76.3c0-8.7-5.7-14.4-14.7-14.7-4.6 0-9.5 1.4-12.8 6.5-2.4-4.1-6.5-6.5-12.2-6.5-3.8 0-7.6 1.4-10.6 5.4V392h-8.2v36.7h8.2c0-18.9-2.5-30.2 9-30.2 10.2 0 8.2 10.2 8.2 30.2h7.9c0-18.3-2.5-30.2 9-30.2 10.2 0 8.2 10 8.2 30.2h8.2v-23zm44.9-13.7h-7.9v4.4c-2.7-3.3-6.5-5.4-11.7-5.4-10.3 0-18.2 8.2-18.2 19.3 0 11.2 7.9 19.3 18.2 19.3 5.2 0 9-1.9 11.7-5.4v4.6h7.9V392zm40.5 25.6c0-15-22.9-8.2-22.9-15.2 0-5.7 11.9-4.8 18.5-1.1l3.3-6.5c-9.4-6.1-30.2-6-30.2 8.2 0 14.3 22.9 8.3 22.9 15 0 6.3-13.5 5.8-20.7.8l-3.5 6.3c11.2 7.6 32.6 6 32.6-7.5zm35.4 9.3l-2.2-6.8c-3.8 2.1-12.2 4.4-12.2-4.1v-16.6h13.1V392h-13.1v-11.2h-8.2V392h-7.6v7.3h7.6V416c0 17.6 17.3 14.4 22.6 10.9zm13.3-13.4h27.5c0-16.2-7.4-22.6-17.4-22.6-10.6 0-18.2 7.9-18.2 19.3 0 20.5 22.6 23.9 33.8 14.2l-3.8-6c-7.8 6.4-19.6 5.8-21.9-4.9zm59.1-21.5c-4.6-2-11.6-1.8-15.2 4.4V392h-8.2v36.7h8.2V408c0-11.6 9.5-10.1 12.8-8.4l2.4-7.6zm10.6 18.3c0-11.4 11.6-15.1 20.7-8.4l3.8-6.5c-11.6-9.1-32.7-4.1-32.7 15 0 19.8 22.4 23.8 32.7 15l-3.8-6.5c-9.2 6.5-20.7 2.6-20.7-8.6zm66.7-18.3H408v4.4c-8.3-11-29.9-4.8-29.9 13.9 0 19.2 22.4 24.7 29.9 13.9v4.6h8.2V392zm33.7 0c-2.4-1.2-11-2.9-15.2 4.4V392h-7.9v36.7h7.9V408c0-11 9-10.3 12.8-8.4l2.4-7.6zm40.3-14.9h-7.9v19.3c-8.2-10.9-29.9-5.1-29.9 13.9 0 19.4 22.5 24.6 29.9 13.9v4.6h7.9v-51.7zm7.6-75.1v4.6h.8V302h1.9v-.8h-4.6v.8h1.9zm6.6 123.8c0-.5 0-1.1-.3-1.6-.3-.3-.5-.8-.8-1.1-.3-.3-.8-.5-1.1-.8-.5 0-1.1-.3-1.6-.3-.3 0-.8.3-1.4.3-.5.3-.8.5-1.1.8-.5.3-.8.8-.8 1.1-.3.5-.3 1.1-.3 1.6 0 .3 0 .8.3 1.4 0 .3.3.8.8 1.1.3.3.5.5 1.1.8.5.3 1.1.3 1.4.3.5 0 1.1 0 1.6-.3.3-.3.8-.5 1.1-.8.3-.3.5-.8.8-1.1.3-.6.3-1.1.3-1.4zm3.2-124.7h-1.4l-1.6 3.5-1.6-3.5h-1.4v5.4h.8v-4.1l1.6 3.5h1.1l1.4-3.5v4.1h1.1v-5.4zm4.4-80.5c0-76.2-62.1-138.3-138.5-138.3-27.2 0-53.9 8.2-76.5 23.1 72.1 59.3 73.2 171.5 0 230.5 22.6 15 49.5 23.1 76.5 23.1 76.4.1 138.5-61.9 138.5-138.4z"]};var m5={prefix:"fab",iconName:"skype",icon:[448,512,[],"f17e","M424.7 299.8c2.9-14 4.7-28.9 4.7-43.8 0-113.5-91.9-205.3-205.3-205.3-14.9 0-29.7 1.7-43.8 4.7C161.3 40.7 137.7 32 112 32 50.2 32 0 82.2 0 144c0 25.7 8.7 49.3 23.3 68.2-2.9 14-4.7 28.9-4.7 43.8 0 113.5 91.9 205.3 205.3 205.3 14.9 0 29.7-1.7 43.8-4.7 19 14.6 42.6 23.3 68.2 23.3 61.8 0 112-50.2 112-112 .1-25.6-8.6-49.2-23.2-68.1zm-194.6 91.5c-65.6 0-120.5-29.2-120.5-65 0-16 9-30.6 29.5-30.6 31.2 0 34.1 44.9 88.1 44.9 25.7 0 42.3-11.4 42.3-26.3 0-18.7-16-21.6-42-28-62.5-15.4-117.8-22-117.8-87.2 0-59.2 58.6-81.1 109.1-81.1 55.1 0 110.8 21.9 110.8 55.4 0 16.9-11.4 31.8-30.3 31.8-28.3 0-29.2-33.5-75-33.5-25.7 0-42 7-42 22.5 0 19.8 20.8 21.8 69.1 33 41.4 9.3 90.7 26.8 90.7 77.6 0 59.1-57.1 86.5-112 86.5z"]};var z5={prefix:"fab",iconName:"behance",icon:[576,512,[],"f1b4","M232 237.2c31.8-15.2 48.4-38.2 48.4-74 0-70.6-52.6-87.8-113.3-87.8H0v354.4h171.8c64.4 0 124.9-30.9 124.9-102.9 0-44.5-21.1-77.4-64.7-89.7zM77.9 135.9H151c28.1 0 53.4 7.9 53.4 40.5 0 30.1-19.7 42.2-47.5 42.2h-79v-82.7zm83.3 233.7H77.9V272h84.9c34.3 0 56 14.3 56 50.6 0 35.8-25.9 47-57.6 47zm358.5-240.7H376V94h143.7v34.9zM576 305.2c0-75.9-44.4-139.2-124.9-139.2-78.2 0-131.3 58.8-131.3 135.8 0 79.9 50.3 134.7 131.3 134.7 61.3 0 101-27.6 120.1-86.3H509c-6.7 21.9-34.3 33.5-55.7 33.5-41.3 0-63-24.2-63-65.3h185.1c.3-4.2.6-8.7.6-13.2zM390.4 274c2.3-33.7 24.7-54.8 58.5-54.8 35.4 0 53.2 20.8 56.2 54.8H390.4z"]},h5={prefix:"fab",iconName:"reddit",icon:[512,512,[],"f1a1","M0 256C0 114.6 114.6 0 256 0S512 114.6 512 256s-114.6 256-256 256L37.1 512c-13.7 0-20.5-16.5-10.9-26.2L75 437C28.7 390.7 0 326.7 0 256zM349.6 153.6c23.6 0 42.7-19.1 42.7-42.7s-19.1-42.7-42.7-42.7c-20.6 0-37.8 14.6-41.8 34c-34.5 3.7-61.4 33-61.4 68.4l0 .2c-37.5 1.6-71.8 12.3-99 29.1c-10.1-7.8-22.8-12.5-36.5-12.5c-33 0-59.8 26.8-59.8 59.8c0 24 14.1 44.6 34.4 54.1c2 69.4 77.6 125.2 170.6 125.2s168.7-55.9 170.6-125.3c20.2-9.6 34.1-30.2 34.1-54c0-33-26.8-59.8-59.8-59.8c-13.7 0-26.3 4.6-36.4 12.4c-27.4-17-62.1-27.7-100-29.1l0-.2c0-25.4 18.9-46.5 43.4-49.9l0 0c4.4 18.8 21.3 32.8 41.5 32.8zM177.1 246.9c16.7 0 29.5 17.6 28.5 39.3s-13.5 29.6-30.3 29.6s-31.4-8.8-30.4-30.5s15.4-38.3 32.1-38.3zm190.1 38.3c1 21.7-13.7 30.5-30.4 30.5s-29.3-7.9-30.3-29.6c-1-21.7 11.8-39.3 28.5-39.3s31.2 16.6 32.1 38.3zm-48.1 56.7c-10.3 24.6-34.6 41.9-63 41.9s-52.7-17.3-63-41.9c-1.2-2.9 .8-6.2 3.9-6.5c18.4-1.9 38.3-2.9 59.1-2.9s40.7 1 59.1 2.9c3.1 .3 5.1 3.6 3.9 6.5z"]},u5={prefix:"fab",iconName:"discord",icon:[640,512,[],"f392","M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z"]};var p5={prefix:"fab",iconName:"ebay",icon:[640,512,[],"f4f4","M606 189.5l-54.8 109.9-54.9-109.9h-37.5l10.9 20.6c-11.5-19-35.9-26-63.3-26-31.8 0-67.9 8.7-71.5 43.1h33.7c1.4-13.8 15.7-21.8 35-21.8 26 0 41 9.6 41 33v3.4c-12.7 0-28 .1-41.7.4-42.4.9-69.6 10-76.7 34.4 1-5.2 1.5-10.6 1.5-16.2 0-52.1-39.7-76.2-75.4-76.2-21.3 0-43 5.5-58.7 24.2v-80.6h-32.1v169.5c0 10.3-.6 22.9-1.1 33.1h31.5c.7-6.3 1.1-12.9 1.1-19.5 13.6 16.6 35.4 24.9 58.7 24.9 36.9 0 64.9-21.9 73.3-54.2-.5 2.8-.7 5.8-.7 9 0 24.1 21.1 45 60.6 45 26.6 0 45.8-5.7 61.9-25.5 0 6.6.3 13.3 1.1 20.2h29.8c-.7-8.2-1-17.5-1-26.8v-65.6c0-9.3-1.7-17.2-4.8-23.8l61.5 116.1-28.5 54.1h35.9L640 189.5zM243.7 313.8c-29.6 0-50.2-21.5-50.2-53.8 0-32.4 20.6-53.8 50.2-53.8 29.8 0 50.2 21.4 50.2 53.8 0 32.3-20.4 53.8-50.2 53.8zm200.9-47.3c0 30-17.9 48.4-51.6 48.4-25.1 0-35-13.4-35-25.8 0-19.1 18.1-24.4 47.2-25.3 13.1-.5 27.6-.6 39.4-.6zm-411.9 1.6h128.8v-8.5c0-51.7-33.1-75.4-78.4-75.4-56.8 0-83 30.8-83 77.6 0 42.5 25.3 74 82.5 74 31.4 0 68-11.7 74.4-46.1h-33.1c-12 35.8-87.7 36.7-91.2-21.6zm95-21.4H33.3c6.9-56.6 92.1-54.7 94.4 0z"]},L5={prefix:"fab",iconName:"amazon",icon:[448,512,[],"f270","M257.2 162.7c-48.7 1.8-169.5 15.5-169.5 117.5 0 109.5 138.3 114 183.5 43.2 6.5 10.2 35.4 37.5 45.3 46.8l56.8-56S341 288.9 341 261.4V114.3C341 89 316.5 32 228.7 32 140.7 32 94 87 94 136.3l73.5 6.8c16.3-49.5 54.2-49.5 54.2-49.5 40.7-.1 35.5 29.8 35.5 69.1zm0 86.8c0 80-84.2 68-84.2 17.2 0-47.2 50.5-56.7 84.2-57.8v40.6zm136 163.5c-7.7 10-70 67-174.5 67S34.2 408.5 9.7 379c-6.8-7.7 1-11.3 5.5-8.3C88.5 415.2 203 488.5 387.7 401c7.5-3.7 13.3 2 5.5 12zm39.8 2.2c-6.5 15.8-16 26.8-21.2 31-5.5 4.5-9.5 2.7-6.5-3.8s19.3-46.5 12.7-55c-6.5-8.3-37-4.3-48-3.2-10.8 1-13 2-14-.3-2.3-5.7 21.7-15.5 37.5-17.5 15.7-1.8 41-.8 46 5.7 3.7 5.1 0 27.1-6.5 43.1z"]};var d5={prefix:"fab",iconName:"apple",icon:[384,512,[],"f179","M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z"]};var M5={prefix:"fab",iconName:"shopify",icon:[448,512,[],"e057","M388.32,104.1a4.66,4.66,0,0,0-4.4-4c-2,0-37.23-.8-37.23-.8s-21.61-20.82-29.62-28.83V503.2L442.76,472S388.72,106.5,388.32,104.1ZM288.65,70.47a116.67,116.67,0,0,0-7.21-17.61C271,32.85,255.42,22,237,22a15,15,0,0,0-4,.4c-.4-.8-1.2-1.2-1.6-2C223.4,11.63,213,7.63,200.58,8c-24,.8-48,18-67.25,48.83-13.61,21.62-24,48.84-26.82,70.06-27.62,8.4-46.83,14.41-47.23,14.81-14,4.4-14.41,4.8-16,18-1.2,10-38,291.82-38,291.82L307.86,504V65.67a41.66,41.66,0,0,0-4.4.4S297.86,67.67,288.65,70.47ZM233.41,87.69c-16,4.8-33.63,10.4-50.84,15.61,4.8-18.82,14.41-37.63,25.62-50,4.4-4.4,10.41-9.61,17.21-12.81C232.21,54.86,233.81,74.48,233.41,87.69ZM200.58,24.44A27.49,27.49,0,0,1,215,28c-6.4,3.2-12.81,8.41-18.81,14.41-15.21,16.42-26.82,42-31.62,66.45-14.42,4.41-28.83,8.81-42,12.81C131.33,83.28,163.75,25.24,200.58,24.44ZM154.15,244.61c1.6,25.61,69.25,31.22,73.25,91.66,2.8,47.64-25.22,80.06-65.65,82.47-48.83,3.2-75.65-25.62-75.65-25.62l10.4-44s26.82,20.42,48.44,18.82c14-.8,19.22-12.41,18.81-20.42-2-33.62-57.24-31.62-60.84-86.86-3.2-46.44,27.22-93.27,94.47-97.68,26-1.6,39.23,4.81,39.23,4.81L221.4,225.39s-17.21-8-37.63-6.4C154.15,221,153.75,239.8,154.15,244.61ZM249.42,82.88c0-12-1.6-29.22-7.21-43.63,18.42,3.6,27.22,24,31.23,36.43Q262.63,78.68,249.42,82.88Z"]};var C5={prefix:"fab",iconName:"bitcoin",icon:[512,512,[],"f379","M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zm-141.651-35.33c4.937-32.999-20.191-50.739-54.55-62.573l11.146-44.702-27.213-6.781-10.851 43.524c-7.154-1.783-14.502-3.464-21.803-5.13l10.929-43.81-27.198-6.781-11.153 44.686c-5.922-1.349-11.735-2.682-17.377-4.084l.031-.14-37.53-9.37-7.239 29.062s20.191 4.627 19.765 4.913c11.022 2.751 13.014 10.044 12.68 15.825l-12.696 50.925c.76.194 1.744.473 2.829.907-.907-.225-1.876-.473-2.876-.713l-17.796 71.338c-1.349 3.348-4.767 8.37-12.471 6.464.271.395-19.78-4.937-19.78-4.937l-13.51 31.147 35.414 8.827c6.588 1.651 13.045 3.379 19.4 5.006l-11.262 45.213 27.182 6.781 11.153-44.733a1038.209 1038.209 0 0 0 21.687 5.627l-11.115 44.523 27.213 6.781 11.262-45.128c46.404 8.781 81.299 5.239 95.986-36.727 11.836-33.79-.589-53.281-25.004-65.991 17.78-4.098 31.174-15.792 34.747-39.949zm-62.177 87.179c-8.41 33.79-65.308 15.523-83.755 10.943l14.944-59.899c18.446 4.603 77.6 13.717 68.811 48.956zm8.417-87.667c-7.673 30.736-55.031 15.12-70.393 11.292l13.548-54.327c15.363 3.828 64.836 10.973 56.845 43.035z"]};var g5={prefix:"fab",iconName:"paypal",icon:[384,512,[],"f1ed","M111.4 295.9c-3.5 19.2-17.4 108.7-21.5 134-.3 1.8-1 2.5-3 2.5H12.3c-7.6 0-13.1-6.6-12.1-13.9L58.8 46.6c1.5-9.6 10.1-16.9 20-16.9 152.3 0 165.1-3.7 204 11.4 60.1 23.3 65.6 79.5 44 140.3-21.5 62.6-72.5 89.5-140.1 90.3-43.4.7-69.5-7-75.3 24.2zM357.1 152c-1.8-1.3-2.5-1.8-3 1.3-2 11.4-5.1 22.5-8.8 33.6-39.9 113.8-150.5 103.9-204.5 103.9-6.1 0-10.1 3.3-10.9 9.4-22.6 140.4-27.1 169.7-27.1 169.7-1 7.1 3.5 12.9 10.6 12.9h63.5c8.6 0 15.7-6.3 17.4-14.9.7-5.4-1.1 6.1 14.4-91.3 4.6-22 14.3-19.7 29.3-19.7 71 0 126.4-28.8 142.9-112.3 6.5-34.8 4.6-71.4-23.8-92.6z"]};var v5={prefix:"fab",iconName:"bootstrap",icon:[576,512,[],"f836","M333.5,201.4c0-22.1-15.6-34.3-43-34.3h-50.4v71.2h42.5C315.4,238.2,333.5,225,333.5,201.4z M517,188.6 c-9.5-30.9-10.9-68.8-9.8-98.1c1.1-30.5-22.7-58.5-54.7-58.5H123.7c-32.1,0-55.8,28.1-54.7,58.5c1,29.3-0.3,67.2-9.8,98.1 c-9.6,31-25.7,50.6-52.2,53.1v28.5c26.4,2.5,42.6,22.1,52.2,53.1c9.5,30.9,10.9,68.8,9.8,98.1c-1.1,30.5,22.7,58.5,54.7,58.5h328.7 c32.1,0,55.8-28.1,54.7-58.5c-1-29.3,0.3-67.2,9.8-98.1c9.6-31,25.7-50.6,52.1-53.1v-28.5C542.7,239.2,526.5,219.6,517,188.6z M300.2,375.1h-97.9V136.8h97.4c43.3,0,71.7,23.4,71.7,59.4c0,25.3-19.1,47.9-43.5,51.8v1.3c33.2,3.6,55.5,26.6,55.5,58.3 C383.4,349.7,352.1,375.1,300.2,375.1z M290.2,266.4h-50.1v78.4h52.3c34.2,0,52.3-13.7,52.3-39.5 C344.7,279.6,326.1,266.4,290.2,266.4z"]};var x5={prefix:"fab",iconName:"ethereum",icon:[320,512,[],"f42e","M311.9 260.8L160 353.6 8 260.8 160 0l151.9 260.8zM160 383.4L8 290.6 160 512l152-221.4-152 92.8z"]};var b5={prefix:"fab",iconName:"facebook-f",icon:[320,512,[],"f39e","M80 299.3V512H196V299.3h86.5l18-97.8H196V166.9c0-51.7 20.3-71.5 72.7-71.5c16.3 0 29.4 .4 37 1.2V7.9C291.4 4 256.4 0 236.2 0C129.3 0 80 50.5 80 159.4v42.1H14v97.8H80z"]};var N5={prefix:"fab",iconName:"spotify",icon:[496,512,[],"f1bc","M248 8C111.1 8 0 119.1 0 256s111.1 248 248 248 248-111.1 248-248S384.9 8 248 8zm100.7 364.9c-4.2 0-6.8-1.3-10.7-3.6-62.4-37.6-135-39.2-206.7-24.5-3.9 1-9 2.6-11.9 2.6-9.7 0-15.8-7.7-15.8-15.8 0-10.3 6.1-15.2 13.6-16.8 81.9-18.1 165.6-16.5 237 26.2 6.1 3.9 9.7 7.4 9.7 16.5s-7.1 15.4-15.2 15.4zm26.9-65.6c-5.2 0-8.7-2.3-12.3-4.2-62.5-37-155.7-51.9-238.6-29.4-4.8 1.3-7.4 2.6-11.9 2.6-10.7 0-19.4-8.7-19.4-19.4s5.2-17.8 15.5-20.7c27.8-7.8 56.2-13.6 97.8-13.6 64.9 0 127.6 16.1 177 45.5 8.1 4.8 11.3 11 11.3 19.7-.1 10.8-8.5 19.5-19.4 19.5zm31-76.2c-5.2 0-8.4-1.3-12.9-3.9-71.2-42.5-198.5-52.7-280.9-29.7-3.6 1-8.1 2.6-12.9 2.6-13.2 0-23.3-10.3-23.3-23.6 0-13.6 8.4-21.3 17.4-23.9 35.2-10.3 74.6-15.2 117.5-15.2 73 0 149.5 15.2 205.4 47.8 7.8 4.5 12.9 10.7 12.9 22.6 0 13.6-11 23.3-23.2 23.3z"]};var y5={prefix:"fab",iconName:"viber",icon:[512,512,[],"f409","M444 49.9C431.3 38.2 379.9.9 265.3.4c0 0-135.1-8.1-200.9 52.3C27.8 89.3 14.9 143 13.5 209.5c-1.4 66.5-3.1 191.1 117 224.9h.1l-.1 51.6s-.8 20.9 13 25.1c16.6 5.2 26.4-10.7 42.3-27.8 8.7-9.4 20.7-23.2 29.8-33.7 82.2 6.9 145.3-8.9 152.5-11.2 16.6-5.4 110.5-17.4 125.7-142 15.8-128.6-7.6-209.8-49.8-246.5zM457.9 287c-12.9 104-89 110.6-103 115.1-6 1.9-61.5 15.7-131.2 11.2 0 0-52 62.7-68.2 79-5.3 5.3-11.1 4.8-11-5.7 0-6.9.4-85.7.4-85.7-.1 0-.1 0 0 0-101.8-28.2-95.8-134.3-94.7-189.8 1.1-55.5 11.6-101 42.6-131.6 55.7-50.5 170.4-43 170.4-43 96.9.4 143.3 29.6 154.1 39.4 35.7 30.6 53.9 103.8 40.6 211.1zm-139-80.8c.4 8.6-12.5 9.2-12.9.6-1.1-22-11.4-32.7-32.6-33.9-8.6-.5-7.8-13.4.7-12.9 27.9 1.5 43.4 17.5 44.8 46.2zm20.3 11.3c1-42.4-25.5-75.6-75.8-79.3-8.5-.6-7.6-13.5.9-12.9 58 4.2 88.9 44.1 87.8 92.5-.1 8.6-13.1 8.2-12.9-.3zm47 13.4c.1 8.6-12.9 8.7-12.9.1-.6-81.5-54.9-125.9-120.8-126.4-8.5-.1-8.5-12.9 0-12.9 73.7.5 133 51.4 133.7 139.2zM374.9 329v.2c-10.8 19-31 40-51.8 33.3l-.2-.3c-21.1-5.9-70.8-31.5-102.2-56.5-16.2-12.8-31-27.9-42.4-42.4-10.3-12.9-20.7-28.2-30.8-46.6-21.3-38.5-26-55.7-26-55.7-6.7-20.8 14.2-41 33.3-51.8h.2c9.2-4.8 18-3.2 23.9 3.9 0 0 12.4 14.8 17.7 22.1 5 6.8 11.7 17.7 15.2 23.8 6.1 10.9 2.3 22-3.7 26.6l-12 9.6c-6.1 4.9-5.3 14-5.3 14s17.8 67.3 84.3 84.3c0 0 9.1.8 14-5.3l9.6-12c4.6-6 15.7-9.8 26.6-3.7 14.7 8.3 33.4 21.2 45.8 32.9 7 5.7 8.6 14.4 3.8 23.6z"]};var S5={prefix:"fab",iconName:"tiktok",icon:[448,512,[],"e07b","M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"]};var w5={prefix:"fab",iconName:"linkedin",icon:[448,512,[],"f08c","M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"]};var A5={prefix:"fab",iconName:"twitch",icon:[512,512,[],"f1e8","M391.17,103.47H352.54v109.7h38.63ZM285,103H246.37V212.75H285ZM120.83,0,24.31,91.42V420.58H140.14V512l96.53-91.42h77.25L487.69,256V0ZM449.07,237.75l-77.22,73.12H294.61l-67.6,64v-64H140.14V36.58H449.07Z"]};var V5={prefix:"fab",iconName:"vimeo",icon:[448,512,[],"f40a","M403.2 32H44.8C20.1 32 0 52.1 0 76.8v358.4C0 459.9 20.1 480 44.8 480h358.4c24.7 0 44.8-20.1 44.8-44.8V76.8c0-24.7-20.1-44.8-44.8-44.8zM377 180.8c-1.4 31.5-23.4 74.7-66 129.4-44 57.2-81.3 85.8-111.7 85.8-18.9 0-34.8-17.4-47.9-52.3-25.5-93.3-36.4-148-57.4-148-2.4 0-10.9 5.1-25.4 15.2l-15.2-19.6c37.3-32.8 72.9-69.2 95.2-71.2 25.2-2.4 40.7 14.8 46.5 51.7 20.7 131.2 29.9 151 67.6 91.6 13.5-21.4 20.8-37.7 21.8-48.9 3.5-33.2-25.9-30.9-45.8-22.4 15.9-52.1 46.3-77.4 91.2-76 33.3.9 49 22.5 47.1 64.7z"]};var H5={prefix:"fab",iconName:"dropbox",icon:[528,512,[],"f16b","M264.4 116.3l-132 84.3 132 84.3-132 84.3L0 284.1l132.3-84.3L0 116.3 132.3 32l132.1 84.3zM131.6 395.7l132-84.3 132 84.3-132 84.3-132-84.3zm132.8-111.6l132-84.3-132-83.6L395.7 32 528 116.3l-132.3 84.3L528 284.8l-132.3 84.3-131.3-85z"]},k5={prefix:"fab",iconName:"instagram",icon:[448,512,[],"f16d","M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"]};var D5={prefix:"fab",iconName:"facebook",icon:[512,512,[62e3],"f09a","M512 256C512 114.6 397.4 0 256 0S0 114.6 0 256C0 376 82.7 476.8 194.2 504.5V334.2H141.4V256h52.8V222.3c0-87.1 39.4-127.5 125-127.5c16.2 0 44.2 3.2 55.7 6.4V172c-6-.6-16.5-1-29.6-1c-42 0-58.2 15.9-58.2 57.2V256h83.6l-14.4 78.2H287V510.1C413.8 494.8 512 386.9 512 256h0z"]};var _5={prefix:"fab",iconName:"whatsapp",icon:[448,512,[],"f232","M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"]};var F5={prefix:"fab",iconName:"gitter",icon:[384,512,[],"f426","M66.4 322.5H16V0h50.4v322.5zM166.9 76.1h-50.4V512h50.4V76.1zm100.6 0h-50.4V512h50.4V76.1zM368 76h-50.4v247H368V76z"]};var P5={prefix:"fab",iconName:"html5",icon:[384,512,[],"f13b","M0 32l34.9 395.8L191.5 480l157.6-52.2L384 32H0zm308.2 127.9H124.4l4.1 49.4h175.6l-13.6 148.4-97.9 27v.3h-1.1l-98.7-27.3-6-75.8h47.7L138 320l53.5 14.5 53.7-14.5 6-62.2H84.3L71.5 112.2h241.1l-4.4 47.7z"]};var T5={prefix:"fab",iconName:"snapchat",icon:[512,512,[62124,"snapchat-ghost"],"f2ab","M496.926,366.6c-3.373-9.176-9.8-14.086-17.112-18.153-1.376-.806-2.641-1.451-3.72-1.947-2.182-1.128-4.414-2.22-6.634-3.373-22.8-12.09-40.609-27.341-52.959-45.42a102.889,102.889,0,0,1-9.089-16.12c-1.054-3.013-1-4.724-.248-6.287a10.221,10.221,0,0,1,2.914-3.038c3.918-2.591,7.96-5.22,10.7-6.993,4.885-3.162,8.754-5.667,11.246-7.44,9.362-6.547,15.909-13.5,20-21.278a42.371,42.371,0,0,0,2.1-35.191c-6.2-16.318-21.613-26.449-40.287-26.449a55.543,55.543,0,0,0-11.718,1.24c-1.029.224-2.059.459-************-11.16-.074-22.94-1.066-34.534-3.522-40.758-17.794-62.123-32.674-79.16A130.167,130.167,0,0,0,332.1,36.443C309.515,23.547,283.91,17,256,17S202.6,23.547,180,36.443a129.735,129.735,0,0,0-33.281,26.783c-14.88,17.038-29.152,38.44-32.673,79.161-.992,11.594-1.24,23.435-1.079,34.533-1-.26-2.021-.5-3.051-.719a55.461,55.461,0,0,0-11.717-1.24c-18.687,0-34.125,10.131-40.3,26.449a42.423,42.423,0,0,0,2.046,35.228c4.105,7.774,10.652,14.731,20.014,21.278,2.48,1.736,6.361,4.24,11.246,7.44,2.641,1.711,6.5,4.216,10.28,6.72a11.054,11.054,0,0,1,3.3,3.311c.794,1.624.818,3.373-.36,6.6a102.02,102.02,0,0,1-8.94,15.785c-12.077,17.669-29.363,32.648-51.434,44.639C32.355,348.608,20.2,352.75,15.069,366.7c-3.868,10.528-1.339,22.506,8.494,32.6a49.137,49.137,0,0,0,12.4,9.387,134.337,134.337,0,0,0,30.342,12.139,20.024,20.024,0,0,1,6.126,2.741c3.583,3.137,3.075,7.861,7.849,14.78a34.468,34.468,0,0,0,8.977,9.127c10.019,6.919,21.278,7.353,33.207,7.811,10.776.41,22.989.881,36.939,5.481,5.778,1.91,11.78,5.605,18.736,9.92C194.842,480.951,217.707,495,255.973,495s61.292-14.123,78.118-24.428c6.907-4.24,12.872-7.9,18.489-9.758,13.949-4.613,26.163-5.072,36.939-5.481,11.928-.459,23.187-.893,33.206-7.812a34.584,34.584,0,0,0,10.218-11.16c3.434-5.84,3.348-9.919,6.572-12.771a18.971,18.971,0,0,1,5.753-2.629A134.893,134.893,0,0,0,476.02,408.71a48.344,48.344,0,0,0,13.019-10.193l.124-.149C498.389,388.5,500.708,376.867,496.926,366.6Zm-34.013,18.277c-20.745,11.458-34.533,10.23-45.259,17.137-9.114,5.865-3.72,18.513-10.342,23.076-8.134,5.617-32.177-.4-63.239,9.858-25.618,8.469-41.961,32.822-88.038,32.822s-62.036-24.3-88.076-32.884c-31-10.255-55.092-4.241-63.239-9.858-6.609-4.563-1.24-17.211-10.341-23.076-10.739-6.907-24.527-5.679-45.26-17.075-13.206-7.291-5.716-11.8-1.314-13.937,75.143-36.381,87.133-92.552,87.666-96.719.645-5.046,1.364-9.014-4.191-14.148-5.369-4.96-29.189-19.7-35.8-24.316-10.937-7.638-15.748-15.264-12.2-24.638,2.48-6.485,8.531-8.928,14.879-8.928a27.643,27.643,0,0,1,5.965.67c12,2.6,23.659,8.617,30.392,10.242a10.749,10.749,0,0,0,2.48.335c3.6,0,4.86-1.811,4.612-5.927-.768-13.132-2.628-38.725-.558-62.644,2.84-32.909,13.442-49.215,26.04-63.636,6.051-6.932,34.484-36.976,88.857-36.976s82.88,29.92,88.931,36.827c12.611,14.421,23.225,30.727,26.04,63.636,2.071,23.919.285,49.525-.558,62.644-.285,4.327,1.017,5.927,4.613,5.927a10.648,10.648,0,0,0,2.48-.335c6.745-1.624,18.4-7.638,30.4-10.242a27.641,27.641,0,0,1,5.964-.67c6.386,0,12.4,2.48,14.88,8.928,3.546,9.374-1.24,17-12.189,24.639-6.609,4.612-30.429,19.343-35.8,24.315-5.568,5.134-4.836,9.1-4.191,14.149.533,4.228,12.511,60.4,87.666,96.718C468.629,373.011,476.119,377.524,462.913,384.877Z"]};var E5={prefix:"fab",iconName:"android",icon:[576,512,[],"f17b","M420.55,301.93a24,24,0,1,1,24-24,24,24,0,0,1-24,24m-265.1,0a24,24,0,1,1,24-24,24,24,0,0,1-24,24m273.7-144.48,47.94-83a10,10,0,1,0-17.27-10h0l-48.54,84.07a301.25,301.25,0,0,0-246.56,0L116.18,64.45a10,10,0,1,0-17.27,10h0l47.94,83C64.53,202.22,8.24,285.55,0,384H576c-8.24-98.45-64.54-181.78-146.85-226.55"]};var I5={prefix:"fab",iconName:"cc-amex",icon:[576,512,[],"f1f3","M0 432c0 26.5 21.5 48 48 48H528c26.5 0 48-21.5 48-48v-1.1H514.3l-31.9-35.1-31.9 35.1H246.8V267.1H181L262.7 82.4h78.6l28.1 63.2V82.4h97.2L483.5 130l17-47.6H576V80c0-26.5-21.5-48-48-48H48C21.5 32 0 53.5 0 80V432zm440.4-21.7L482.6 364l42 46.3H576l-68-72.1 68-72.1H525.4l-42 46.7-41.5-46.7H390.5L458 338.6l-67.4 71.6V377.1h-83V354.9h80.9V322.6H307.6V300.2h83V267.1h-122V410.3H440.4zm96.3-72L576 380.2V296.9l-39.3 41.4zm-36.3-92l36.9-100.6V246.3H576V103H515.8l-32.2 89.3L451.7 103H390.5V246.1L327.3 103H276.1L213.7 246.3h43l11.9-28.7h65.9l12 28.7h82.7V146L466 246.3h34.4zM282 185.4l19.5-46.9 19.4 46.9H282z"]};var O5={prefix:"fab",iconName:"github",icon:[496,512,[],"f09b","M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"]};var R5={prefix:"fab",iconName:"youtube",icon:[576,512,[61802],"f167","M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"]};var B5={prefix:"fab",iconName:"twitter",icon:[512,512,[],"f099","M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"]};var q5={prefix:"fab",iconName:"pinterest",icon:[496,512,[],"f0d2","M496 256c0 137-111 248-248 248-25.6 0-50.2-3.9-73.4-11.1 10.1-16.5 25.2-43.5 30.8-65 3-11.6 15.4-59 15.4-59 8.1 15.4 31.7 28.5 56.8 28.5 74.8 0 128.7-68.8 128.7-154.3 0-81.9-66.9-143.2-152.9-143.2-107 0-163.9 71.8-163.9 150.1 0 36.4 19.4 81.7 50.3 96.1 4.7 2.2 7.2 1.2 8.3-3.3.8-3.4 5-20.3 6.9-28.1.6-2.5.3-4.7-1.7-7.1-10.1-12.5-18.3-35.3-18.3-56.6 0-54.7 41.4-107.6 112-107.6 60.9 0 103.6 41.5 103.6 100.9 0 67.1-33.9 113.6-78 113.6-24.3 0-42.6-20.1-36.7-44.8 7-29.5 20.5-61.3 20.5-82.6 0-19-10.2-34.9-31.4-34.9-24.9 0-44.9 25.7-44.9 60.2 0 22 7.4 36.8 7.4 36.8s-24.5 103.8-29 123.2c-5 21.4-3 51.6-.9 71.2C65.4 450.9 0 361.1 0 256 0 119 111 8 248 8s248 111 248 248z"]};var U5={prefix:"fab",iconName:"telegram",icon:[496,512,[62462,"telegram-plane"],"f2c6","M248,8C111.033,8,0,119.033,0,256S111.033,504,248,504,496,392.967,496,256,384.967,8,248,8ZM362.952,176.66c-3.732,39.215-19.881,134.378-28.1,178.3-3.476,18.584-10.322,24.816-16.948,25.425-14.4,1.326-25.338-9.517-39.287-18.661-21.827-14.308-34.158-23.215-55.346-37.177-24.485-16.135-8.612-25,5.342-39.5,3.652-3.793,67.107-61.51,68.335-66.746.153-.655.3-3.1-1.154-4.384s-3.59-.849-5.135-.5q-3.283.746-104.608,69.142-14.845,10.194-26.894,9.934c-8.855-.191-25.888-5.006-38.551-9.123-15.531-5.048-27.875-7.717-26.8-16.291q.84-6.7,18.45-13.7,108.446-47.248,144.628-62.3c68.872-28.647,83.183-33.623,92.511-33.789,2.052-.034,6.639.474,9.61,2.885a10.452,10.452,0,0,1,3.53,6.716A43.765,43.765,0,0,1,362.952,176.66Z"]};var j5={prefix:"fab",iconName:"slack",icon:[448,512,[62447,"slack-hash"],"f198","M94.12 315.1c0 25.9-21.16 47.06-47.06 47.06S0 341 0 315.1c0-25.9 21.16-47.06 47.06-47.06h47.06v47.06zm23.72 0c0-25.9 21.16-47.06 47.06-47.06s47.06 21.16 47.06 47.06v117.84c0 25.9-21.16 47.06-47.06 47.06s-47.06-21.16-47.06-47.06V315.1zm47.06-188.98c-25.9 0-47.06-21.16-47.06-47.06S139 32 164.9 32s47.06 21.16 47.06 47.06v47.06H164.9zm0 23.72c25.9 0 47.06 21.16 47.06 47.06s-21.16 47.06-47.06 47.06H47.06C21.16 243.96 0 222.8 0 196.9s21.16-47.06 47.06-47.06H164.9zm188.98 47.06c0-25.9 21.16-47.06 47.06-47.06 25.9 0 47.06 21.16 47.06 47.06s-21.16 47.06-47.06 47.06h-47.06V196.9zm-23.72 0c0 25.9-21.16 47.06-47.06 47.06-25.9 0-47.06-21.16-47.06-47.06V79.06c0-25.9 21.16-47.06 47.06-47.06 25.9 0 47.06 21.16 47.06 47.06V196.9zM283.1 385.88c25.9 0 47.06 21.16 47.06 47.06 0 25.9-21.16 47.06-47.06 47.06-25.9 0-47.06-21.16-47.06-47.06v-47.06h47.06zm0-23.72c-25.9 0-47.06-21.16-47.06-47.06 0-25.9 21.16-47.06 47.06-47.06h117.84c25.9 0 47.06 21.16 47.06 47.06 0 25.9-21.16 47.06-47.06 47.06H283.1z"]};var G5={prefix:"fab",iconName:"medium",icon:[640,512,[62407,"medium-m"],"f23a","M180.5,74.262C80.813,74.262,0,155.633,0,256S80.819,437.738,180.5,437.738,361,356.373,361,256,280.191,74.262,180.5,74.262Zm288.25,10.646c-49.845,0-90.245,76.619-90.245,171.095s40.406,171.1,90.251,171.1,90.251-76.619,90.251-171.1H559C559,161.5,518.6,84.908,468.752,84.908Zm139.506,17.821c-17.526,0-31.735,68.628-31.735,153.274s14.2,153.274,31.735,153.274S640,340.631,640,256C640,171.351,625.785,102.729,608.258,102.729Z"]};var W5={faAmazon:L5,faAndroid:E5,faApple:d5,faAppStore:r5,faBehance:z5,faBitcoin:C5,faBootstrap:v5,faCcAmex:I5,faCcMastercard:f5,faCcVisa:n5,faDiscord:u5,faDropbox:H5,faEbay:p5,faEthereum:x5,faFacebook:D5,faFacebookF:b5,faGithub:O5,faGoogle:t5,faHtml5:P5,faInstagram:k5,faLinkedin:w5,faMedium:G5,faPaypal:g5,faPinterest:q5,faReddit:h5,faShopify:M5,faSkype:m5,faSlack:j5,faSnapchat:T5,faSpotify:N5,faTelegram:U5,faTiktok:S5,faTwitch:A5,faTwitter:B5,faViber:y5,faVimeo:V5,faWeixin:o5,faWhatsapp:_5,faYoutube:R5,faCreativeCommons:i5,faGitter:F5},As={accessibleIcon:"#1a75ff",accusoft:"#186f93",acquisitionsIncorporated:"#006666",adn:"#ec1b23",adobe:"#ff0000",adversal:"#1e1e1e",affiliatetheme:"#000000",airbnb:"#ff5a5f",algolia:"#5468ff",alipay:"#00a0e9",amazon:"#ff9900",amazonPay:"#ff9900",amilia:"#0088cc",android:"#3ddc84",angellist:"#000000",angrycreative:"#f96400",angular:"#dd0031",appStore:"#007aff",apple:"#000000",applePay:"#000000",artstation:"#13aff0",asymmetrik:"#0071bc",atlassian:"#0052cc",audible:"#f8991c",autoprefixer:"#ff2b2b",avianex:"#7cc576",aviato:"#f06595",aws:"#ff9900",bandcamp:"#408294",battleNet:"#00aeff",behance:"#1769ff",bilibili:"#00a1d6",bimobject:"#0066b1",bitbucket:"#0052cc",bitcoin:"#f7931a",bity:"#00c2f2",blackberry:"#000000",blogger:"#ff5722",bluetooth:"#0082fc",bootstrap:"#7952b3",bots:"#e10098",brave:"#fb542b",buffer:"#168eea",buyNLarge:"#0077c0",buysellads:"#cd2027",canadianMapleLeaf:"#ff0000",ccAmex:"#2E77BC",ccMastercard:"#EB001B",ccVisa:"#142787",centercode:"#003c9f",chrome:"#4285f4",cloudscale:"#212121",cloudsmith:"#3e82f7",cloudversify:"#2f80ed",codepen:"#000000",codiepie:"#e14329",confluence:"#172b4d",connectdevelop:"#003e54",contao:"#f47f20",cottonBureau:"#f8df00",cpanel:"#ff6c2c",creativeCommons:"#ef9421",css3:"#264de4",css3Alt:"#1572b6",cuttlefish:"#7b8b8e",dailymotion:"#0066dc",dashcube:"#2e2e2e",deezer:"#ef5466",delicious:"#39f",deploydog:"#23a2f2",deskpro:"#1e1e1e",dev:"#0a0a0a",deviantart:"#05cc47",dhl:"#ba0c2f",diaspora:"#000000",digg:"#005be2",digitalOcean:"#0080ff",discord:"#5865f2",discourse:"#000000",dochub:"#008489",docker:"#0db7ed",draft2digital:"#d3302f",dribbble:"#ea4c89",dropbox:"#0061ff",drupal:"#0678be",dyalog:"#5a3a98",earlybirds:"#ff5f00",ebay:"#e53238",edge:"#0078d7",elementor:"#92003b",ello:"#000000",empire:"#000000",envira:"#7ac143",erlang:"#a90533",ethereum:"#3c3c3d",etsy:"#f16521",evernote:"#00a82d",expeditedssl:"#343538",facebook:"#1877f2",facebookF:"#1877f2",firefox:"#ff7139",firstOrder:"#000000",flickr:"#ff0084",flipboard:"#e12828",fly:"#ed1c24",fontAwesome:"#228ae6",fonticons:"#0b0b0b",fortAwesome:"#0e2a47",foursquare:"#f94877",freeCodeCamp:"#006400",freebsd:"#ab2b28",fulcrum:"#ff2a2a",galacticRepublic:"#000000",getPocket:"#ef3f56",gg:"#171717",git:"#f34f29",github:"#181717",gitkraken:"#179287",gitlab:"#fc6d26",gitter:"#ed1965",glide:"#00b4f1",gofore:"#ff6c00",goodreads:"#372213",google:"#4285f4",googleDrive:"#34a853",googlePay:"#000000",googlePlay:"#3bccff",googlePlus:"#db4437",grunt:"#fba919",gulp:"#cf4647",hackerNews:"#ff6600",hackerrank:"#2ec866",heroku:"#430098",html5:"#e34f26",hubspot:"#ff7a59",imdb:"#f5c518",instagram:"#e1306c",intercom:"#1f8ded",internetExplorer:"#0076d6",invision:"#ff3366",itunes:"#ea4cc0",java:"#007396",jira:"#0052cc",joomla:"#f44321",js:"#f7df1e",jsfiddle:"#4679bd",kaggle:"#20beff",keybase:"#33a0ff",kickstarter:"#2bde73",korvue:"#003366",laravel:"#ff2d20",lastfm:"#d51007",less:"#1d365d",line:"#00c300",linkedin:"#0077b5",linux:"#fcc624",lyft:"#ff00bf",magento:"#ee672f",mailchimp:"#ffe01b",markdown:"#000000",mastodon:"#6364ff",medium:"#00ab6c",mendeley:"#9d1620",microblog:"#ffd300",microsoft:"#666666",mix:"#ff8126",mixcloud:"#52aad8",monero:"#ff6600",napster:"#0d1a2b",neos:"#0088cc",npm:"#cb3837",node:"#339933",nodeJs:"#339933",npmJs:"#cb3837",nintendoSwitch:"#e60012",odnoklassniki:"#ed812b",opencart:"#2ed0e4",opera:"#ff1b2d",page4:"#e90000",patreon:"#f96854",paypal:"#003087",periscope:"#40a4c4",phabricator:"#4a5f88",phoenixFramework:"#f0653c",php:"#777bb4",piedPiper:"#1f8dd6",pinterest:"#e60023",playstation:"#003791",productHunt:"#da552f",python:"#306998",quora:"#a82400",react:"#61dafb",reddit:"#ff4500",redhat:"#e00",rocketchat:"#f80061",safari:"#1b94e0",sass:"#cc6699",scala:"#dc322f",shopify:"#7AB55C",sketch:"#f7b500",skype:"#00aff0",slack:"#4a154b",snapchat:"#fffc00",soundcloud:"#ff3300",spotify:"#1db954",stackExchange:"#285e8e",stackOverflow:"#f48024",steam:"#000000",stripe:"#635bff",stumbleupon:"#eb4924",telegram:"#0088cc",tencentWeibo:"#74af2c",tiktok:"#000000",tumblr:"#35465c",twitch:"#9146ff",twitter:"#1da1f2",uber:"#09091a",ubuntu:"#e95420",uikit:"#2396f3",uniregistry:"#007fff",unity:"#222c37",unsplash:"#000000",untappd:"#ffc000",ups:"#351c15",usps:"#333399",vaadin:"#00b4f0",viber:"#665cac",vimeo:"#1ab7ea",vine:"#11b48a",vk:"#4680c2",vuejs:"#42b883",weixin:"#07C160",whatsapp:"#25d366",wikipediaW:"#000000",windows:"#00a4ef",wordpress:"#21759b",xbox:"#107c10",xing:"#026466",yahoo:"#430297",yelp:"#af0606",youtube:"#ff0000"};var _s=(()=>{let c=class c{constructor(e){e.addIconPacks(a5,W5)}};c.\u0275fac=function(s){return new(s||c)(d(q3))},c.\u0275mod=P({type:c}),c.\u0275inj=F({imports:[D0]});let l=c;return l})();var s7=(()=>{let c=class c{constructor(e,s){this._renderer=e,this._elementRef=s,this.onChange=n=>{},this.onTouched=()=>{}}setProperty(e,s){this._renderer.setProperty(this._elementRef.nativeElement,e,s)}registerOnTouched(e){this.onTouched=e}registerOnChange(e){this.onChange=e}setDisabledState(e){this.setProperty("disabled",e)}};c.\u0275fac=function(s){return new(s||c)(h(Q),h(U))},c.\u0275dir=M({type:c});let l=c;return l})(),P1=(()=>{let c=class c extends s7{};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,features:[v]});let l=c;return l})(),o1=new w(""),Ze={provide:o1,useExisting:x(()=>$e),multi:!0},$e=(()=>{let c=class c extends P1{writeValue(e){this.setProperty("checked",e)}};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(s,n){s&1&&j("change",function(i){return n.onChange(i.target.checked)})("blur",function(){return n.onTouched()})},features:[N([Ze]),v]});let l=c;return l})(),Ye={provide:o1,useExisting:x(()=>a7),multi:!0};function Xe(){let l=J2()?J2().getUserAgent():"";return/android (\d+)/.test(l.toLowerCase())}var Ke=new w(""),a7=(()=>{let c=class c extends s7{constructor(e,s,n){super(e,s),this._compositionMode=n,this._composing=!1,this._compositionMode==null&&(this._compositionMode=!Xe())}writeValue(e){let s=e==null?"":e;this.setProperty("value",s)}_handleInput(e){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(e)}_compositionStart(){this._composing=!0}_compositionEnd(e){this._composing=!1,this._compositionMode&&this.onChange(e)}};c.\u0275fac=function(s){return new(s||c)(h(Q),h(U),h(Ke,8))},c.\u0275dir=M({type:c,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(s,n){s&1&&j("input",function(i){return n._handleInput(i.target.value)})("blur",function(){return n.onTouched()})("compositionstart",function(){return n._compositionStart()})("compositionend",function(i){return n._compositionEnd(i.target.value)})},features:[N([Ye]),v]});let l=c;return l})();function t1(l){return l==null||(typeof l=="string"||Array.isArray(l))&&l.length===0}function n7(l){return l!=null&&typeof l.length=="number"}var K=new w(""),R2=new w(""),Je=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Z5=class{static min(c){return t7(c)}static max(c){return i7(c)}static required(c){return o7(c)}static requiredTrue(c){return Qe(c)}static email(c){return cl(c)}static minLength(c){return el(c)}static maxLength(c){return r7(c)}static pattern(c){return f7(c)}static nullValidator(c){return _2(c)}static compose(c){return L7(c)}static composeAsync(c){return d7(c)}};function t7(l){return c=>{if(t1(c.value)||t1(l))return null;let a=parseFloat(c.value);return!isNaN(a)&&a<l?{min:{min:l,actual:c.value}}:null}}function i7(l){return c=>{if(t1(c.value)||t1(l))return null;let a=parseFloat(c.value);return!isNaN(a)&&a>l?{max:{max:l,actual:c.value}}:null}}function o7(l){return t1(l.value)?{required:!0}:null}function Qe(l){return l.value===!0?null:{required:!0}}function cl(l){return t1(l.value)||Je.test(l.value)?null:{email:!0}}function el(l){return c=>t1(c.value)||!n7(c.value)?null:c.value.length<l?{minlength:{requiredLength:l,actualLength:c.value.length}}:null}function r7(l){return c=>n7(c.value)&&c.value.length>l?{maxlength:{requiredLength:l,actualLength:c.value.length}}:null}function f7(l){if(!l)return _2;let c,a;return typeof l=="string"?(a="",l.charAt(0)!=="^"&&(a+="^"),a+=l,l.charAt(l.length-1)!=="$"&&(a+="$"),c=new RegExp(a)):(a=l.toString(),c=l),e=>{if(t1(e.value))return null;let s=e.value;return c.test(s)?null:{pattern:{requiredPattern:a,actualValue:s}}}}function _2(l){return null}function m7(l){return l!=null}function z7(l){return z4(l)?l4(l):l}function h7(l){let c={};return l.forEach(a=>{c=a!=null?H(H({},c),a):c}),Object.keys(c).length===0?null:c}function u7(l,c){return c.map(a=>a(l))}function ll(l){return!l.validate}function p7(l){return l.map(c=>ll(c)?c:a=>c.validate(a))}function L7(l){if(!l)return null;let c=l.filter(m7);return c.length==0?null:function(a){return h7(u7(a,c))}}function K3(l){return l!=null?L7(p7(l)):null}function d7(l){if(!l)return null;let c=l.filter(m7);return c.length==0?null:function(a){let e=u7(a,c).map(z7);return o2(e).pipe(I1(h7))}}function J3(l){return l!=null?d7(p7(l)):null}function $5(l,c){return l===null?[c]:Array.isArray(l)?[...l,c]:[l,c]}function M7(l){return l._rawValidators}function C7(l){return l._rawAsyncValidators}function $3(l){return l?Array.isArray(l)?l:[l]:[]}function F2(l,c){return Array.isArray(l)?l.includes(c):l===c}function Y5(l,c){let a=$3(c);return $3(l).forEach(s=>{F2(a,s)||a.push(s)}),a}function X5(l,c){return $3(c).filter(a=>!F2(l,a))}var P2=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(c){this._rawValidators=c||[],this._composedValidatorFn=K3(this._rawValidators)}_setAsyncValidators(c){this._rawAsyncValidators=c||[],this._composedAsyncValidatorFn=J3(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(c){this._onDestroyCallbacks.push(c)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(c=>c()),this._onDestroyCallbacks=[]}reset(c=void 0){this.control&&this.control.reset(c)}hasError(c,a){return this.control?this.control.hasError(c,a):!1}getError(c,a){return this.control?this.control.getError(c,a):null}},X=class extends P2{get formDirective(){return null}get path(){return null}},i1=class extends P2{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}},T2=class{constructor(c){this._cd=c}get isTouched(){var c,a;return!!((a=(c=this._cd)==null?void 0:c.control)!=null&&a.touched)}get isUntouched(){var c,a;return!!((a=(c=this._cd)==null?void 0:c.control)!=null&&a.untouched)}get isPristine(){var c,a;return!!((a=(c=this._cd)==null?void 0:c.control)!=null&&a.pristine)}get isDirty(){var c,a;return!!((a=(c=this._cd)==null?void 0:c.control)!=null&&a.dirty)}get isValid(){var c,a;return!!((a=(c=this._cd)==null?void 0:c.control)!=null&&a.valid)}get isInvalid(){var c,a;return!!((a=(c=this._cd)==null?void 0:c.control)!=null&&a.invalid)}get isPending(){var c,a;return!!((a=(c=this._cd)==null?void 0:c.control)!=null&&a.pending)}get isSubmitted(){var c;return!!((c=this._cd)!=null&&c.submitted)}},sl={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},Ks=m1(H({},sl),{"[class.ng-submitted]":"isSubmitted"}),Js=(()=>{let c=class c extends T2{constructor(e){super(e)}};c.\u0275fac=function(s){return new(s||c)(h(i1,2))},c.\u0275dir=M({type:c,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(s,n){s&2&&m2("ng-untouched",n.isUntouched)("ng-touched",n.isTouched)("ng-pristine",n.isPristine)("ng-dirty",n.isDirty)("ng-valid",n.isValid)("ng-invalid",n.isInvalid)("ng-pending",n.isPending)},features:[v]});let l=c;return l})(),Qs=(()=>{let c=class c extends T2{constructor(e){super(e)}};c.\u0275fac=function(s){return new(s||c)(h(X,10))},c.\u0275dir=M({type:c,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(s,n){s&2&&m2("ng-untouched",n.isUntouched)("ng-touched",n.isTouched)("ng-pristine",n.isPristine)("ng-dirty",n.isDirty)("ng-valid",n.isValid)("ng-invalid",n.isInvalid)("ng-pending",n.isPending)("ng-submitted",n.isSubmitted)},features:[v]});let l=c;return l})();var l2="VALID",D2="INVALID",D1="PENDING",s2="DISABLED";function Q3(l){return(B2(l)?l.validators:l)||null}function al(l){return Array.isArray(l)?K3(l):l||null}function c4(l,c){return(B2(c)?c.asyncValidators:l)||null}function nl(l){return Array.isArray(l)?J3(l):l||null}function B2(l){return l!=null&&!Array.isArray(l)&&typeof l=="object"}function g7(l,c,a){let e=l.controls;if(!(c?Object.keys(e):e).length)throw new r2(1e3,"");if(!e[a])throw new r2(1001,"")}function v7(l,c,a){l._forEachChild((e,s)=>{if(a[s]===void 0)throw new r2(1002,"")})}var _1=class{constructor(c,a){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(c),this._assignAsyncValidators(a)}get validator(){return this._composedValidatorFn}set validator(c){this._rawValidators=this._composedValidatorFn=c}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(c){this._rawAsyncValidators=this._composedAsyncValidatorFn=c}get parent(){return this._parent}get valid(){return this.status===l2}get invalid(){return this.status===D2}get pending(){return this.status==D1}get disabled(){return this.status===s2}get enabled(){return this.status!==s2}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(c){this._assignValidators(c)}setAsyncValidators(c){this._assignAsyncValidators(c)}addValidators(c){this.setValidators(Y5(c,this._rawValidators))}addAsyncValidators(c){this.setAsyncValidators(Y5(c,this._rawAsyncValidators))}removeValidators(c){this.setValidators(X5(c,this._rawValidators))}removeAsyncValidators(c){this.setAsyncValidators(X5(c,this._rawAsyncValidators))}hasValidator(c){return F2(this._rawValidators,c)}hasAsyncValidator(c){return F2(this._rawAsyncValidators,c)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(c={}){this.touched=!0,this._parent&&!c.onlySelf&&this._parent.markAsTouched(c)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(c=>c.markAllAsTouched())}markAsUntouched(c={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(a=>{a.markAsUntouched({onlySelf:!0})}),this._parent&&!c.onlySelf&&this._parent._updateTouched(c)}markAsDirty(c={}){this.pristine=!1,this._parent&&!c.onlySelf&&this._parent.markAsDirty(c)}markAsPristine(c={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(a=>{a.markAsPristine({onlySelf:!0})}),this._parent&&!c.onlySelf&&this._parent._updatePristine(c)}markAsPending(c={}){this.status=D1,c.emitEvent!==!1&&this.statusChanges.emit(this.status),this._parent&&!c.onlySelf&&this._parent.markAsPending(c)}disable(c={}){let a=this._parentMarkedDirty(c.onlySelf);this.status=s2,this.errors=null,this._forEachChild(e=>{e.disable(m1(H({},c),{onlySelf:!0}))}),this._updateValue(),c.emitEvent!==!1&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(m1(H({},c),{skipPristineCheck:a})),this._onDisabledChange.forEach(e=>e(!0))}enable(c={}){let a=this._parentMarkedDirty(c.onlySelf);this.status=l2,this._forEachChild(e=>{e.enable(m1(H({},c),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:c.emitEvent}),this._updateAncestors(m1(H({},c),{skipPristineCheck:a})),this._onDisabledChange.forEach(e=>e(!1))}_updateAncestors(c){this._parent&&!c.onlySelf&&(this._parent.updateValueAndValidity(c),c.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(c){this._parent=c}getRawValue(){return this.value}updateValueAndValidity(c={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===l2||this.status===D1)&&this._runAsyncValidator(c.emitEvent)),c.emitEvent!==!1&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!c.onlySelf&&this._parent.updateValueAndValidity(c)}_updateTreeValidity(c={emitEvent:!0}){this._forEachChild(a=>a._updateTreeValidity(c)),this.updateValueAndValidity({onlySelf:!0,emitEvent:c.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?s2:l2}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(c){if(this.asyncValidator){this.status=D1,this._hasOwnPendingAsyncValidator=!0;let a=z7(this.asyncValidator(this));this._asyncValidationSubscription=a.subscribe(e=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(e,{emitEvent:c})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(c,a={}){this.errors=c,this._updateControlsErrors(a.emitEvent!==!1)}get(c){let a=c;return a==null||(Array.isArray(a)||(a=a.split(".")),a.length===0)?null:a.reduce((e,s)=>e&&e._find(s),this)}getError(c,a){let e=a?this.get(a):this;return e&&e.errors?e.errors[c]:null}hasError(c,a){return!!this.getError(c,a)}get root(){let c=this;for(;c._parent;)c=c._parent;return c}_updateControlsErrors(c){this.status=this._calculateStatus(),c&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(c)}_initObservables(){this.valueChanges=new T,this.statusChanges=new T}_calculateStatus(){return this._allControlsDisabled()?s2:this.errors?D2:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(D1)?D1:this._anyControlsHaveStatus(D2)?D2:l2}_anyControlsHaveStatus(c){return this._anyControls(a=>a.status===c)}_anyControlsDirty(){return this._anyControls(c=>c.dirty)}_anyControlsTouched(){return this._anyControls(c=>c.touched)}_updatePristine(c={}){this.pristine=!this._anyControlsDirty(),this._parent&&!c.onlySelf&&this._parent._updatePristine(c)}_updateTouched(c={}){this.touched=this._anyControlsTouched(),this._parent&&!c.onlySelf&&this._parent._updateTouched(c)}_registerOnCollectionChange(c){this._onCollectionChange=c}_setUpdateStrategy(c){B2(c)&&c.updateOn!=null&&(this._updateOn=c.updateOn)}_parentMarkedDirty(c){let a=this._parent&&this._parent.dirty;return!c&&!!a&&!this._parent._anyControlsDirty()}_find(c){return null}_assignValidators(c){this._rawValidators=Array.isArray(c)?c.slice():c,this._composedValidatorFn=al(this._rawValidators)}_assignAsyncValidators(c){this._rawAsyncValidators=Array.isArray(c)?c.slice():c,this._composedAsyncValidatorFn=nl(this._rawAsyncValidators)}},F1=class extends _1{constructor(c,a,e){super(Q3(a),c4(e,a)),this.controls=c,this._initObservables(),this._setUpdateStrategy(a),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(c,a){return this.controls[c]?this.controls[c]:(this.controls[c]=a,a.setParent(this),a._registerOnCollectionChange(this._onCollectionChange),a)}addControl(c,a,e={}){this.registerControl(c,a),this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}removeControl(c,a={}){this.controls[c]&&this.controls[c]._registerOnCollectionChange(()=>{}),delete this.controls[c],this.updateValueAndValidity({emitEvent:a.emitEvent}),this._onCollectionChange()}setControl(c,a,e={}){this.controls[c]&&this.controls[c]._registerOnCollectionChange(()=>{}),delete this.controls[c],a&&this.registerControl(c,a),this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}contains(c){return this.controls.hasOwnProperty(c)&&this.controls[c].enabled}setValue(c,a={}){v7(this,!0,c),Object.keys(c).forEach(e=>{g7(this,!0,e),this.controls[e].setValue(c[e],{onlySelf:!0,emitEvent:a.emitEvent})}),this.updateValueAndValidity(a)}patchValue(c,a={}){c!=null&&(Object.keys(c).forEach(e=>{let s=this.controls[e];s&&s.patchValue(c[e],{onlySelf:!0,emitEvent:a.emitEvent})}),this.updateValueAndValidity(a))}reset(c={},a={}){this._forEachChild((e,s)=>{e.reset(c?c[s]:null,{onlySelf:!0,emitEvent:a.emitEvent})}),this._updatePristine(a),this._updateTouched(a),this.updateValueAndValidity(a)}getRawValue(){return this._reduceChildren({},(c,a,e)=>(c[e]=a.getRawValue(),c))}_syncPendingControls(){let c=this._reduceChildren(!1,(a,e)=>e._syncPendingControls()?!0:a);return c&&this.updateValueAndValidity({onlySelf:!0}),c}_forEachChild(c){Object.keys(this.controls).forEach(a=>{let e=this.controls[a];e&&c(e,a)})}_setUpControls(){this._forEachChild(c=>{c.setParent(this),c._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(c){for(let[a,e]of Object.entries(this.controls))if(this.contains(a)&&c(e))return!0;return!1}_reduceValue(){let c={};return this._reduceChildren(c,(a,e,s)=>((e.enabled||this.disabled)&&(a[s]=e.value),a))}_reduceChildren(c,a){let e=c;return this._forEachChild((s,n)=>{e=a(e,s,n)}),e}_allControlsDisabled(){for(let c of Object.keys(this.controls))if(this.controls[c].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(c){return this.controls.hasOwnProperty(c)?this.controls[c]:null}};var Y3=class extends F1{};var T1=new w("CallSetDisabledState",{providedIn:"root",factory:()=>t2}),t2="always";function x7(l,c){return[...c.path,l]}function E2(l,c,a=t2){var e,s;e4(l,c),c.valueAccessor.writeValue(l.value),(l.disabled||a==="always")&&((s=(e=c.valueAccessor).setDisabledState)==null||s.call(e,l.disabled)),il(l,c),rl(l,c),ol(l,c),tl(l,c)}function K5(l,c,a=!0){let e=()=>{};c.valueAccessor&&(c.valueAccessor.registerOnChange(e),c.valueAccessor.registerOnTouched(e)),O2(l,c),l&&(c._invokeOnDestroyCallbacks(),l._registerOnCollectionChange(()=>{}))}function I2(l,c){l.forEach(a=>{a.registerOnValidatorChange&&a.registerOnValidatorChange(c)})}function tl(l,c){if(c.valueAccessor.setDisabledState){let a=e=>{c.valueAccessor.setDisabledState(e)};l.registerOnDisabledChange(a),c._registerOnDestroy(()=>{l._unregisterOnDisabledChange(a)})}}function e4(l,c){let a=M7(l);c.validator!==null?l.setValidators($5(a,c.validator)):typeof a=="function"&&l.setValidators([a]);let e=C7(l);c.asyncValidator!==null?l.setAsyncValidators($5(e,c.asyncValidator)):typeof e=="function"&&l.setAsyncValidators([e]);let s=()=>l.updateValueAndValidity();I2(c._rawValidators,s),I2(c._rawAsyncValidators,s)}function O2(l,c){let a=!1;if(l!==null){if(c.validator!==null){let s=M7(l);if(Array.isArray(s)&&s.length>0){let n=s.filter(t=>t!==c.validator);n.length!==s.length&&(a=!0,l.setValidators(n))}}if(c.asyncValidator!==null){let s=C7(l);if(Array.isArray(s)&&s.length>0){let n=s.filter(t=>t!==c.asyncValidator);n.length!==s.length&&(a=!0,l.setAsyncValidators(n))}}}let e=()=>{};return I2(c._rawValidators,e),I2(c._rawAsyncValidators,e),a}function il(l,c){c.valueAccessor.registerOnChange(a=>{l._pendingValue=a,l._pendingChange=!0,l._pendingDirty=!0,l.updateOn==="change"&&b7(l,c)})}function ol(l,c){c.valueAccessor.registerOnTouched(()=>{l._pendingTouched=!0,l.updateOn==="blur"&&l._pendingChange&&b7(l,c),l.updateOn!=="submit"&&l.markAsTouched()})}function b7(l,c){l._pendingDirty&&l.markAsDirty(),l.setValue(l._pendingValue,{emitModelToViewChange:!1}),c.viewToModelUpdate(l._pendingValue),l._pendingChange=!1}function rl(l,c){let a=(e,s)=>{c.valueAccessor.writeValue(e),s&&c.viewToModelUpdate(e)};l.registerOnChange(a),c._registerOnDestroy(()=>{l._unregisterOnChange(a)})}function N7(l,c){l==null,e4(l,c)}function fl(l,c){return O2(l,c)}function y7(l,c){if(!l.hasOwnProperty("model"))return!1;let a=l.model;return a.isFirstChange()?!0:!Object.is(c,a.currentValue)}function ml(l){return Object.getPrototypeOf(l.constructor)===P1}function S7(l,c){l._syncPendingControls(),c.forEach(a=>{let e=a.control;e.updateOn==="submit"&&e._pendingChange&&(a.viewToModelUpdate(e._pendingValue),e._pendingChange=!1)})}function w7(l,c){if(!c)return null;Array.isArray(c);let a,e,s;return c.forEach(n=>{n.constructor===a7?a=n:ml(n)?e=n:s=n}),s||e||a||null}function zl(l,c){let a=l.indexOf(c);a>-1&&l.splice(a,1)}var hl={provide:X,useExisting:x(()=>ul)},a2=Promise.resolve(),ul=(()=>{let c=class c extends X{constructor(e,s,n){super(),this.callSetDisabledState=n,this.submitted=!1,this._directives=new Set,this.ngSubmit=new T,this.form=new F1({},K3(e),J3(s))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(e){a2.then(()=>{let s=this._findContainer(e.path);e.control=s.registerControl(e.name,e.control),E2(e.control,e,this.callSetDisabledState),e.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(e)})}getControl(e){return this.form.get(e.path)}removeControl(e){a2.then(()=>{let s=this._findContainer(e.path);s&&s.removeControl(e.name),this._directives.delete(e)})}addFormGroup(e){a2.then(()=>{let s=this._findContainer(e.path),n=new F1({});N7(n,e),s.registerControl(e.name,n),n.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(e){a2.then(()=>{let s=this._findContainer(e.path);s&&s.removeControl(e.name)})}getFormGroup(e){return this.form.get(e.path)}updateModel(e,s){a2.then(()=>{this.form.get(e.path).setValue(s)})}setValue(e){this.control.setValue(e)}onSubmit(e){var s;return this.submitted=!0,S7(this.form,this._directives),this.ngSubmit.emit(e),((s=e==null?void 0:e.target)==null?void 0:s.method)==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this.submitted=!1}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(e){return e.pop(),e.length?this.form.get(e):this.form}};c.\u0275fac=function(s){return new(s||c)(h(K,10),h(R2,10),h(T1,8))},c.\u0275dir=M({type:c,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(s,n){s&1&&j("submit",function(i){return n.onSubmit(i)})("reset",function(){return n.onReset()})},inputs:{options:[B.None,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[N([hl]),v]});let l=c;return l})();function J5(l,c){let a=l.indexOf(c);a>-1&&l.splice(a,1)}function Q5(l){return typeof l=="object"&&l!==null&&Object.keys(l).length===2&&"value"in l&&"disabled"in l}var n2=class extends _1{constructor(c=null,a,e){super(Q3(a),c4(e,a)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(c),this._setUpdateStrategy(a),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),B2(a)&&(a.nonNullable||a.initialValueIsDefault)&&(Q5(c)?this.defaultValue=c.value:this.defaultValue=c)}setValue(c,a={}){this.value=this._pendingValue=c,this._onChange.length&&a.emitModelToViewChange!==!1&&this._onChange.forEach(e=>e(this.value,a.emitViewToModelChange!==!1)),this.updateValueAndValidity(a)}patchValue(c,a={}){this.setValue(c,a)}reset(c=this.defaultValue,a={}){this._applyFormState(c),this.markAsPristine(a),this.markAsUntouched(a),this.setValue(this.value,a),this._pendingChange=!1}_updateValue(){}_anyControls(c){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(c){this._onChange.push(c)}_unregisterOnChange(c){J5(this._onChange,c)}registerOnDisabledChange(c){this._onDisabledChange.push(c)}_unregisterOnDisabledChange(c){J5(this._onDisabledChange,c)}_forEachChild(c){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(c){Q5(c)?(this.value=this._pendingValue=c.value,c.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=c}};var pl=l=>l instanceof n2;var Ll={provide:i1,useExisting:x(()=>dl)},c7=Promise.resolve(),dl=(()=>{let c=class c extends i1{constructor(e,s,n,t,i,f){super(),this._changeDetectorRef=i,this.callSetDisabledState=f,this.control=new n2,this._registered=!1,this.name="",this.update=new T,this._parent=e,this._setValidators(s),this._setAsyncValidators(n),this.valueAccessor=w7(this,t)}ngOnChanges(e){if(this._checkForErrors(),!this._registered||"name"in e){if(this._registered&&(this._checkName(),this.formDirective)){let s=e.name.previousValue;this.formDirective.removeControl({name:s,path:this._getPath(s)})}this._setUpControl()}"isDisabled"in e&&this._updateDisabled(e),y7(e,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){E2(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(e){c7.then(()=>{var s;this.control.setValue(e,{emitViewToModelChange:!1}),(s=this._changeDetectorRef)==null||s.markForCheck()})}_updateDisabled(e){let s=e.isDisabled.currentValue,n=s!==0&&K2(s);c7.then(()=>{var t;n&&!this.control.disabled?this.control.disable():!n&&this.control.disabled&&this.control.enable(),(t=this._changeDetectorRef)==null||t.markForCheck()})}_getPath(e){return this._parent?x7(e,this._parent):[e]}};c.\u0275fac=function(s){return new(s||c)(h(X,9),h(K,10),h(R2,10),h(o1,10),h(L2,8),h(T1,8))},c.\u0275dir=M({type:c,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[B.None,"disabled","isDisabled"],model:[B.None,"ngModel","model"],options:[B.None,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[N([Ll]),v,q]});let l=c;return l})(),ea=(()=>{let c=class c{};c.\u0275fac=function(s){return new(s||c)},c.\u0275dir=M({type:c,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]});let l=c;return l})(),Ml={provide:o1,useExisting:x(()=>Cl),multi:!0},Cl=(()=>{let c=class c extends P1{writeValue(e){let s=e==null?"":e;this.setProperty("value",s)}registerOnChange(e){this.onChange=s=>{e(s==""?null:parseFloat(s))}}};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(s,n){s&1&&j("input",function(i){return n.onChange(i.target.value)})("blur",function(){return n.onTouched()})},features:[N([Ml]),v]});let l=c;return l})(),gl={provide:o1,useExisting:x(()=>xl),multi:!0};var vl=(()=>{let c=class c{constructor(){this._accessors=[]}add(e,s){this._accessors.push([e,s])}remove(e){for(let s=this._accessors.length-1;s>=0;--s)if(this._accessors[s][1]===e){this._accessors.splice(s,1);return}}select(e){this._accessors.forEach(s=>{this._isSameGroup(s,e)&&s[1]!==e&&s[1].fireUncheck(e.value)})}_isSameGroup(e,s){return e[0].control?e[0]._parent===s._control._parent&&e[1].name===s.name:!1}};c.\u0275fac=function(s){return new(s||c)},c.\u0275prov=g({token:c,factory:c.\u0275fac,providedIn:"root"});let l=c;return l})(),xl=(()=>{let c=class c extends P1{constructor(e,s,n,t){var i;super(e,s),this._registry=n,this._injector=t,this.setDisabledStateFired=!1,this.onChange=()=>{},this.callSetDisabledState=(i=a4(T1,{optional:!0}))!=null?i:t2}ngOnInit(){this._control=this._injector.get(i1),this._checkName(),this._registry.add(this._control,this)}ngOnDestroy(){this._registry.remove(this)}writeValue(e){this._state=e===this.value,this.setProperty("checked",this._state)}registerOnChange(e){this._fn=e,this.onChange=()=>{e(this.value),this._registry.select(this)}}setDisabledState(e){(this.setDisabledStateFired||e||this.callSetDisabledState==="whenDisabledForLegacyCode")&&this.setProperty("disabled",e),this.setDisabledStateFired=!0}fireUncheck(e){this.writeValue(e)}_checkName(){this.name&&this.formControlName&&(this.name,this.formControlName),!this.name&&this.formControlName&&(this.name=this.formControlName)}};c.\u0275fac=function(s){return new(s||c)(h(Q),h(U),h(vl),h(t4))},c.\u0275dir=M({type:c,selectors:[["input","type","radio","formControlName",""],["input","type","radio","formControl",""],["input","type","radio","ngModel",""]],hostBindings:function(s,n){s&1&&j("change",function(){return n.onChange()})("blur",function(){return n.onTouched()})},inputs:{name:"name",formControlName:"formControlName",value:"value"},features:[N([gl]),v]});let l=c;return l})();var A7=new w("");var bl={provide:X,useExisting:x(()=>Nl)},Nl=(()=>{let c=class c extends X{constructor(e,s,n){super(),this.callSetDisabledState=n,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new T,this._setValidators(e),this._setAsyncValidators(s)}ngOnChanges(e){this._checkFormPresent(),e.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(O2(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(e){let s=this.form.get(e.path);return E2(s,e,this.callSetDisabledState),s.updateValueAndValidity({emitEvent:!1}),this.directives.push(e),s}getControl(e){return this.form.get(e.path)}removeControl(e){K5(e.control||null,e,!1),zl(this.directives,e)}addFormGroup(e){this._setUpFormContainer(e)}removeFormGroup(e){this._cleanUpFormContainer(e)}getFormGroup(e){return this.form.get(e.path)}addFormArray(e){this._setUpFormContainer(e)}removeFormArray(e){this._cleanUpFormContainer(e)}getFormArray(e){return this.form.get(e.path)}updateModel(e,s){this.form.get(e.path).setValue(s)}onSubmit(e){var s;return this.submitted=!0,S7(this.form,this.directives),this.ngSubmit.emit(e),((s=e==null?void 0:e.target)==null?void 0:s.method)==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this.submitted=!1}_updateDomValue(){this.directives.forEach(e=>{let s=e.control,n=this.form.get(e.path);s!==n&&(K5(s||null,e),pl(n)&&(E2(n,e,this.callSetDisabledState),e.control=n))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(e){let s=this.form.get(e.path);N7(s,e),s.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(e){if(this.form){let s=this.form.get(e.path);s&&fl(s,e)&&s.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){e4(this.form,this),this._oldForm&&O2(this._oldForm,this)}_checkFormPresent(){this.form}};c.\u0275fac=function(s){return new(s||c)(h(K,10),h(R2,10),h(T1,8))},c.\u0275dir=M({type:c,selectors:[["","formGroup",""]],hostBindings:function(s,n){s&1&&j("submit",function(i){return n.onSubmit(i)})("reset",function(){return n.onReset()})},inputs:{form:[B.None,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[N([bl]),v,q]});let l=c;return l})();var yl={provide:i1,useExisting:x(()=>Sl)},Sl=(()=>{let c=class c extends i1{set isDisabled(e){}constructor(e,s,n,t,i){super(),this._ngModelWarningConfig=i,this._added=!1,this.name=null,this.update=new T,this._ngModelWarningSent=!1,this._parent=e,this._setValidators(s),this._setAsyncValidators(n),this.valueAccessor=w7(this,t)}ngOnChanges(e){this._added||this._setUpControl(),y7(e,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}get path(){return x7(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}};c._ngModelWarningSentOnce=!1,c.\u0275fac=function(s){return new(s||c)(h(X,13),h(K,10),h(R2,10),h(o1,10),h(A7,8))},c.\u0275dir=M({type:c,selectors:[["","formControlName",""]],inputs:{name:[B.None,"formControlName","name"],isDisabled:[B.None,"disabled","isDisabled"],model:[B.None,"ngModel","model"]},outputs:{update:"ngModelChange"},features:[N([yl]),v,q]});let l=c;return l})(),wl={provide:o1,useExisting:x(()=>H7),multi:!0};function V7(l,c){return l==null?`${c}`:(c&&typeof c=="object"&&(c="Object"),`${l}: ${c}`.slice(0,50))}function Al(l){return l.split(":")[0]}var H7=(()=>{let c=class c extends P1{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(e){this._compareWith=e}writeValue(e){this.value=e;let s=this._getOptionId(e),n=V7(s,e);this.setProperty("value",n)}registerOnChange(e){this.onChange=s=>{this.value=this._getOptionValue(s),e(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(e){for(let s of this._optionMap.keys())if(this._compareWith(this._optionMap.get(s),e))return s;return null}_getOptionValue(e){let s=Al(e);return this._optionMap.has(s)?this._optionMap.get(s):e}};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(s,n){s&1&&j("change",function(i){return n.onChange(i.target.value)})("blur",function(){return n.onTouched()})},inputs:{compareWith:"compareWith"},features:[N([wl]),v]});let l=c;return l})(),la=(()=>{let c=class c{constructor(e,s,n){this._element=e,this._renderer=s,this._select=n,this._select&&(this.id=this._select._registerOption())}set ngValue(e){this._select!=null&&(this._select._optionMap.set(this.id,e),this._setElementValue(V7(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._setElementValue(e),this._select&&this._select.writeValue(this._select.value)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}};c.\u0275fac=function(s){return new(s||c)(h(U),h(Q),h(H7,9))},c.\u0275dir=M({type:c,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}});let l=c;return l})(),Vl={provide:o1,useExisting:x(()=>k7),multi:!0};function e7(l,c){return l==null?`${c}`:(typeof c=="string"&&(c=`'${c}'`),c&&typeof c=="object"&&(c="Object"),`${l}: ${c}`.slice(0,50))}function Hl(l){return l.split(":")[0]}var k7=(()=>{let c=class c extends P1{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(e){this._compareWith=e}writeValue(e){this.value=e;let s;if(Array.isArray(e)){let n=e.map(t=>this._getOptionId(t));s=(t,i)=>{t._setSelected(n.indexOf(i.toString())>-1)}}else s=(n,t)=>{n._setSelected(!1)};this._optionMap.forEach(s)}registerOnChange(e){this.onChange=s=>{let n=[],t=s.selectedOptions;if(t!==void 0){let i=t;for(let f=0;f<i.length;f++){let r=i[f],z=this._getOptionValue(r.value);n.push(z)}}else{let i=s.options;for(let f=0;f<i.length;f++){let r=i[f];if(r.selected){let z=this._getOptionValue(r.value);n.push(z)}}}this.value=n,e(n)}}_registerOption(e){let s=(this._idCounter++).toString();return this._optionMap.set(s,e),s}_getOptionId(e){for(let s of this._optionMap.keys())if(this._compareWith(this._optionMap.get(s)._value,e))return s;return null}_getOptionValue(e){let s=Hl(e);return this._optionMap.has(s)?this._optionMap.get(s)._value:e}};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(s,n){s&1&&j("change",function(i){return n.onChange(i.target)})("blur",function(){return n.onTouched()})},inputs:{compareWith:"compareWith"},features:[N([Vl]),v]});let l=c;return l})(),sa=(()=>{let c=class c{constructor(e,s,n){this._element=e,this._renderer=s,this._select=n,this._select&&(this.id=this._select._registerOption(this))}set ngValue(e){this._select!=null&&(this._value=e,this._setElementValue(e7(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._select?(this._value=e,this._setElementValue(e7(this.id,e)),this._select.writeValue(this._select.value)):this._setElementValue(e)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}_setSelected(e){this._renderer.setProperty(this._element.nativeElement,"selected",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}};c.\u0275fac=function(s){return new(s||c)(h(U),h(Q),h(k7,9))},c.\u0275dir=M({type:c,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}});let l=c;return l})();function kl(l){return typeof l=="number"?l:parseInt(l,10)}function D7(l){return typeof l=="number"?l:parseFloat(l)}var i2=(()=>{let c=class c{constructor(){this._validator=_2}ngOnChanges(e){if(this.inputName in e){let s=this.normalizeInput(e[this.inputName].currentValue);this._enabled=this.enabled(s),this._validator=this._enabled?this.createValidator(s):_2,this._onChange&&this._onChange()}}validate(e){return this._validator(e)}registerOnValidatorChange(e){this._onChange=e}enabled(e){return e!=null}};c.\u0275fac=function(s){return new(s||c)},c.\u0275dir=M({type:c,features:[q]});let l=c;return l})(),Dl={provide:K,useExisting:x(()=>_l),multi:!0},_l=(()=>{let c=class c extends i2{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=e=>D7(e),this.createValidator=e=>i7(e)}};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(s,n){s&2&&c1("max",n._enabled?n.max:null)},inputs:{max:"max"},features:[N([Dl]),v]});let l=c;return l})(),Fl={provide:K,useExisting:x(()=>Pl),multi:!0},Pl=(()=>{let c=class c extends i2{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=e=>D7(e),this.createValidator=e=>t7(e)}};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(s,n){s&2&&c1("min",n._enabled?n.min:null)},inputs:{min:"min"},features:[N([Fl]),v]});let l=c;return l})(),Tl={provide:K,useExisting:x(()=>El),multi:!0};var El=(()=>{let c=class c extends i2{constructor(){super(...arguments),this.inputName="required",this.normalizeInput=K2,this.createValidator=e=>o7}enabled(e){return e}};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(s,n){s&2&&c1("required",n._enabled?"":null)},inputs:{required:"required"},features:[N([Tl]),v]});let l=c;return l})();var Il={provide:K,useExisting:x(()=>Ol),multi:!0},Ol=(()=>{let c=class c extends i2{constructor(){super(...arguments),this.inputName="maxlength",this.normalizeInput=e=>kl(e),this.createValidator=e=>r7(e)}};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(s,n){s&2&&c1("maxlength",n._enabled?n.maxlength:null)},inputs:{maxlength:"maxlength"},features:[N([Il]),v]});let l=c;return l})(),Rl={provide:K,useExisting:x(()=>Bl),multi:!0},Bl=(()=>{let c=class c extends i2{constructor(){super(...arguments),this.inputName="pattern",this.normalizeInput=e=>e,this.createValidator=e=>f7(e)}};c.\u0275fac=(()=>{let e;return function(n){return(e||(e=b(c)))(n||c)}})(),c.\u0275dir=M({type:c,selectors:[["","pattern","","formControlName",""],["","pattern","","formControl",""],["","pattern","","ngModel",""]],hostVars:1,hostBindings:function(s,n){s&2&&c1("pattern",n._enabled?n.pattern:null)},inputs:{pattern:"pattern"},features:[N([Rl]),v]});let l=c;return l})();var _7=(()=>{let c=class c{};c.\u0275fac=function(s){return new(s||c)},c.\u0275mod=P({type:c}),c.\u0275inj=F({});let l=c;return l})(),X3=class extends _1{constructor(c,a,e){super(Q3(a),c4(e,a)),this.controls=c,this._initObservables(),this._setUpdateStrategy(a),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(c){return this.controls[this._adjustIndex(c)]}push(c,a={}){this.controls.push(c),this._registerControl(c),this.updateValueAndValidity({emitEvent:a.emitEvent}),this._onCollectionChange()}insert(c,a,e={}){this.controls.splice(c,0,a),this._registerControl(a),this.updateValueAndValidity({emitEvent:e.emitEvent})}removeAt(c,a={}){let e=this._adjustIndex(c);e<0&&(e=0),this.controls[e]&&this.controls[e]._registerOnCollectionChange(()=>{}),this.controls.splice(e,1),this.updateValueAndValidity({emitEvent:a.emitEvent})}setControl(c,a,e={}){let s=this._adjustIndex(c);s<0&&(s=0),this.controls[s]&&this.controls[s]._registerOnCollectionChange(()=>{}),this.controls.splice(s,1),a&&(this.controls.splice(s,0,a),this._registerControl(a)),this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(c,a={}){v7(this,!1,c),c.forEach((e,s)=>{g7(this,!1,s),this.at(s).setValue(e,{onlySelf:!0,emitEvent:a.emitEvent})}),this.updateValueAndValidity(a)}patchValue(c,a={}){c!=null&&(c.forEach((e,s)=>{this.at(s)&&this.at(s).patchValue(e,{onlySelf:!0,emitEvent:a.emitEvent})}),this.updateValueAndValidity(a))}reset(c=[],a={}){this._forEachChild((e,s)=>{e.reset(c[s],{onlySelf:!0,emitEvent:a.emitEvent})}),this._updatePristine(a),this._updateTouched(a),this.updateValueAndValidity(a)}getRawValue(){return this.controls.map(c=>c.getRawValue())}clear(c={}){this.controls.length<1||(this._forEachChild(a=>a._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:c.emitEvent}))}_adjustIndex(c){return c<0?c+this.length:c}_syncPendingControls(){let c=this.controls.reduce((a,e)=>e._syncPendingControls()?!0:a,!1);return c&&this.updateValueAndValidity({onlySelf:!0}),c}_forEachChild(c){this.controls.forEach((a,e)=>{c(a,e)})}_updateValue(){this.value=this.controls.filter(c=>c.enabled||this.disabled).map(c=>c.value)}_anyControls(c){return this.controls.some(a=>a.enabled&&c(a))}_setUpControls(){this._forEachChild(c=>this._registerControl(c))}_allControlsDisabled(){for(let c of this.controls)if(c.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(c){c.setParent(this),c._registerOnCollectionChange(this._onCollectionChange)}_find(c){var a;return(a=this.at(c))!=null?a:null}};function l7(l){return!!l&&(l.asyncValidators!==void 0||l.validators!==void 0||l.updateOn!==void 0)}var aa=(()=>{let c=class c{constructor(){this.useNonNullable=!1}get nonNullable(){let e=new c;return e.useNonNullable=!0,e}group(e,s=null){let n=this._reduceControls(e),t={};return l7(s)?t=s:s!==null&&(t.validators=s.validator,t.asyncValidators=s.asyncValidator),new F1(n,t)}record(e,s=null){let n=this._reduceControls(e);return new Y3(n,s)}control(e,s,n){let t={};return this.useNonNullable?(l7(s)?t=s:(t.validators=s,t.asyncValidators=n),new n2(e,m1(H({},t),{nonNullable:!0}))):new n2(e,s,n)}array(e,s,n){let t=e.map(i=>this._createControl(i));return new X3(t,s,n)}_reduceControls(e){let s={};return Object.keys(e).forEach(n=>{s[n]=this._createControl(e[n])}),s}_createControl(e){if(e instanceof n2)return e;if(e instanceof _1)return e;if(Array.isArray(e)){let s=e[0],n=e.length>1?e[1]:null,t=e.length>2?e[2]:null;return this.control(s,n,t)}else return this.control(e)}};c.\u0275fac=function(s){return new(s||c)},c.\u0275prov=g({token:c,factory:c.\u0275fac,providedIn:"root"});let l=c;return l})();var na=(()=>{let c=class c{static withConfig(e){var s;return{ngModule:c,providers:[{provide:T1,useValue:(s=e.callSetDisabledState)!=null?s:t2}]}}};c.\u0275fac=function(s){return new(s||c)},c.\u0275mod=P({type:c}),c.\u0275inj=F({imports:[_7]});let l=c;return l})(),ta=(()=>{let c=class c{static withConfig(e){var s,n;return{ngModule:c,providers:[{provide:A7,useValue:(s=e.warnOnNgModelWithFormControl)!=null?s:"always"},{provide:T1,useValue:(n=e.callSetDisabledState)!=null?n:t2}]}}};c.\u0275fac=function(s){return new(s||c)},c.\u0275mod=P({type:c}),c.\u0275inj=F({imports:[_7]});let l=c;return l})();var r1=(()=>{let c=class c{};c.SUCCESS="SUCCESS",c.WARNING="WARNING",c.ERROR="DANGER";let l=c;return l})();var q2={production:!1,apiUrl:"/api",cdnUrl:"http://localhost/cdn",tenantId:"0e22c37d-bfb5-4276-bd30-355fcdb39c9e",tenantDomain:"autovnfb.com",defaultLanguage:"vi",appName:"SMM Admin",appVersion:"1.0.0",defaultCurrency:"USD",defaultCurrencySymbol:"$",defaultDateFormat:"dd/MM/yyyy",defaultTimeFormat:"HH:mm",defaultDateTimeFormat:"dd/MM/yyyy HH:mm",defaultPageSize:10,maxPageSize:100,defaultTheme:"light",defaultPrimaryColor:"#1976d2",defaultSecondaryColor:"#f44336",defaultSuccessColor:"#4caf50",defaultWarningColor:"#ff9800",defaultErrorColor:"#f44336",defaultInfoColor:"#2196f3",defaultTextColor:"#212121",defaultBackgroundColor:"#ffffff",defaultFontFamily:'Roboto, "Helvetica Neue", sans-serif',defaultFontSize:"14px",defaultLineHeight:"1.5",defaultBorderRadius:"4px",defaultBoxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)",defaultTransition:"all 0.3s ease",defaultZIndex:1e3,defaultOpacity:.8,defaultSpacing:"8px",defaultPadding:"16px",defaultMargin:"16px",defaultGap:"8px",defaultBorderWidth:"1px",defaultBorderColor:"#e0e0e0",defaultBorderStyle:"solid",defaultOutlineWidth:"2px",defaultOutlineColor:"#1976d2",defaultOutlineStyle:"solid",defaultOutlineOffset:"2px",defaultFocusRingWidth:"2px",defaultFocusRingColor:"#1976d2",defaultFocusRingStyle:"solid",defaultFocusRingOffset:"2px",defaultDisabledOpacity:.5,defaultDisabledCursor:"not-allowed",defaultDisabledBackgroundColor:"#f5f5f5",defaultDisabledTextColor:"#9e9e9e",defaultDisabledBorderColor:"#e0e0e0",defaultHoverOpacity:.9,defaultHoverBackgroundColor:"#f5f5f5",defaultHoverTextColor:"#212121",defaultHoverBorderColor:"#e0e0e0",defaultActiveOpacity:1,defaultActiveBackgroundColor:"#e0e0e0",defaultActiveTextColor:"#212121",defaultActiveBorderColor:"#e0e0e0",defaultFocusOpacity:1,defaultFocusBackgroundColor:"#f5f5f5",defaultFocusTextColor:"#212121",defaultFocusBorderColor:"#1976d2"};var U2=(()=>{let c=class c{constructor(e){this.platformId=e,this._tenantConfig=null,this._apiUrl=this.getApiUrl(),this._tenantConfig=this.getTenantConfig(),console.log("ConfigService initialized with API URL:",this._apiUrl),this._tenantConfig&&console.log("Tenant configuration loaded:",this._tenantConfig)}getTenantConfig(){if(G(this.platformId)){let e=window.TENANT_CONFIG;if(e)return e}return null}getApiUrl(){if(G(this.platformId)){let e=window.TENANT_CONFIG;if(e&&e.apiUrl)return console.log("Using API URL from tenant configuration:",e.apiUrl),e.apiUrl;let s=window.API_URL;if(s)return console.log("Using API URL from window object:",s),s;let n=this.getTenantApiUrlFromHeaders();if(n)return console.log("Using API URL from headers:",n),n}if(L4(this.platformId)){let e=this.getServerApiUrl();if(e)return console.log("Using API URL from server environment:",e),e}return console.log("Using API URL from environment.ts:",q2.apiUrl),q2.apiUrl}getTenantApiUrlFromHeaders(){if(G(this.platformId)&&document){let e=document.querySelector('meta[name="x-tenant-api-url"]');if(e){let s=e.getAttribute("content");if(s)return s}}return null}getServerApiUrl(){return null}get currentDomain(){return G(this.platformId)?window.location.hostname:""}get apiUrl(){return this._apiUrl}get tenantConfig(){return this._tenantConfig}get tenantId(){var e;return((e=this._tenantConfig)==null?void 0:e.tenantId)||null}get theme(){var e;return((e=this._tenantConfig)==null?void 0:e.theme)||"default"}get logoUrl(){var e;return((e=this._tenantConfig)==null?void 0:e.logoUrl)||null}get companyName(){var e;return((e=this._tenantConfig)==null?void 0:e.companyName)||"SMM System"}get isProduction(){return q2.production}};c.\u0275fac=function(s){return new(s||c)(d(f2))},c.\u0275prov=g({token:c,factory:c.\u0275fac,providedIn:"root"});let l=c;return l})();var C1=class C1{};C1.PANEL="PANEL",C1.USER="USER",C1.ROLES=[C1.PANEL,C1.USER];var j2=C1;var ql=new w("JWT_OPTIONS"),G2=(()=>{class l{constructor(a=null){this.tokenGetter=a&&a.tokenGetter||function(){}}urlBase64Decode(a){let e=a.replace(/-/g,"+").replace(/_/g,"/");switch(e.length%4){case 0:break;case 2:{e+="==";break}case 3:{e+="=";break}default:throw new Error("Illegal base64url string!")}return this.b64DecodeUnicode(e)}b64decode(a){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s="";if(a=String(a).replace(/=+$/,""),a.length%4===1)throw new Error("'atob' failed: The string to be decoded is not correctly encoded.");for(let n=0,t,i,f=0;i=a.charAt(f++);~i&&(t=n%4?t*64+i:i,n++%4)?s+=String.fromCharCode(255&t>>(-2*n&6)):0)i=e.indexOf(i);return s}b64DecodeUnicode(a){return decodeURIComponent(Array.prototype.map.call(this.b64decode(a),e=>"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)).join(""))}decodeToken(a=this.tokenGetter()){return a instanceof Promise?a.then(e=>this._decodeToken(e)):this._decodeToken(a)}_decodeToken(a){if(!a||a==="")return null;let e=a.split(".");if(e.length!==3)throw new Error("The inspected token doesn't appear to be a JWT. Check to make sure it has three parts and see https://jwt.io for more.");let s=this.urlBase64Decode(e[1]);if(!s)throw new Error("Cannot decode the token.");return JSON.parse(s)}getTokenExpirationDate(a=this.tokenGetter()){return a instanceof Promise?a.then(e=>this._getTokenExpirationDate(e)):this._getTokenExpirationDate(a)}_getTokenExpirationDate(a){let e;if(e=this.decodeToken(a),!e||!e.hasOwnProperty("exp"))return null;let s=new Date(0);return s.setUTCSeconds(e.exp),s}isTokenExpired(a=this.tokenGetter(),e){return a instanceof Promise?a.then(s=>this._isTokenExpired(s,e)):this._isTokenExpired(a,e)}_isTokenExpired(a,e){if(!a||a==="")return!0;let s=this.getTokenExpirationDate(a);return e=e||0,s===null?!1:!(s.valueOf()>new Date().valueOf()+e*1e3)}getAuthScheme(a,e){return typeof a=="function"?a(e):a}}return l.\u0275fac=function(a){return new(a||l)(d(ql))},l.\u0275prov=g({token:l,factory:l.\u0275fac}),l})();var T7=(()=>{let c=class c{constructor(e,s,n,t,i){this.router=e,this.http=s,this.jwtHelper=n,this.platformId=t,this.configService=i,this._loading$=new x1(!1);let f;if(G(this.platformId)){let r=localStorage.getItem("user");if(r)try{f=JSON.parse(r)}catch(z){console.error("Error parsing user from localStorage",z)}}this._authSubject$=new x1(f),this._auth$=this._authSubject$.asObservable()}get authValue(){return this._authSubject$.getValue()}isAdmin(){let e=this.getRoles();return e?e.every(s=>[j2.PANEL].includes(s)):!1}getRoles(){let e=this.authValue;if(e)return this.jwtHelper.decodeToken(e.tokens.access_token).roles}isAuthenticated(){let e=this.authValue;return!!(e&&e.tokens.access_token)}isTokenExpired(){return this.isAuthenticated()&&this.jwtHelper.isTokenExpired(this.authValue.tokens.access_token)}register(e){return this.loading$.next(!0),this.http.post(`${this.configService.apiUrl}/users/register`,e).pipe(B1(),z1(s=>this.updateUser(s)),R1(()=>this.loading$.next(!1)))}updateUser(e){if(this._authSubject$.next(e),console.log("AuthService - updateUser called, user:",e==null?void 0:e.id),G(this.platformId))try{localStorage.setItem("user",JSON.stringify(e)),console.log("AuthService - User saved to localStorage")}catch(s){console.error("AuthService - Error saving user to localStorage:",s)}}refreshToken(e){this.loading$.next(!0);let s=new d4({"x-refresh-token":e});return this.http.post(`${this.configService.apiUrl}/access/refresh`,null,{headers:s}).pipe(B1(),z1(n=>this.updateUser(n)),R1(()=>this.loading$.next(!1)))}login(e,s){return console.log("AuthService - login method called"),this.loading$.next(!0),this.http.post(`${this.configService.apiUrl}/access/login`,{user_name:e,password:s}).pipe(B1(),z1(n=>{console.log("AuthService - Login successful, updating user"),this.updateUser(n)}),R1(()=>{console.log("AuthService - Login finalized"),this.loading$.next(!1)}))}logout(){this.loading$.next(!0),this.http.post(`${this.configService.apiUrl}/access/logout`,null).pipe(B1(),R1(()=>{this.loading$.next(!1),this._authSubject$.next(void 0),G(this.platformId)&&localStorage.removeItem("user"),this.redirectTo("/auth/login")})).subscribe()}redirectTo(e){this.router.navigate([e])}get loading$(){return this._loading$}get auth$(){return this._auth$}};c.\u0275fac=function(s){return new(s||c)(d(C4),d(d2),d(G2),d(f2),d(U2))},c.\u0275prov=g({token:c,factory:c.\u0275fac,providedIn:"root"});let l=c;return l})();var Fa=(()=>{let c=class c{get currentTenantValue(){return this._currentTenant$.value}get accessibleTenantsValue(){return this._accessibleTenants$.value}constructor(e,s,n,t){this.http=e,this.configService=s,this.authService=n,this.jwtHelper=t,this._currentTenant$=new x1(null),this._accessibleTenants$=new x1([]),this.currentTenant$=this._currentTenant$.asObservable(),this.accessibleTenants$=this._accessibleTenants$.asObservable(),this.updateTenantInfoFromToken(),this.authService.auth$.subscribe(i=>{i&&i.tokens&&i.tokens.access_token?this.updateTenantInfoFromToken():(this._currentTenant$.next(null),this._accessibleTenants$.next([]))})}updateTenantInfoFromToken(){let e=this.authService.authValue;if(e&&e.tokens&&e.tokens.access_token)try{let s=this.jwtHelper.decodeToken(e.tokens.access_token);console.log("TenantService - Token payload:",s);let n=s.current_tenant_id;n&&(this._currentTenant$.next(n),typeof localStorage<"u"&&localStorage.setItem("tenant_id",n))}catch(s){console.error("Error extracting tenant info from token:",s)}}switchTenant(e){return this.http.post(`${this.configService.apiUrl}/access/switch/${e}`,{}).pipe(z1(s=>{let n=this.authService.authValue;n&&n.tokens&&(n.tokens.access_token=s.access_token,n.tokens.refresh_token=s.refresh_token,n.tokens.client_id=s.client_id,this.authService.updateUser(n)),typeof localStorage<"u"&&(localStorage.removeItem("app_design_settings"),localStorage.removeItem("general_settings")),this.updateTenantInfoFromToken()}))}reloadDataAfterTenantSwitch(){this.updateTenantInfoFromToken(),this.getAccessibleTenants().subscribe()}getAccessibleTenants(){return this.http.get(`${this.configService.apiUrl}/access/tenants/accessible`).pipe(z1(e=>{this._accessibleTenants$.next(e)}))}getAllTenantSubscriptions(){return this.http.get(`${this.configService.apiUrl}/tenant-subscriptions`)}toggleAutoRenewal(e,s){return this.http.put(`${this.configService.apiUrl}/tenant-subscriptions/${e}/auto-renewal`,{auto_renewal:s})}};c.\u0275fac=function(s){return new(s||c)(d(d2),d(U2),d(T7),d(G2))},c.\u0275prov=g({token:c,factory:c.\u0275fac,providedIn:"root"});let l=c;return l})();var Ea=(()=>{let c=class c{constructor(){this.toasts=[]}showToast(e,s=r1.SUCCESS,n=3e3){let t=document.createElement("div");switch(t.className="toast-notification",s){case r1.SUCCESS:t.classList.add("toast-success");break;case r1.WARNING:t.classList.add("toast-warning");break;case r1.ERROR:t.classList.add("toast-error");break;default:t.classList.add("toast-success")}t.textContent=e,document.body.appendChild(t),this.toasts.push(t),setTimeout(()=>{t.classList.add("show")},10),setTimeout(()=>{t.classList.remove("show"),setTimeout(()=>{document.body.removeChild(t),this.toasts=this.toasts.filter(i=>i!==t)},300)},n)}showSuccess(e,s=3e3){this.showToast(e,r1.SUCCESS,s)}showWarning(e,s=3e3){this.showToast(e,r1.WARNING,s)}showError(e,s=3e3){this.showToast(e,r1.ERROR,s)}};c.\u0275fac=function(s){return new(s||c)},c.\u0275prov=g({token:c,factory:c.\u0275fac,providedIn:"root"});let l=c;return l})();export{N1 as a,n3 as b,es as c,ls as d,is as e,j2 as f,ql as g,G2 as h,xs as i,D0 as j,W5 as k,As as l,_s as m,o1 as n,$e as o,a7 as p,K as q,Z5 as r,Js as s,Qs as t,F1 as u,ul as v,n2 as w,dl as x,ea as y,Cl as z,xl as A,Nl as B,Sl as C,H7 as D,la as E,sa as F,_l as G,Pl as H,El as I,Ol as J,Bl as K,aa as L,na as M,ta as N,r1 as O,q2 as P,U2 as Q,T7 as R,Fa as S,Ea as T};
