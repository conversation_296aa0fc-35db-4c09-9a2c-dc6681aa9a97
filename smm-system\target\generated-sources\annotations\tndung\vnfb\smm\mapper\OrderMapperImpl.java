package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.constant.enums.OrderStatus;
import tndung.vnfb.smm.dto.ServiceLabel;
import tndung.vnfb.smm.dto.request.OrderReq;
import tndung.vnfb.smm.dto.response.OrderRes;
import tndung.vnfb.smm.dto.response.SpecialPriceLiteRes;
import tndung.vnfb.smm.dto.response.SuperOrderRes;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.dto.response.smm.SMMOrderStatusRes;
import tndung.vnfb.smm.dto.response.smm.SMMOrderStatusRes.SMMOrderStatusResBuilder;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GOrder.GOrderBuilder;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.entity.SpecialPrice;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class OrderMapperImpl implements OrderMapper {

    @Autowired
    private ApiProviderMapper apiProviderMapper;
    @Autowired
    private GUserMapper gUserMapper;

    @Override
    public GOrder toEntity(OrderReq req) {
        if ( req == null ) {
            return null;
        }

        GOrderBuilder gOrder = GOrder.builder();

        gOrder.link( req.getLink() );
        gOrder.quantity( req.getQuantity() );

        return gOrder.build();
    }

    @Override
    public OrderRes toRes(GOrder entity) {
        if ( entity == null ) {
            return null;
        }

        OrderRes orderRes = new OrderRes();

        orderRes.setActualCharge( entity.getActualCharge() );
        orderRes.setBalance( entity.getBalance() );
        orderRes.setCharge( entity.getCharge() );
        orderRes.setComments( entity.getComments() );
        orderRes.setCreatedAt( entity.getCreatedAt() );
        if ( entity.getId() != null ) {
            orderRes.setId( entity.getId().intValue() );
        }
        orderRes.setLink( entity.getLink() );
        orderRes.setNote( entity.getNote() );
        orderRes.setQuantity( entity.getQuantity() );
        orderRes.setRemains( entity.getRemains() );
        orderRes.setService( gServiceToServiceRes( entity.getService() ) );
        orderRes.setStartCount( entity.getStartCount() );
        orderRes.setStatus( entity.getStatus() );
        orderRes.setTag( entity.getTag() );
        orderRes.setType( entity.getType() );
        orderRes.setUpdatedAt( entity.getUpdatedAt() );

        return orderRes;
    }

    @Override
    public List<OrderRes> toRes(List<GOrder> entity) {
        if ( entity == null ) {
            return null;
        }

        List<OrderRes> list = new ArrayList<OrderRes>( entity.size() );
        for ( GOrder gOrder : entity ) {
            list.add( toRes( gOrder ) );
        }

        return list;
    }

    @Override
    public SuperOrderRes toSuperRes(GOrder entity) {
        if ( entity == null ) {
            return null;
        }

        SuperOrderRes superOrderRes = new SuperOrderRes();

        superOrderRes.setActualCharge( entity.getActualCharge() );
        superOrderRes.setBalance( entity.getBalance() );
        superOrderRes.setCharge( entity.getCharge() );
        superOrderRes.setComments( entity.getComments() );
        superOrderRes.setCreatedAt( entity.getCreatedAt() );
        if ( entity.getId() != null ) {
            superOrderRes.setId( entity.getId().intValue() );
        }
        superOrderRes.setLink( entity.getLink() );
        superOrderRes.setNote( entity.getNote() );
        superOrderRes.setQuantity( entity.getQuantity() );
        superOrderRes.setRemains( entity.getRemains() );
        superOrderRes.setService( gServiceToServiceRes( entity.getService() ) );
        superOrderRes.setStartCount( entity.getStartCount() );
        superOrderRes.setStatus( entity.getStatus() );
        superOrderRes.setTag( entity.getTag() );
        superOrderRes.setType( entity.getType() );
        superOrderRes.setUpdatedAt( entity.getUpdatedAt() );
        superOrderRes.setApiOrderId( entity.getApiOrderId() );
        superOrderRes.setApiProvider( apiProviderMapper.toRes( entity.getApiProvider() ) );
        superOrderRes.setApiServiceId( entity.getApiServiceId() );
        superOrderRes.setUser( gUserMapper.toApiKey( entity.getUser() ) );

        return superOrderRes;
    }

    @Override
    public List<SuperOrderRes> toSuperRes(List<GOrder> entity) {
        if ( entity == null ) {
            return null;
        }

        List<SuperOrderRes> list = new ArrayList<SuperOrderRes>( entity.size() );
        for ( GOrder gOrder : entity ) {
            list.add( toSuperRes( gOrder ) );
        }

        return list;
    }

    @Override
    public SMMOrderStatusRes toSMM(GOrder entity) {
        if ( entity == null ) {
            return null;
        }

        SMMOrderStatusResBuilder sMMOrderStatusRes = SMMOrderStatusRes.builder();

        sMMOrderStatusRes.status( entityStatusValue( entity ) );
        if ( entity.getCharge() != null ) {
            sMMOrderStatusRes.charge( entity.getCharge().doubleValue() );
        }
        sMMOrderStatusRes.note( entity.getNote() );
        sMMOrderStatusRes.remains( entity.getRemains() );
        sMMOrderStatusRes.startCount( entity.getStartCount() );

        return sMMOrderStatusRes.build();
    }

    protected SpecialPriceLiteRes specialPriceToSpecialPriceLiteRes(SpecialPrice specialPrice) {
        if ( specialPrice == null ) {
            return null;
        }

        SpecialPriceLiteRes specialPriceLiteRes = new SpecialPriceLiteRes();

        if ( specialPrice.getDiscountType() != null ) {
            specialPriceLiteRes.setDiscountType( specialPrice.getDiscountType().name() );
        }
        specialPriceLiteRes.setDiscountValue( specialPrice.getDiscountValue() );
        specialPriceLiteRes.setId( specialPrice.getId() );

        return specialPriceLiteRes;
    }

    protected List<SpecialPriceLiteRes> specialPriceListToSpecialPriceLiteResList(List<SpecialPrice> list) {
        if ( list == null ) {
            return null;
        }

        List<SpecialPriceLiteRes> list1 = new ArrayList<SpecialPriceLiteRes>( list.size() );
        for ( SpecialPrice specialPrice : list ) {
            list1.add( specialPriceToSpecialPriceLiteRes( specialPrice ) );
        }

        return list1;
    }

    protected ServiceRes gServiceToServiceRes(GService gService) {
        if ( gService == null ) {
            return null;
        }

        ServiceRes serviceRes = new ServiceRes();

        serviceRes.setAddType( gService.getAddType() );
        serviceRes.setAverageTime( gService.getAverageTime() );
        serviceRes.setCancelButton( gService.getCancelButton() );
        serviceRes.setDescription( gService.getDescription() );
        if ( gService.getId() != null ) {
            serviceRes.setId( gService.getId().intValue() );
        }
        serviceRes.setIsOverflow( gService.getIsOverflow() );
        List<ServiceLabel> list = gService.getLabels();
        if ( list != null ) {
            serviceRes.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        serviceRes.setMax( gService.getMax() );
        serviceRes.setMin( gService.getMin() );
        serviceRes.setName( gService.getName() );
        serviceRes.setOverflow( gService.getOverflow() );
        serviceRes.setPrice( gService.getPrice() );
        serviceRes.setRefill( gService.getRefill() );
        serviceRes.setRefillDays( gService.getRefillDays() );
        serviceRes.setSampleLink( gService.getSampleLink() );
        serviceRes.setSort( gService.getSort() );
        serviceRes.setSpecialPrices( specialPriceListToSpecialPriceLiteResList( gService.getSpecialPrices() ) );
        serviceRes.setSpeedPerDay( gService.getSpeedPerDay() );
        serviceRes.setType( gService.getType() );

        return serviceRes;
    }

    private String entityStatusValue(GOrder gOrder) {
        if ( gOrder == null ) {
            return null;
        }
        OrderStatus status = gOrder.getStatus();
        if ( status == null ) {
            return null;
        }
        String value = status.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }
}
