package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import tndung.vnfb.smm.constant.Common;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.repository.nontenant.CurrencyRepository;
import tndung.vnfb.smm.service.CurrencyService;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.List;

@Service
@RequiredArgsConstructor
public class CurrencyServiceImpl implements CurrencyService {
    private final CurrencyRepository currencyRepository;

    @Override
    public List<Currency> getAllCurrencies() {
        return currencyRepository.findAll();
    }

    public Currency getCurrencyByCode(String code) {
        return currencyRepository.findById(code)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.CURRENCY_NOT_FOUND));
    }

    public Currency getBaseCurrency() {
        return currencyRepository.findBaseCurrency()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.BASE_CURRENCY_NOT_CONFIGURED));
    }

    @Transactional
    public Currency createCurrency(Currency currency) {
        if (currency.isBaseCurrency()) {
            // If this is a base currency, set all other currencies to non-base
            currencyRepository.findBaseCurrency().ifPresent(baseCurrency -> {
                baseCurrency.setBaseCurrency(false);
                currencyRepository.save(baseCurrency);
            });
        }
        return currencyRepository.save(currency);
    }

    @Transactional
    public Currency updateCurrency(String code, Currency currencyDetails) {
        Currency currency = getCurrencyByCode(code);

        currency.setName(currencyDetails.getName());
        currency.setSymbol(currencyDetails.getSymbol());
        currency.setExchangeRate(currencyDetails.getExchangeRate());

        if (currencyDetails.isBaseCurrency() && !currency.isBaseCurrency()) {
            // If changing to a base currency, update other currencies
            currencyRepository.findBaseCurrency().ifPresent(baseCurrency -> {
                baseCurrency.setBaseCurrency(false);
                currencyRepository.save(baseCurrency);
            });
            currency.setBaseCurrency(true);
            currency.setExchangeRate(BigDecimal.ONE); // Base currency rate is always 1.0
        }

        return currencyRepository.save(currency);
    }

    public void deleteCurrency(String code) {
        Currency currency = getCurrencyByCode(code);
        if (currency.isBaseCurrency()) {
            throw new IllegalStateException("Cannot delete base currency");
        }
        currencyRepository.delete(currency);
    }

    /**
     * Convert amount from one currency to another
     */
    public BigDecimal convert(BigDecimal amount, String fromCurrencyCode, String toCurrencyCode) {
        if (fromCurrencyCode.equals(toCurrencyCode)) {
            return amount;
        }

        Currency fromCurrency = getCurrencyByCode(fromCurrencyCode);
        Currency toCurrency = getCurrencyByCode(toCurrencyCode);

        // Convert to base currency first (if not already base)
        BigDecimal amountInBase;
        if (fromCurrency.isBaseCurrency()) {
            amountInBase = amount;
        } else {
            amountInBase = amount.divide(fromCurrency.getExchangeRate(), Common.SCALE_DEFAULT, BigDecimal.ROUND_HALF_UP);
        }

        // Then convert from base to target currency
        if (toCurrency.isBaseCurrency()) {
            return amountInBase;
        } else {
            return amountInBase.multiply(toCurrency.getExchangeRate());
        }
    }
}
