package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.ServiceLabel;
import tndung.vnfb.smm.dto.request.CurrencyRes;
import tndung.vnfb.smm.dto.response.GUserRes;
import tndung.vnfb.smm.dto.response.SpecialPriceLiteRes;
import tndung.vnfb.smm.dto.response.SpecialPriceRes;
import tndung.vnfb.smm.dto.response.SpecialPriceRes.SpecialPriceResBuilder;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.entity.Currency.CurrencyBuilder;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.SpecialPrice;
import tndung.vnfb.smm.entity.SpecialPrice.DiscountType;
import tndung.vnfb.smm.entity.SpecialPrice.SpecialPriceBuilder;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class SpecialPriceMapperImpl implements SpecialPriceMapper {

    @Autowired
    private GUserMapper gUserMapper;

    @Override
    public SpecialPriceRes toRes(SpecialPrice entity) {
        if ( entity == null ) {
            return null;
        }

        SpecialPriceResBuilder specialPriceRes = SpecialPriceRes.builder();

        if ( entity.getDiscountType() != null ) {
            specialPriceRes.discountType( entity.getDiscountType().name() );
        }
        specialPriceRes.discountValue( entity.getDiscountValue() );
        specialPriceRes.id( entity.getId() );
        specialPriceRes.service( gServiceToServiceRes( entity.getService() ) );
        specialPriceRes.user( gUserMapper.toApiKey( entity.getUser() ) );

        return specialPriceRes.build();
    }

    @Override
    public SpecialPriceLiteRes toLiteRes(SpecialPrice entity) {
        if ( entity == null ) {
            return null;
        }

        SpecialPriceLiteRes specialPriceLiteRes = new SpecialPriceLiteRes();

        if ( entity.getDiscountType() != null ) {
            specialPriceLiteRes.setDiscountType( entity.getDiscountType().name() );
        }
        specialPriceLiteRes.setDiscountValue( entity.getDiscountValue() );
        specialPriceLiteRes.setId( entity.getId() );

        return specialPriceLiteRes;
    }

    @Override
    public List<SpecialPriceLiteRes> toLiteRes(List<SpecialPrice> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SpecialPriceLiteRes> list = new ArrayList<SpecialPriceLiteRes>( entities.size() );
        for ( SpecialPrice specialPrice : entities ) {
            list.add( toLiteRes( specialPrice ) );
        }

        return list;
    }

    @Override
    public List<SpecialPriceRes> toRes(List<SpecialPrice> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SpecialPriceRes> list = new ArrayList<SpecialPriceRes>( entities.size() );
        for ( SpecialPrice specialPrice : entities ) {
            list.add( toRes( specialPrice ) );
        }

        return list;
    }

    @Override
    public SpecialPrice toEntity(SpecialPriceRes dto) {
        if ( dto == null ) {
            return null;
        }

        SpecialPriceBuilder specialPrice = SpecialPrice.builder();

        if ( dto.getDiscountType() != null ) {
            specialPrice.discountType( Enum.valueOf( DiscountType.class, dto.getDiscountType() ) );
        }
        specialPrice.discountValue( dto.getDiscountValue() );
        specialPrice.id( dto.getId() );
        specialPrice.service( serviceResToGService( dto.getService() ) );
        specialPrice.user( gUserResToGUser( dto.getUser() ) );

        return specialPrice.build();
    }

    protected ServiceRes gServiceToServiceRes(GService gService) {
        if ( gService == null ) {
            return null;
        }

        ServiceRes serviceRes = new ServiceRes();

        serviceRes.setAddType( gService.getAddType() );
        serviceRes.setAverageTime( gService.getAverageTime() );
        serviceRes.setCancelButton( gService.getCancelButton() );
        serviceRes.setDescription( gService.getDescription() );
        if ( gService.getId() != null ) {
            serviceRes.setId( gService.getId().intValue() );
        }
        serviceRes.setIsOverflow( gService.getIsOverflow() );
        List<ServiceLabel> list = gService.getLabels();
        if ( list != null ) {
            serviceRes.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        serviceRes.setMax( gService.getMax() );
        serviceRes.setMin( gService.getMin() );
        serviceRes.setName( gService.getName() );
        serviceRes.setOverflow( gService.getOverflow() );
        serviceRes.setPrice( gService.getPrice() );
        serviceRes.setRefill( gService.getRefill() );
        serviceRes.setRefillDays( gService.getRefillDays() );
        serviceRes.setSampleLink( gService.getSampleLink() );
        serviceRes.setSort( gService.getSort() );
        serviceRes.setSpecialPrices( toLiteRes( gService.getSpecialPrices() ) );
        serviceRes.setSpeedPerDay( gService.getSpeedPerDay() );
        serviceRes.setType( gService.getType() );

        return serviceRes;
    }

    protected SpecialPrice specialPriceLiteResToSpecialPrice(SpecialPriceLiteRes specialPriceLiteRes) {
        if ( specialPriceLiteRes == null ) {
            return null;
        }

        SpecialPriceBuilder specialPrice = SpecialPrice.builder();

        if ( specialPriceLiteRes.getDiscountType() != null ) {
            specialPrice.discountType( Enum.valueOf( DiscountType.class, specialPriceLiteRes.getDiscountType() ) );
        }
        specialPrice.discountValue( specialPriceLiteRes.getDiscountValue() );
        specialPrice.id( specialPriceLiteRes.getId() );

        return specialPrice.build();
    }

    protected List<SpecialPrice> specialPriceLiteResListToSpecialPriceList(List<SpecialPriceLiteRes> list) {
        if ( list == null ) {
            return null;
        }

        List<SpecialPrice> list1 = new ArrayList<SpecialPrice>( list.size() );
        for ( SpecialPriceLiteRes specialPriceLiteRes : list ) {
            list1.add( specialPriceLiteResToSpecialPrice( specialPriceLiteRes ) );
        }

        return list1;
    }

    protected GService serviceResToGService(ServiceRes serviceRes) {
        if ( serviceRes == null ) {
            return null;
        }

        GService gService = new GService();

        gService.setAddType( serviceRes.getAddType() );
        gService.setAverageTime( serviceRes.getAverageTime() );
        gService.setCancelButton( serviceRes.getCancelButton() );
        gService.setDescription( serviceRes.getDescription() );
        if ( serviceRes.getId() != null ) {
            gService.setId( serviceRes.getId().longValue() );
        }
        gService.setIsOverflow( serviceRes.getIsOverflow() );
        List<ServiceLabel> list = serviceRes.getLabels();
        if ( list != null ) {
            gService.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        gService.setMax( serviceRes.getMax() );
        gService.setMin( serviceRes.getMin() );
        gService.setName( serviceRes.getName() );
        gService.setOverflow( serviceRes.getOverflow() );
        gService.setPrice( serviceRes.getPrice() );
        gService.setRefill( serviceRes.getRefill() );
        gService.setRefillDays( serviceRes.getRefillDays() );
        gService.setSampleLink( serviceRes.getSampleLink() );
        gService.setSort( serviceRes.getSort() );
        gService.setSpecialPrices( specialPriceLiteResListToSpecialPriceList( serviceRes.getSpecialPrices() ) );
        gService.setSpeedPerDay( serviceRes.getSpeedPerDay() );
        gService.setType( serviceRes.getType() );

        return gService;
    }

    protected Currency currencyResToCurrency(CurrencyRes currencyRes) {
        if ( currencyRes == null ) {
            return null;
        }

        CurrencyBuilder currency = Currency.builder();

        currency.code( currencyRes.getCode() );
        currency.exchangeRate( currencyRes.getExchangeRate() );
        currency.symbol( currencyRes.getSymbol() );

        return currency.build();
    }

    protected GUser gUserResToGUser(GUserRes gUserRes) {
        if ( gUserRes == null ) {
            return null;
        }

        GUser gUser = new GUser();

        gUser.setCreatedAt( gUserRes.getCreatedAt() );
        gUser.setAvatar( gUserRes.getAvatar() );
        gUser.setBalance( gUserRes.getBalance() );
        gUser.setCustomDiscount( gUserRes.getCustomDiscount() );
        gUser.setCustomReferralRate( gUserRes.getCustomReferralRate() );
        gUser.setEmail( gUserRes.getEmail() );
        gUser.setId( gUserRes.getId() );
        gUser.setLastLoginAt( gUserRes.getLastLoginAt() );
        gUser.setMfaEnabled( gUserRes.getMfaEnabled() );
        gUser.setPhone( gUserRes.getPhone() );
        gUser.setPreferredCurrency( currencyResToCurrency( gUserRes.getPreferredCurrency() ) );
        gUser.setStatus( gUserRes.getStatus() );
        gUser.setTotalOrder( gUserRes.getTotalOrder() );
        gUser.setUserName( gUserRes.getUserName() );

        return gUser;
    }
}
