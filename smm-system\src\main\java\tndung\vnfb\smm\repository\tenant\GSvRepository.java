package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.constant.enums.CommonStatus;
import tndung.vnfb.smm.dto.response.TopServiceDTO;
import tndung.vnfb.smm.entity.GService;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface GSvRepository extends TenantAwareRepository<GService, Long> {

    @Query("SELECT s FROM GService s WHERE s.name = :name AND s.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    Optional<GService> findByName(@Param("name") String name);

    @Query("SELECT s FROM GService s WHERE s.id = :id AND s.isDeleted = false AND s.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    Optional<GService> findByIdAndIsDeletedFalse(@Param("id") Long id);

    @Query("SELECT s FROM GService s WHERE s.status = :status AND s.isDeleted = false AND s.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getSiteTenant()}")
    List<GService> findAllSMMByStatus(@Param("status") CommonStatus status);

    @Query("SELECT s FROM GService s WHERE s.category.id = :categoryId AND s.isDeleted = false AND s.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    List<GService> findAllByCategoryId(@Param("categoryId") Long category);

    @Query("SELECT s FROM GService s WHERE s.category.id = :categoryId AND s.isDeleted = false AND s.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY s.sort ASC")
    List<GService> findAllByCategoryIdOrderBySortAsc(@Param("categoryId") Long categoryId);


    @Query(value = "SELECT " +
            "s.id as id, " +
            "s.name as name, " +
            "s.category_id as categoryId, " +
            "COUNT(o.id) as orderCount, " +
            "SUM(o.quantity) as totalQuantity, " +
            "ROUND(COUNT(o.id) * 100.0 / (SELECT COUNT(*) FROM g_order WHERE created_at BETWEEN :startDate" +
            " AND :endDate AND tenant_id = :tenantId), 2) as percentage " +
            "FROM g_service s " +
            "JOIN g_order o ON s.id = o.service_id " +
            "WHERE s.is_deleted = false " +
            "AND s.tenant_id = :tenantId " +
            "AND o.tenant_id = :tenantId " +
            "AND o.created_at BETWEEN :startDate AND :endDate " +
            "GROUP BY s.id, s.name, s.category_id " +
            "ORDER BY orderCount DESC " +
            "LIMIT 5", nativeQuery = true)
    List<TopServiceDTO> findTopOrderedServicesByDateRange(
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("tenantId") String tenantId);
    @Query("SELECT MAX(s.sort) FROM GService s WHERE s.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    Integer findMaxSort();

    @Query("SELECT s FROM GService s WHERE s.apiProvider IS NOT NULL " +
           "AND (s.syncMinMax = true OR s.syncRefill = true OR s.syncCancel = true OR s.syncStatus = true) " +
           "AND s.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    List<GService> findServicesWithSyncFlagsEnabled();

    @Query("SELECT s FROM GService s WHERE s.apiProvider IS NOT NULL AND s.isDeleted = false " +
           "AND (s.syncMinMax = true OR s.syncRefill = true OR s.syncCancel = true OR s.syncStatus = true) ")
    List<GService> findServicesWithSyncFlagsEnabled(Pageable pageable);
}
