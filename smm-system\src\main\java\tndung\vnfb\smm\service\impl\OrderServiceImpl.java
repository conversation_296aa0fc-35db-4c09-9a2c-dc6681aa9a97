package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.json.JSONObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tndung.vnfb.smm.anotation.LogUserActivity;
import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.constant.enums.*;
import tndung.vnfb.smm.dto.CommentOrderSMM;
import tndung.vnfb.smm.dto.OrderSMM;
import tndung.vnfb.smm.dto.request.MyOrderSearchReq;
import tndung.vnfb.smm.dto.request.OrderReq;
import tndung.vnfb.smm.dto.request.SearchOrderReq;
import tndung.vnfb.smm.dto.response.OrderRes;
import tndung.vnfb.smm.dto.response.SuperOrderRes;
import tndung.vnfb.smm.dto.response.smm.SMMOrderRes;
import tndung.vnfb.smm.dto.response.smm.SMMOrderStatusRes;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.entity.*;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.helper.CommonHelper;
import tndung.vnfb.smm.mapper.OrderMapper;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.tenant.OrderRepository;
import tndung.vnfb.smm.repository.tenant.SpecialPriceRepository;
import tndung.vnfb.smm.rest.SMMConsumer;
import tndung.vnfb.smm.service.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {
    private final GSvService gSvService;
    private final OrderRepository orderRepository;
    private final AuthenticationFacade authenticationFacade;
    private final OrderMapper orderMapper;
    private final SMMConsumer smmConsumer;
    private final GUserRepository gUserRepository;
    private final SpecialPriceRepository specialPriceRepository;
    private final VoucherService voucherService;
    private final BalanceService balanceService;

    private static final BigDecimal DEFAULT_ITEMS_RATE = new BigDecimal(1000);


    @Override
    public SMMOrderStatusRes getSSMOrderStatus(Long id) {
        final GOrder order = findById(id);
        return orderMapper.toSMM(order);
    }

    @Override
    public Map<String, SMMOrderStatusRes> getMultipleSSMOrderStatus(String orders) {
        List<Long> orderList = Arrays.stream(orders.split(",")).map(Long::parseLong).collect(Collectors.toList());
        final List<GOrder> ordersEntity = orderRepository.findByIdIn(orderList);

        return ordersEntity.stream().collect(Collectors.toMap(o -> String.valueOf(o.getId()), orderMapper::toSMM));
    }

    @Override
    @Transactional
    @LogUserActivity(operation = "CREATE", entity = "OrderReq")
    public OrderRes add(OrderReq req) {
        log.info("Creating new order for service: {}, quantity: {}", req.getServiceId(), req.getQuantity());

        // Step 1: Validate request and get entities
        OrderCreationContext context = validateAndPrepareOrder(req);

        // Step 2: Calculate total price with all discounts
        BigDecimal totalPrice = calculateOrderPrice(context.service(), context.user(), context.totalQuantity(), req.getVoucherCode());

        // Step 3: Validate sufficient balance
        validateSufficientBalance(context.user(), totalPrice);

        // Step 4: Create order entity
        GOrder orderEntity = createOrderEntity(req, context, totalPrice);

        // Step 5: Process API integration if needed
        processApiOrder(orderEntity, context.service(), req);

        // Step 6: Finalize order (save, deduct balance, use voucher)
        GOrder savedOrder = finalizeOrder(orderEntity, context.user(), totalPrice, context.voucher());

        log.info("Order created successfully: orderId={}, amount={}", savedOrder.getId(), totalPrice);

        return orderMapper.toRes(savedOrder);
    }


    /**
     * Context record to hold order creation data
     */
    private record OrderCreationContext(
            GService service,
            GUser user,
            Integer totalQuantity,
            String sanitizedLink,
            Currency currency,
            Voucher voucher
    ) {}

    /**
     * Step 1: Validate request and prepare order context
     */
    private OrderCreationContext validateAndPrepareOrder(OrderReq req) {
        log.debug("Validating and preparing order for service: {}", req.getServiceId());

        // Get service and validate
        final GService service = gSvService.getById(req.getServiceId());

        // Sanitize link
        final String sanitizedLink = req.getLink().replaceAll("<.*?>", "");

        // Calculate total quantity based on service type
        Integer totalQuantity = calculateTotalQuantity(req, service);

        // Get current user
        final GUser user = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Get user's preferred currency
        Currency currency = user.getPreferredCurrency();

        // Validate and get voucher if provided
        Voucher voucher = null;
        if (!Strings.isBlank(req.getVoucherCode())) {
            voucher = voucherService.checkValidVoucherOffer(user, req.getVoucherCode());
        }

        return new OrderCreationContext(service, user, totalQuantity, sanitizedLink, currency, voucher);
    }

    /**
     * Step 2: Calculate total price with all applicable discounts
     */
    private BigDecimal calculateOrderPrice(GService service, GUser user, Integer totalQuantity, String voucherCode) {
        log.debug("Calculating order price for service: {}, quantity: {}", service.getId(), totalQuantity);

        // Base price calculation
        BigDecimal basePrice = calculateBasePrice(service);

        // Apply voucher discount if applicable
        if (!Strings.isBlank(voucherCode)) {
            Voucher voucher = voucherService.checkValidVoucherOffer(user, voucherCode);
            if (voucher != null) {
                basePrice = CommonHelper.discount(basePrice, voucher.getDiscountValue());
            }
        }

        // Apply special price or custom discount
        basePrice = applyUserSpecificDiscounts(basePrice, user, service);

        // Apply quantity multiplier for non-package services
        if (!ServiceType.Package.equals(service.getType())) {
            basePrice = basePrice.multiply(new BigDecimal(totalQuantity))
                    .divide(DEFAULT_ITEMS_RATE, RoundingMode.HALF_EVEN);
        }

        log.debug("Calculated total price: {} for quantity: {}", basePrice, totalQuantity);
        return basePrice;
    }

    /**
     * Calculate base price based on service pricing model
     */
    private BigDecimal calculateBasePrice(GService service) {
        if (Boolean.TRUE.equals(service.getIsFixedPrice())) {
            return service.getPrice();
        } else {
            return CommonHelper.extend(service.getOriginalPrice(), service.getPercent());
        }
    }

    /**
     * Apply user-specific discounts (special prices or custom discount)
     */
    private BigDecimal applyUserSpecificDiscounts(BigDecimal price, GUser user, GService service) {
        final Optional<SpecialPrice> specialPriceOpt = specialPriceRepository.findByUserAndService(user, service);

        if (specialPriceOpt.isPresent()) {
            final SpecialPrice specialPrice = specialPriceOpt.get();
            if (SpecialPrice.DiscountType.FIXED.equals(specialPrice.getDiscountType())) {
                return specialPrice.getDiscountValue();
            } else {
                return CommonHelper.discount(price, specialPrice.getDiscountValue());
            }
        } else {
            return CommonHelper.discount(price, user.getCustomDiscount());
        }
    }

    /**
     * Step 3: Validate user has sufficient balance
     */
    private void validateSufficientBalance(GUser user, BigDecimal totalPrice) {
        if (!balanceService.hasSufficientBalance(user, totalPrice)) {
            log.warn("Insufficient balance for user: {}, required: {}, available: {}",
                    user.getId(), totalPrice, user.getBalance());
            throw new InvalidParameterException(IdErrorCode.BALANCE_NOT_ENOUGH);
        }
    }

    /**
     * Step 4: Create order entity
     */
    private GOrder createOrderEntity(OrderReq req, OrderCreationContext context, BigDecimal totalPrice) {
        log.debug("Creating order entity for service: {}", context.service().getId());

        final OrderType orderType = context.service().getApiProvider() != null ? OrderType.Api : OrderType.Direct;

        return GOrder.builder()
                .charge(totalPrice)
                .actualCharge(totalPrice) // Initialize actual charge with original charge
                .quantity(context.totalQuantity())
                .price(context.service().getPrice())
                .link(context.sanitizedLink())
                .service(context.service())
                .currency(context.currency())
                .type(orderType)
                .apiProvider(context.service().getApiProvider())
                .user(context.user())
                .apiServiceId(context.service().getApiServiceId())
                .build();
    }

    /**
     * Step 5: Process API integration for external orders
     */
    private void processApiOrder(GOrder orderEntity, GService service, OrderReq req) {
        if (!OrderType.Api.equals(orderEntity.getType())) {
            log.debug("Order is not API type, skipping API processing");
            return;
        }

        log.debug("Processing API order for service: {}", service.getId());

        try {
            final SMMOrderRes apiResponse = callExternalApi(service, req, orderEntity);

            String apiOrderId = String.valueOf(apiResponse.getOrder());
            orderEntity.setApiOrderId(apiOrderId);

            // Fetch initial status from API
            fetchStatus(orderEntity);

            log.debug("API order created successfully: apiOrderId={}", apiOrderId);

        } catch (Exception e) {
            log.error("Failed to create API order for service: {}, error: {}", service.getId(), e.getMessage(), e);
            orderEntity.setStatus(OrderStatus.FAILED);
        }
    }

    /**
     * Call external API based on service type
     */
    private SMMOrderRes callExternalApi(GService service, OrderReq req, GOrder orderEntity) {
        return switch (service.getType()) {
            case Default -> smmConsumer.addOrderDefault(
                    service.getApiProvider(),
                    OrderSMM.builder()
                            .apiServiceId(service.getApiServiceId())
                            .link(orderEntity.getLink())
                            .quantity(orderEntity.getQuantity())
                            .build()
            );
            case Comment -> smmConsumer.addOrderComments(
                    service.getApiProvider(),
                    CommentOrderSMM.builder()
                            .apiServiceId(service.getApiServiceId())
                            .link(orderEntity.getLink())
                            .comments(String.join("\n", req.getComments()))
                            .build()
            );
            default -> new SMMOrderRes(); // For Package or other types
        };
    }

    /**
     * Step 6: Finalize order (save, deduct balance, use voucher)
     */
    private GOrder finalizeOrder(GOrder orderEntity, GUser user, BigDecimal totalPrice, Voucher voucher) {
        log.debug("Finalizing order for user: {}, amount: {}", user.getId(), totalPrice);

        // Update user's total order count
        user.setTotalOrder(user.getTotalOrder() + 1);
        gUserRepository.save(user);

        // Update base charge and save order
        orderEntity.updateBaseCharge();
        final GOrder savedOrder = orderRepository.save(orderEntity);

        // Deduct balance with transaction logging
        String orderNote = String.format("Order created: %s (ID: %d, Quantity: %d, Amount: %s)",
                orderEntity.getService().getName(), savedOrder.getId(), orderEntity.getQuantity(), totalPrice);
        GUser gUser = balanceService.deductBalance(user, totalPrice, TransactionSource.ADD_ORDER, orderNote, savedOrder);
        savedOrder.setBalance(gUser.getBalance());
        // Use voucher if provided
        if (voucher != null) {
            voucherService.useVoucher(user, voucher, savedOrder);
            log.debug("Voucher used: {}", voucher.getCode());
        }

        return savedOrder;
    }

    /**
     * Calculate total quantity based on service type and request
     */
    private Integer calculateTotalQuantity(OrderReq req, GService service) {
        Integer totalQuantity = req.getQuantity();

        switch (service.getType()) {
            case Comment:
                if (CollectionUtils.isEmpty(req.getComments())) {
                    throw new InvalidParameterException(IdErrorCode.RESOURCE_ERROR);
                }
                totalQuantity = req.getComments().size();
                break;
            case Default:
            case Package:
                // Use the provided quantity
                break;
        }

        // Validate quantity against service limits
        if (totalQuantity < service.getMin() || totalQuantity > service.getMax()) {
            throw new InvalidParameterException(IdErrorCode.QUANTITY_NOT_VALID);
        }

        return totalQuantity;
    }

    @Override
    public OrderRes cancel(Long id) {
        final GOrder order = findById(id);

        log.info("Updating order to CANCEL: orderId={}", order.getId());

        // Call external API to cancel the order
        try {
            smmConsumer.cancel(order.getApiProvider(), id.toString());
//            log.info((String) o);
        } catch (Exception e) {
            // Log exception for debugging with full stack trace
            log.error("Error cancelling order " + id + ": " + e.getMessage(), e);
          //  throw new InvalidParameterException(IdErrorCode.CANNOT_CANCEL_ORDER);
        }

        // Set order status to CANCELED and set CANCEL tag
        //order.setStatus(OrderStatus.CANCELED);
        order.setTag(OrderTag.CANCEL);

        GOrder savedOrder = orderRepository.save(order);
        return orderMapper.toRes(savedOrder);
    }

    @Override
    @Transactional
    public OrderRes refill(Long id) {
        final GOrder order = findById(id);
        final GService service = order.getService();

        log.info("Processing refill request for order: {}", id);

        // Validate service supports refill
        if (!Boolean.TRUE.equals(service.getRefill())) {
            log.warn("Service does not support refill: serviceId={}, orderId={}", service.getId(), id);
            throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
        }

        // Validate order status (must be COMPLETED or PARTIAL)
        if (!OrderStatus.COMPLETED.equals(order.getStatus()) && !OrderStatus.PARTIAL.equals(order.getStatus()) ) {
            log.warn("Order status is not COMPLETED or PARTIAL: orderId={}, status={}", id, order.getStatus());
            throw new InvalidParameterException(IdErrorCode.INVALID_STATUS_TRANSITION);
        }

        // Validate refill time window (order creation date + refill_days)
        if (service.getRefillDays() != null && service.getRefillDays() > 0) {
            OffsetDateTime orderCreatedAt = order.getCreatedAt();
            OffsetDateTime refillDeadline = orderCreatedAt.plusDays(service.getRefillDays());
            OffsetDateTime now = OffsetDateTime.now();

            if (now.isAfter(refillDeadline)) {
                log.warn("Refill deadline exceeded: orderId={}, createdAt={}, deadline={}, now={}",
                        id, orderCreatedAt, refillDeadline, now);
                throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
            }
        }

        // Call external API to process refill
        try {
            smmConsumer.refill(order.getApiProvider(), id.toString());

            // Set refill tag
            order.setTag(OrderTag.REFILL);

            GOrder savedOrder = orderRepository.save(order);

            log.info("Refill processed successfully: orderId={}", id);

            return orderMapper.toRes(savedOrder);

        } catch (Exception e) {
            log.error("Error processing refill for order {}: {}", id, e.getMessage(), e);
            throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
        }
    }



    @Override
    @Transactional
    public OrderRes updateOrderStatus(Long id, OrderStatus orderStatus, Integer remains) {
        // Validate status transition first
        final GOrder order = findById(id);
        if (!isValidStatusTransition(order.getStatus(), orderStatus)) {
            throw new InvalidParameterException(IdErrorCode.INVALID_STATUS_TRANSITION);
        }

        // Delegate to specific status update methods
        return switch (orderStatus) {
            case COMPLETED -> updateToCompleted(id);
            case PARTIAL -> updateToPartial(id, remains);
            case CANCELED -> updateToCancel(id);
            case IN_PROGRESS -> updateToInProgress(id);
            case PENDING -> updateToPending(id);
            case PROCESSING -> updateToProcessing(id);
            case FAILED -> updateToFailed(id);
            default -> throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
        };
    }

    @Override
    @Transactional
    public OrderRes updateToCompleted(Long id) {
        final GOrder order = findById(id);

        log.info("Updating order to COMPLETED: orderId={}", order.getId());

        order.setStatus(OrderStatus.COMPLETED);
        order.setRemains(0);

        GOrder savedOrder = orderRepository.save(order);

        log.info("Order updated to COMPLETED: orderId={}", savedOrder.getId());

        return orderMapper.toRes(savedOrder);
    }

    @Override
    @Transactional
    public OrderRes updateToPartial(Long id, Integer remains) {
        final GOrder order = findById(id);

        log.info("Updating order to PARTIAL: orderId={}, currentRemains={}, newRemains={}",
                order.getId(), order.getRemains(), remains);

        // Validate remains parameter
        if (remains == null || remains > order.getQuantity()) {
            throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
        }

        // Validate that order has remains to refund
        if (remains <= 0) {
            log.warn("Cannot set order to PARTIAL: no remains. OrderId={}, remains={}", order.getId(), remains);
            throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
        }

        // Set order status to PARTIAL and update remains
        order.setStatus(OrderStatus.PARTIAL);
        order.setRemains(remains);

        GOrder savedOrder = orderRepository.save(order);

        // Process refund using BalanceService (this will handle the intelligent calculation)
        balanceService.processRefund(savedOrder);

        log.info("Order updated to PARTIAL with refund processed: orderId={}, finalRemains={}",
                savedOrder.getId(), savedOrder.getRemains());

        return orderMapper.toRes(savedOrder);
    }



    @Override
    @Transactional
    public OrderRes updateToCancel(Long id) {
        final GOrder order = findById(id);

        log.info("Updating order to CANCEL: orderId={}", order.getId());

        // Call external API to cancel the order
        try {
            smmConsumer.cancel(order.getApiProvider(), id.toString());
        } catch (Exception e) {
            // Log exception for debugging with full stack trace
            log.error("Error cancelling order " + id + ": " + e.getMessage(), e);
            throw new InvalidParameterException(IdErrorCode.CANNOT_CANCEL_ORDER);
        }

        // Set order status to CANCELED and set CANCEL tag
        order.setStatus(OrderStatus.CANCELED);
      //  order.setTag(OrderTag.CANCEL);

        GOrder savedOrder = orderRepository.save(order);
        balanceService.processRefund(savedOrder);
        log.info("Order updated to CANCELED: orderId={}", savedOrder.getId());

        return orderMapper.toRes(savedOrder);
    }

    @Override
    @Transactional
    public OrderRes updateToInProgress(Long id) {
        final GOrder order = findById(id);

        log.info("Updating order to IN_PROGRESS: orderId={}", order.getId());

        order.setStatus(OrderStatus.IN_PROGRESS);

        GOrder savedOrder = orderRepository.save(order);

        log.info("Order updated to IN_PROGRESS: orderId={}", savedOrder.getId());

        return orderMapper.toRes(savedOrder);
    }

    @Override
    @Transactional
    public OrderRes updateToPending(Long id) {
        final GOrder order = findById(id);

        log.info("Updating order to PENDING: orderId={}", order.getId());

        order.setStatus(OrderStatus.PENDING);

        GOrder savedOrder = orderRepository.save(order);

        log.info("Order updated to PENDING: orderId={}", savedOrder.getId());

        return orderMapper.toRes(savedOrder);
    }

    @Override
    @Transactional
    public OrderRes updateToProcessing(Long id) {
        final GOrder order = findById(id);

        log.info("Updating order to PROCESSING: orderId={}", order.getId());

        order.setStatus(OrderStatus.PROCESSING);

        GOrder savedOrder = orderRepository.save(order);

        log.info("Order updated to PROCESSING: orderId={}", savedOrder.getId());

        return orderMapper.toRes(savedOrder);
    }

    @Override
    @Transactional
    public OrderRes updateToFailed(Long id) {
        final GOrder order = findById(id);

        log.info("Updating order to FAILED: orderId={}", order.getId());

        order.setStatus(OrderStatus.FAILED);

        GOrder savedOrder = orderRepository.save(order);

        log.info("Order updated to FAILED: orderId={}", savedOrder.getId());

        return orderMapper.toRes(savedOrder);
    }

    /**
     * Validates if a status transition is allowed based on business rules.
     * <p>
     * Status transition rules:
     * - IN_PROGRESS & PENDING: Can change to → COMPLETED, PARTIAL, CANCELED_WITHOUT_REFUND, CANCELED
     * - COMPLETED: Can change to → IN_PROGRESS, PARTIAL, CANCELED_WITHOUT_REFUND, CANCELED
     * - CANCELED & FAILED: Cannot change status (no transitions allowed)
     * - PARTIAL: Can change to → IN_PROGRESS, PARTIAL, CANCELED_WITHOUT_REFUND, CANCELED
     * - CANCELED_WITHOUT_REFUND: Cannot change status (no transitions allowed, like CANCELED)
     *
     * @param currentStatus The current order status
     * @param newStatus     The target status to transition to
     * @return true if the transition is valid, false otherwise
     */
    public boolean isValidStatusTransition(OrderStatus currentStatus, OrderStatus newStatus) {
        // If statuses are the same, allow (no actual change)
        if (currentStatus == newStatus) {
            return true;
        }

        // Null checks
        if (currentStatus == null || newStatus == null) {
            return false;
        }

        // Define allowed transitions based on current status
        return switch (currentStatus) {
            case IN_PROGRESS, PENDING ->
                // Can change to: COMPLETED, PARTIAL, CANCELED
                    newStatus == OrderStatus.COMPLETED ||
                            newStatus == OrderStatus.PARTIAL ||
                            newStatus == OrderStatus.CANCELED;
            case COMPLETED ->
                // Can change to: IN_PROGRESS, PARTIAL, CANCELED
                    newStatus == OrderStatus.IN_PROGRESS ||
                            newStatus == OrderStatus.PARTIAL ||
                            newStatus == OrderStatus.CANCELED;
            case PARTIAL ->
                // Can change to: IN_PROGRESS, PARTIAL, CANCELED
                    newStatus == OrderStatus.IN_PROGRESS ||
                            newStatus == OrderStatus.PARTIAL ||
                            newStatus == OrderStatus.CANCELED;
            case CANCELED, FAILED ->
                // Cannot change status (no transitions allowed)
                    false;
            case PROCESSING ->
                // PROCESSING can transition to any status (flexible for system operations)
                    true;
            default ->
                // Unknown status - deny transition for safety
                    false;
        };
    }

    @Override
    public OrderRes changeCancel(Long id) {
        final GOrder order = findById(id);
        order.setRemains(0);
        order.setStatus(OrderStatus.CANCELED);

        orderRepository.save(order);
        return null;
    }

    @Override
    public OrderRes updateApiOderId(Long id, String orderId) {
        final GOrder order = findById(id);
        order.setApiOrderId(orderId);
        return orderMapper.toRes(orderRepository.save(order));
    }

    @Override
    public OrderRes updateStartCount(Long id, Integer startCount) {
        final GOrder order = findById(id);
        order.setStartCount(startCount);
        return orderMapper.toRes(orderRepository.save(order));
    }

    @Override
    public OrderRes updateLink(Long id, String link) {
        final GOrder order = findById(id);
        order.setLink(link);
        return orderMapper.toRes(orderRepository.save(order));
    }

    @Override
    public OrderRes updateNote(Long id, String note) {
        final GOrder order = findById(id);
        order.setNote(note);
        return orderMapper.toRes(orderRepository.save(order));
    }


    @Override
    public Page<OrderRes> getOrdersByUser(Long userId, Pageable pageable) {
        return null;
    }

    @Override
    public Page<SuperOrderRes> search(SearchOrderReq req, Pageable pageable) {


        SearchParams params = buildSearchParams(req.getKeyword(), req.getFrom(), req.getTo());
        Page<GOrder> orders = orderRepository.searchOrders(
                params.processedLink,
                params.orderId,
                req.getApiProviderId(),
                req.getCategoryId(),
                req.getServiceId(),
                params.from,
                params.to,
                req.getStatus(),
                req.getUserId(),
                pageable
        );
        return mapToPage(orders, orderMapper::toSuperRes, pageable);
    }

    @Override
    public Page<OrderRes> searchMyOrder(MyOrderSearchReq req, Pageable pageable) {
        GUser gUser = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));
        SearchParams params = buildSearchParams(req.getKeyword(), req.getFrom(), req.getTo());
        Page<GOrder> orders = orderRepository.searchMyOrders(
                gUser.getId(),
                params.processedLink,
                params.orderId,
                req.getServiceId(),
                req.getCategoryId(),
                params.from,
                params.to,
                req.getStatus(),
                pageable
        );
        return mapToPage(orders, orderMapper::toRes, pageable);
    }

    /**
     * Builds search parameters from keyword and date range.
     */
    private SearchParams buildSearchParams(String keyword, LocalDate from, LocalDate to) {
        ZoneId userTimezone = AuditContextHolder.getUserZone();
        OffsetDateTime startDate = CommonHelper.getStartDay(from, userTimezone);
        OffsetDateTime endDate = CommonHelper.getEndDay(to, userTimezone);

        Long orderId = null;
        String processedLink = null;
        if (CommonHelper.isNumeric(keyword)) {
            orderId = Long.parseLong(keyword);
        } else if (keyword != null) {
            processedLink = keyword.replaceAll("/+$", "");
        }

        return new SearchParams(orderId, processedLink, startDate, endDate);
    }

    /**
     * Maps a Page of GOrder to a Page of the target response type.
     */
    private <T> Page<T> mapToPage(Page<GOrder> orders, Function<List<GOrder>, List<T>> mapper, Pageable pageable) {
        List<T> resultList = mapper.apply(orders.getContent());
        return new PageImpl<>(resultList, pageable, orders.getTotalElements());
    }

    /**
     * Data class to hold search parameters.
     */
    private record SearchParams(Long orderId, String processedLink, OffsetDateTime from, OffsetDateTime to) {
    }

    @Override
    public GOrder findById(Long id) {
        return orderRepository.findById(id).orElseThrow(() -> new InvalidParameterException(IdErrorCode.ORDER_NOT_FOUND));
    }

    @Override
    public GOrder fetchStatus(GOrder order) {
        final JSONObject orderStatus = smmConsumer.getOrderStatus(order.getApiProvider(), order.getApiOrderId());

        order.setStartCount(orderStatus.getInt("start_count"));
        order.setStatus(OrderStatus.findByValue(orderStatus.getString("status")));
        order.setRemains(orderStatus.getInt("remains"));
        return order;
    }

    @Override
    public OrderRes fetchStatus(Long orderId) {
        GOrder order = findById(orderId);
        order = fetchStatus(order);
        return orderMapper.toRes(orderRepository.save(order));
    }


}
