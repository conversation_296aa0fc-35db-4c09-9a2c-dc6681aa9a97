import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { IconDropdownComponent } from "../../common/icon-dropdown/icon-dropdown.component";
import { ServiceLabelComponent } from "../../common/service-label/service-label.component";
import { SocialIconComponent } from "../../common/social-icon/social-icon.component";
import { TimeFormatPipe } from '../../../core/pipes/time.pipe';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { SuperCategoryRes } from '../../../model/response/super-category.model';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { ToggleSwitchComponent } from "../../common/toggle-switch/toggle-switch.component";
import { ClickOutsideDirective } from '../../../shared/directives/click-outside.directive';
import { LoadingComponent } from '../../common/loading/loading.component';
import { LoadingService } from '../../../core/services/loading.service';

import { AddPlatformLightComponent } from '../../popup/add-platform-light/add-platform-light.component';
import { PlatformManagementComponent } from '../../platform-management/platform-management.component';
import { CategorySelectionComponent } from '../../popup/category-selection/category-selection.component';
import { NewCategoryComponent } from '../../popup/new-category/new-category.component';
import { NewPricesComponent } from '../../popup/new-prices/new-prices.component';
import { NewSpecialPricesComponent } from '../../popup/new-special-prices/new-special-prices.component';
import { SpecialPricesUserComponent } from '../../popup/special-prices-user/special-prices-user.component';
import { SpecialPricesServiceComponent } from '../../popup/special-prices-service/special-prices-service.component';
import { ImportServicesComponent } from '../../popup/import-services/import-services.component';
import { ResourcesComponent } from '../../popup/resources/resources.component';
import { DeleteConfirmationComponent } from '../../settings/delete-confirmation/delete-confirmation.component';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { CategoryService } from '../../../core/services/category.service';
import { ServiceManagementService } from '../../../core/services/service-management.service';
import { FilterService } from '../../../core/services/filter.service';
import { UIStateService } from '../../../core/services/ui-state.service';
import { PlatformService } from '../../../core/services/platform.service';
import { SelectionService } from '../../../core/services/selection.service';
import { ExtendedIconBaseModel } from '../../../model/extended/extended-icon-base.model';
import { ExtendedCategoryRes } from '../../../model/extended/extended-category.model';
import { SpecialPriceService } from '../../../core/services/special-price.service';

import { CdkDragDrop, CdkDragStart, moveItemInArray, CdkDrag, CdkDropList, CdkDragHandle, CdkDragPreview } from '@angular/cdk/drag-drop';
import { NewServiceComponent } from '../../popup/new-service/new-service.component';
import { AddType } from '../../popup/new-service/new-service.component';
import { AdminMenuComponent, AdminMenuItem } from '../../../components/common/admin-menu/admin-menu.component';
import { IconsModule } from '../../../icons/icons.module';
import { ToastService } from '../../../core/services/toast.service';
import { DragDropService } from '../../../core/services/drag-drop.service';
@Component({
  selector: 'app-admin-service',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    IconDropdownComponent,
    ServiceLabelComponent,
    SocialIconComponent,
    TimeFormatPipe,
    NewServiceComponent,
    AddPlatformLightComponent,
    PlatformManagementComponent,
    CategorySelectionComponent,
    NewCategoryComponent,
    NewPricesComponent,
    NewSpecialPricesComponent,
    SpecialPricesUserComponent,
    SpecialPricesServiceComponent,
    ImportServicesComponent,
    ResourcesComponent,
    DeleteConfirmationComponent,
    AdminMenuComponent,
    CdkDrag,
    CdkDropList,
    CdkDragHandle,
    CdkDragPreview
],
  templateUrl: './admin-service.component.html',
  styleUrls: ['./admin-service.component.css', '../common/admin-menu.css']
})
export class AdminServiceComponent implements OnInit, OnDestroy {
    allPlatforms: SuperPlatformRes[] = [];

    // View mode for mobile/desktop
    viewMode: 'table' | 'card' = 'table'; // Default to table view, toggle to card view for mobile

    // Category platform dropdown properties
    categoryPlatformSelections: Map<number, Set<number>> = new Map<number, Set<number>>();
    get showCategoryPlatformDropdown(): boolean { return this.uiStateService.showCategoryPlatformDropdown; }
    get selectedCategoryForPlatform(): ExtendedCategoryRes | null { return this.uiStateService.selectedCategoryForPlatform; }
    get categoryPlatformMenuPosition(): { top: number, left: number } { return this.uiStateService.categoryPlatformMenuPosition; }

    // Category dropdown options
    categoryOptions: ExtendedIconBaseModel[] = [];
    selectedCategory: ExtendedIconBaseModel | undefined;

    // Currently displayed category
    currentCategory: ExtendedCategoryRes | null = null;

    // Menu visibility
    get showBulkActionMenu(): boolean { return this.uiStateService.showBulkActionMenu; }
    get showServiceActionMenu(): boolean { return this.uiStateService.showServiceActionMenu; }
    get showCategoryActionMenu(): boolean { return this.uiStateService.showCategoryActionMenu; }

    // Selected items for actions
    get selectedServiceForAction(): SuperGeneralSvRes | null { return this.uiStateService.selectedServiceForAction; }
    get selectedCategoryForAction(): ExtendedCategoryRes | null { return this.uiStateService.selectedCategoryForAction; }
    get categoryToEdit(): any | null { return this.uiStateService.categoryToEdit; }
    set categoryToEdit(value: any | null) { this.uiStateService.categoryToEdit = value; }
    get specialPriceToEdit(): any | null { return this.uiStateService.specialPriceToEdit; }

    // Menu positions
    get menuPosition(): { top: number, left: number } { return this.uiStateService.menuPosition; }
    get categoryMenuPosition(): { top: number, left: number } { return this.uiStateService.categoryMenuPosition; }

    // Loading states
    get loading(): boolean { return this.uiStateService.loading; }
    get loadingBulkOperation(): boolean { return this.uiStateService.loadingBulkOperation; }
    get bulkOperationMessage(): string { return this.uiStateService.bulkOperationMessage; }

    // Drag and drop state for services
    draggedService: SuperGeneralSvRes | null = null;
    draggedServiceIndex: number = -1;
    draggedServiceCategoryId: string = '';
    dropTargetCategoryId: string = '';
    dropTargetIndex: number = -1;

    // Drag and drop state for categories
    draggedCategory: ExtendedCategoryRes | null = null;
    draggedCategoryIndex: number = -1;
    dropTargetCategoryIndex: number = -1;

    constructor(
      private adminService: AdminServiceService,
      private categoryService: CategoryService,
      private serviceManagementService: ServiceManagementService,
      private dragDropService: DragDropService,
      private filterService: FilterService,
      public uiStateService: UIStateService,
      private platformService: PlatformService,
      public selectionService: SelectionService,
      private specialPriceService: SpecialPriceService,
      private loadingService: LoadingService,
      private toastService: ToastService
    ) {}

    // Handle document clicks to close the menus
    @HostListener('document:click', ['$event'])
    handleDocumentClick(event: MouseEvent): void {
      const target = event.target as HTMLElement;
      const isMenuButton = target.closest('.action-menu-button');
      const isMenuContent = target.closest('.dropdown-menu');
      const isPlatformButton = target.closest('.bg-blue-500.rounded-full');
      const isPlatformContent = target.closest('.platform-dropdown-content');

      // Close bulk action menu if open and click is outside
      if (this.uiStateService.showBulkActionMenu && !isMenuButton && !isMenuContent) {
        this.uiStateService.closeBulkActionMenu();
      }

      // Close service action menu if open and click is outside
      if (this.uiStateService.showServiceActionMenu && !isMenuButton && !isMenuContent) {
        this.uiStateService.closeServiceActionMenu();
      }

      // Close category action menu if open and click is outside
      if (this.uiStateService.showCategoryActionMenu && !isMenuButton && !isMenuContent) {
        this.uiStateService.closeCategoryActionMenu();
      }

      // Close category platform dropdown if open and click is outside
      if (this.uiStateService.showCategoryPlatformDropdown && !isPlatformButton && !isPlatformContent) {
        this.uiStateService.closeCategoryPlatformDropdown();
      }
    }

    // Removed scroll handlers to keep menus open during scrolling, similar to admin-support.component.ts

    // Modal visibility is now managed by UIStateService
    get showModal(): boolean { return this.uiStateService.showModal; }
    get showAddServiceV2Modal(): boolean { return this.uiStateService.showAddServiceV2Modal; }
    get showAddPlatformLightModal(): boolean { return this.uiStateService.showAddPlatformLightModal; }
    get showPlatformManagementModal(): boolean { return this.uiStateService.showPlatformManagementModal; }
    get showCategorySelectionModal(): boolean { return this.uiStateService.showCategorySelectionModal; }
    get showNewCategoryModal(): boolean { return this.uiStateService.showNewCategoryModal; }
    get showNewPricesModal(): boolean { return this.uiStateService.showNewPricesModal; }
    get showNewSpecialPricesModal(): boolean { return this.uiStateService.showNewSpecialPricesModal; }
    get showSpecialPricesUserModal(): boolean { return this.uiStateService.showSpecialPricesUserModal; }
    get showSpecialPricesServiceModal(): boolean { return this.uiStateService.showSpecialPricesServiceModal; }
    get showImportServicesModal(): boolean { return this.uiStateService.showImportServicesModal; }
    get showNewServiceModal(): boolean { return this.uiStateService.showNewServiceModal; }

  openModal() {
    this.uiStateService.openModal();
  }

  closeModal() {
    this.uiStateService.closeModal();
  }

  closeAddServiceV2Modal() {
    this.uiStateService.closeAddServiceV2Modal();
    // Refresh services list after adding a new service
    this.loadPlatforms();
  }

  closeAddPlatformLightModal() {
    this.uiStateService.closeAddPlatformLightModal();
    // Refresh platforms list after adding a new platform
    this.loadPlatforms();
  }

  openPlatformManagementModal() {
    this.uiStateService.openPlatformManagementModal();
  }

  closePlatformManagementModal() {
    this.uiStateService.closePlatformManagementModal();
    // Refresh platforms list after managing platforms
    this.loadPlatforms();
  }

  onPlatformsUpdated(platforms: SuperPlatformRes[]) {
    console.log('Platforms updated:', platforms);
    // Refresh platforms list after updating platforms
    this.loadPlatforms();
  }

  onPlatformAdded(platform: any) {
    console.log('Platform added:', platform);
    // Refresh platforms list after adding a new platform
    this.loadPlatforms();
  }

    ngOnInit(): void {
      this.loadPlatforms();
      this.detectMobileDevice();

      // Listen for window resize events to update view mode
      window.addEventListener('resize', () => {
        this.detectMobileDevice();
      });

      // Close menus when scrolling
      window.addEventListener('scroll', () => {
        this.closeAllMenus();
      }, true);
    }

    // Close all menus
    closeAllMenus(): void {
      this.uiStateService.closeBulkActionMenu();
      this.uiStateService.closeServiceActionMenu();
      this.uiStateService.closeCategoryActionMenu();
    }

    /**
     * Detect if the device is mobile and set the view mode accordingly
     */
    detectMobileDevice(): void {
      // Check if screen width is less than 768px (typical mobile breakpoint)
      if (window.innerWidth < 768) {
        this.viewMode = 'card';
      } else {
        this.viewMode = 'table';
      }
    }

    ngOnDestroy(): void {
      // Remove event listeners
      window.removeEventListener('resize', () => {
        this.detectMobileDevice();
      });

      window.removeEventListener('scroll', () => {
        this.closeAllMenus();
      }, true);
    }

    loadPlatforms(): void {
      this.uiStateService.setLoading(true, 'Loading platforms and services...');
      this.loadingService.show('Loading platforms and services...');

      // Use PlatformService to load platforms with services
      this.platformService.loadPlatformsWithServices(
        (platforms) => {
          // Store all platforms data
          this.allPlatforms = platforms;

          // Create a default "All Categories" option
          const allCategoriesOption = {
            id: 'all',
            label: 'filter.all_categories',
            sort: 0,
            icon: '' // No icon
          };

          // Update category options to show all categories
          this.updateCategoryOptions();

          // Set default selection to "All Categories"
          this.selectedCategory = allCategoriesOption;

          // Load special price counts
             this.applySelections();

            // Subscribe to servicesByCategory$ to get updates when services are reordered or moved
            this.adminService.servicesByCategory$.subscribe(servicesByCategory => {
              // Update the services in each category
              this.displayCategories.forEach(category => {
                const categoryId = category.id.toString();
                if (servicesByCategory[categoryId]) {
                  category.services = servicesByCategory[categoryId];
                }
              });
            });

            this.uiStateService.setLoading(false);
            this.loadingService.hide();
        },
        (error) => {
          // Error callback - hide loading states
          this.uiStateService.setLoading(false);
          this.loadingService.hide();
          console.error('Failed to load platforms in component:', error);
        }
      );
    }

    // Platform selection is no longer needed as we always show all categories

    // Property to store filtered categories for display
    displayCategories: ExtendedCategoryRes[] = [];

    // Properties for filtering
    private searchTerm: string = '';
    private isFilterApplied: boolean = false;

    // Original unfiltered data for restoring after filter is reset
    private unfilteredDisplayCategories: ExtendedCategoryRes[] = [];
    private unfilteredCurrentCategory: ExtendedCategoryRes | null = null;



    /**
     * Updates category dropdown options to show all categories from all platforms
     */
    private updateCategoryOptions(): void {
      // Use FilterService to update category options
      this.categoryOptions = this.filterService.updateCategoryOptions(this.allPlatforms);

      // Select first category by default if available
      if (this.categoryOptions.length > 0) {
        this.selectedCategory = this.categoryOptions[0];
      }
    }

    /**
     * Handles category selection from dropdown
     * @param category The selected category
     */
    onCategorySelected(category: ExtendedIconBaseModel): void {
      this.selectedCategory = category;

    }


    /**
     * Displays all categories from all platforms
     */
    private displayAllCategories(): void {
      // Use FilterService to display all categories
      this.displayCategories = this.filterService.displayAllCategories(this.allPlatforms, this.adminService);
    }



    // These methods have been removed as we now handle category selection directly in applySelections

    // createCategoryWithPlatformInfo is now handled by CategoryService

    // Helper method to convert service to GService format for compatibility with existing components


    // Helper methods for displaying platform and category info
    showPlatformCategoryInfo(category: ExtendedIconBaseModel | undefined, _service: any): boolean {
      if (!category || !category.id) return false;
      return category.id === 'all' || category.id.includes('_all');
    }

    getPlatformName(service: any): string {
      return service.platformName || '';
    }

    getCategoryName(service: any): string {
      return service.categoryName ? `| ${service.categoryName}` : '';
    }

    /**
     * Applies the category selections to update the display
     */
    private applySelections(): void {
      // Reset display containers
      this.displayCategories = [];
      this.currentCategory = null;

      // Reset filter state if filtering was previously applied
      if (this.isFilterApplied) {
        this.isFilterApplied = false;
        this.unfilteredDisplayCategories = [];
        this.unfilteredCurrentCategory = null;
      }

      // Display all categories by default
      if (!this.selectedCategory || this.selectedCategory.id === 'all') {
        this.displayAllCategories();
      }
      // Display specific category if selected
      else if (this.selectedCategory.id.includes('_')) {
        const [platformId, categoryId] = this.selectedCategory.id.split('_');
        const platform = this.allPlatforms.find(p => p.id.toString() === platformId);

        if (platform) {
          const category = platform.categories.find(c => c.id.toString() === categoryId);

          if (category && !category.hide) {
            const extendedCategory: ExtendedCategoryRes = {
              ...category,
              platformIcon: platform.icon,
              platformName: platform.name,
              isAllPlatforms: false,
              isAllCategories: false
            };

            this.displayCategories = [extendedCategory];
            this.currentCategory = extendedCategory;
          }
        }
      }

      // Apply search filter if there's a search term
      if (this.searchTerm) {
        // Use FilterService to apply filter
        const result = this.filterService.applyFilter(
          this.searchTerm,
          this.displayCategories,
          this.currentCategory,
          this.unfilteredDisplayCategories,
          this.unfilteredCurrentCategory
        );

        // Update state with filter results
        this.displayCategories = result.displayCategories;
        this.currentCategory = result.currentCategory;
        this.unfilteredDisplayCategories = result.unfilteredDisplayCategories;
        this.unfilteredCurrentCategory = result.unfilteredCurrentCategory;
        this.isFilterApplied = result.isFilterApplied;
      }
    }

    // Platform-specific methods have been removed as we now always display all categories

    /**
     * Applies filter based on search text
     * @param searchText The search text to filter by
     */
    applyFilter(searchText: string): void {
      // First apply the pending selections
      this.applySelections();

      // Use FilterService to apply filter
      const result = this.filterService.applyFilter(
        searchText,
        this.displayCategories,
        this.currentCategory,
        this.unfilteredDisplayCategories,
        this.unfilteredCurrentCategory
      );

      // Update state with filter results
      this.displayCategories = result.displayCategories;
      this.currentCategory = result.currentCategory;
      this.unfilteredDisplayCategories = result.unfilteredDisplayCategories;
      this.unfilteredCurrentCategory = result.unfilteredCurrentCategory;
      this.isFilterApplied = result.isFilterApplied;
      this.searchTerm = result.searchTerm;
    }

    /**
     * Resets all filters and returns to default view
     * @param searchInput The search input element to clear
     */
    resetFilter(searchInput: HTMLInputElement): void {
      // Clear the search input
      searchInput.value = '';

      // Use FilterService to reset filter
      const result = this.filterService.resetFilter(
        this.allPlatforms,
        this.isFilterApplied,
        this.unfilteredDisplayCategories,
        this.unfilteredCurrentCategory
      );

      // Update state with reset results
      this.displayCategories = result.displayCategories;
      this.currentCategory = result.currentCategory;
      this.unfilteredDisplayCategories = result.unfilteredDisplayCategories;
      this.unfilteredCurrentCategory = result.unfilteredCurrentCategory;
      this.isFilterApplied = result.isFilterApplied;
      this.searchTerm = result.searchTerm;
      this.selectedCategory = result.selectedCategory;

      // Apply the reset to display the default view
      this.applySelections();
    }

    /**
     * Get menu items for a service
     */
    getServiceMenuItems(service: SuperGeneralSvRes): AdminMenuItem[] {
      const items: AdminMenuItem[] = [];

      // Notify resellers
      items.push({
        id: 'notify-resellers',
        label: 'Notify resellers',
        icon: 'bell',
        iconColor: 'text-[#ff5722]'
      });

      // Edit service
      items.push({
        id: 'edit-service',
        label: 'Edit service',
        icon: 'edit',
        iconColor: 'text-[#2196F3]'
      });

      // Change category
      items.push({
        id: 'change-category',
        label: 'Change category',
        icon: 'folder',
        iconColor: 'text-[#2196F3]'
      });

      // Duplicate
      items.push({
        id: 'duplicate',
        label: 'Duplicate',
        icon: 'copy',
        iconColor: 'text-gray-500'
      });

      // Special Prices
      items.push({
        id: 'special-prices',
        label: 'Special Prices',
        icon: 'tag',
        iconColor: 'text-[#9C27B0]'
      });

      // Status toggle is handled separately in the template

      // Delete
      items.push({
        id: 'delete',
        label: 'Delete',
        icon: 'trash',
        iconColor: 'text-[#ff5722]'
      });

      return items;
    }

    /**
     * Handle service menu item click
     */
    onServiceMenuItemClick(itemId: string, service: SuperGeneralSvRes): void {
      switch (itemId) {
        case 'notify-resellers':
          this.notifyResellers(service);
          break;
        case 'edit-service':
          this.editService(service);
          break;
        case 'change-category':
          this.changeServiceCategory(service);
          break;
        case 'duplicate':
          this.duplicateService(service);
          break;
        case 'special-prices':
          this.addSpecialPriceForService(service);
          break;
        case 'delete':
          this.deleteService(service);
          break;
      }
    }

    /**
     * Get menu items for a category
     */
    getCategoryMenuItems(category: ExtendedCategoryRes): AdminMenuItem[] {
      const items: AdminMenuItem[] = [];

      // Edit
      items.push({
        id: 'edit-category',
        label: 'Edit',
        icon: 'edit',
        iconColor: 'text-blue-500'
      });

      // Disable all
      items.push({
        id: 'disable-category',
        label: 'Disable all',
        icon: 'ban',
        iconColor: 'text-gray-500'
      });

      // Enable all
      items.push({
        id: 'enable-category',
        label: 'Enable all',
        icon: 'check',
        iconColor: 'text-green-500'
      });

      // Sort by price (Low to High)
      items.push({
        id: 'sort-low-high',
        label: 'Sort by price (Low to High)',
        icon: 'sort-amount-down',
        iconColor: 'text-blue-500'
      });

      // Sort by price (High to Low)
      items.push({
        id: 'sort-high-low',
        label: 'Sort by price (High to Low)',
        icon: 'sort-amount-up',
        iconColor: 'text-blue-500'
      });

      // Remove
      items.push({
        id: 'remove-category',
        label: 'Remove',
        icon: 'trash',
        iconColor: 'text-red-500'
      });

      return items;
    }

    /**
     * Handle category menu item click
     */
    onCategoryMenuItemClick(itemId: string, category: ExtendedCategoryRes): void {
      switch (itemId) {
        case 'edit-category':
          this.editCategory(category);
          break;
        case 'disable-category':
          this.disableCategory(category);
          break;
        case 'enable-category':
          this.enableCategory(category);
          break;
        case 'sort-low-high':
          this.sortCategoryByPriceLowToHigh(category);
          break;
        case 'sort-high-low':
          this.sortCategoryByPriceHighToLow(category);
          break;
        case 'remove-category':
          this.removeCategory(category);
          break;
      }
    }

    /**
     * Get menu items for bulk actions
     */
    getBulkActionMenuItems(): AdminMenuItem[] {
      const items: AdminMenuItem[] = [];

      // Disable all
      items.push({
        id: 'disable-all',
        label: 'Disable all',
        icon: 'ban',
        iconColor: 'text-gray-500'
      });

      // Enable all
      items.push({
        id: 'enable-all',
        label: 'Enable all',
        icon: 'check',
        iconColor: 'text-green-500'
      });

      // Change category
      items.push({
        id: 'change-category',
        label: 'Change category',
        icon: 'folder',
        iconColor: 'text-blue-500'
      });

      // Change price
      items.push({
        id: 'change-price',
        label: 'Change price',
        icon: 'dollar-sign',
        iconColor: 'text-blue-500'
      });

      // Add special price
      items.push({
        id: 'add-special-price',
        label: 'Add special price',
        icon: 'tag',
        iconColor: 'text-blue-500'
      });

      // Delete custom prices
      items.push({
        id: 'delete-custom-prices',
        label: 'Delete custom prices',
        icon: 'times-circle',
        iconColor: 'text-red-500'
      });

      // Delete all
      items.push({
        id: 'delete-all',
        label: 'Delete all',
        icon: 'trash',
        iconColor: 'text-red-500'
      });

      return items;
    }

    /**
     * Handle bulk action menu item click
     */
    onBulkActionMenuItemClick(itemId: string): void {
      switch (itemId) {
        case 'disable-all':
          this.disableAllSelected();
          break;
        case 'enable-all':
          this.enableAllSelected();
          break;
        case 'change-category':
          this.changeCategory();
          break;
        case 'change-price':
          this.changePrice();
          break;
        case 'add-special-price':
          this.addSpecialPrice();
          break;
        case 'delete-custom-prices':
          this.deleteCustomPrices();
          break;
        case 'delete-all':
          this.deleteAllSelected();
          break;
      }
    }



  // Toggle category visibility (UI only, no API calls)
  onToggleCategoryVisibility(category: ExtendedCategoryRes, isCurrentlyHidden: boolean): void {
    // Toggle the hide property (if currently hidden, show it and vice versa)
    this.updateCategoryVisibility(category.id, !isCurrentlyHidden);
  }

  // Update category visibility in all relevant places (UI only)
  private updateCategoryVisibility(categoryId: number, hide: boolean): void {
    const result = this.categoryService.updateCategoryVisibility(
      categoryId,
      hide,
      this.displayCategories,
      this.currentCategory,
      this.allPlatforms
    );

    this.displayCategories = result.displayCategories;
    this.currentCategory = result.currentCategory;
  }

  // Check if a service is selected
  isServiceSelected(service: SuperGeneralSvRes): boolean {
    return this.selectionService.isServiceSelected(service);
  }

  // Toggle selection of a service
  toggleServiceSelection(service: SuperGeneralSvRes, event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.selectionService.toggleServiceSelection(service, checkbox.checked);
  }

  // Check if all services in a category are selected
  isCategoryFullySelected(category: ExtendedCategoryRes): boolean {
    return this.selectionService.isCategoryFullySelected(category);
  }

  // Toggle selection of all services in a category
  toggleCategorySelection(category: ExtendedCategoryRes, event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.selectionService.toggleCategorySelection(category, checkbox.checked);
  }

  // Check if all services across all categories are selected
  areAllServicesSelected(): boolean {
    return this.selectionService.areAllServicesSelected(this.displayCategories, this.currentCategory);
  }

  // Get the count of selected services
  getSelectedServicesCount(): number {
    return this.selectionService.getSelectedServicesCount();
  }

  // Check if any services are selected
  hasSelectedServices(): boolean {
    return this.selectionService.hasSelectedServices();
  }

  // Toggle selection of all services across all categories
  toggleAllServices(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.selectionService.toggleAllServices(this.displayCategories, this.currentCategory, checkbox.checked);
  }

  // Open bulk action menu
  openBulkActionMenu(event: MouseEvent): void {
    // Stop event propagation
    event.stopPropagation();

    // Close all other menus first
    this.uiStateService.closeServiceActionMenu();
    this.uiStateService.closeCategoryActionMenu();

    // Toggle menu visibility
    if (this.uiStateService.showBulkActionMenu) {
      this.uiStateService.closeBulkActionMenu();
      return;
    }

    // Simply toggle the menu visibility
    this.uiStateService.showBulkActionMenu = true;
  }

  // Close bulk action menu
  closeBulkActionMenu(): void {
    this.uiStateService.closeBulkActionMenu();
  }

  // Bulk action methods
  disableAllSelected(): void {
    console.log('Disable all selected services:', Array.from(this.selectionService.getSelectedServices()));

    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      console.log('No services selected');
      this.closeBulkActionMenu();
      return;
    }

    // Show loading indicator
    this.uiStateService.setBulkOperationLoading(true, `Disabling ${selectedServices.size} services...`);

    // Use ServiceManagementService to disable all selected services
    this.serviceManagementService.disableAllSelected(selectedServices, (completedCount, errorCount) => {
      console.log(`Deactivated ${completedCount} services, ${errorCount} errors`);
      this.uiStateService.setBulkOperationLoading(false);
    });

    this.closeBulkActionMenu();
  }

  enableAllSelected(): void {
    console.log('Enable all selected services:', Array.from(this.selectionService.getSelectedServices()));

    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      console.log('No services selected');
      this.closeBulkActionMenu();
      return;
    }

    // Show loading indicator
    this.uiStateService.setBulkOperationLoading(true, `Enabling ${selectedServices.size} services...`);

    // Use ServiceManagementService to enable all selected services
    this.serviceManagementService.enableAllSelected(selectedServices, (completedCount, errorCount) => {
      console.log(`Activated ${completedCount} services, ${errorCount} errors`);
      this.uiStateService.setBulkOperationLoading(false);
    });

    this.closeBulkActionMenu();
  }

  changeCategory(): void {
    console.log('Change category for selected services:', Array.from(this.selectionService.getSelectedServices()));
    this.uiStateService.openCategorySelectionModal();
    this.closeBulkActionMenu();
  }

  changeImage(): void {
    console.log('Change image for selected services:', Array.from(this.selectionService.getSelectedServices()));
    this.closeBulkActionMenu();
  }

  changeNameDescription(): void {
    console.log('Change name & description for selected services:', Array.from(this.selectionService.getSelectedServices()));
    this.closeBulkActionMenu();
  }

  changePrice(): void {
    console.log('Change price for selected services:', Array.from(this.selectionService.getSelectedServices()));
    this.uiStateService.openNewPricesModal();
    this.closeBulkActionMenu();
  }

  closeNewPricesModal(): void {
    this.uiStateService.closeNewPricesModal();
  }

  onPricesUpdated(services: SuperGeneralSvRes[]): void {
    console.log('Prices updated:', services);
    // Refresh the data to show the updated prices
    this.loadPlatforms();
  }

  addSpecialPrice(): void {
    console.log('Add special price for selected services:', Array.from(this.selectionService.getSelectedServices()));
    this.uiStateService.openNewSpecialPricesModal();
    this.closeBulkActionMenu();
  }

  closeNewSpecialPricesModal(): void {
    this.uiStateService.closeNewSpecialPricesModal();
  }

  openSpecialPricesUserModal(): void {
    this.uiStateService.openSpecialPricesUserModal();
  }

  closeSpecialPricesUserModal(): void {
    this.uiStateService.closeSpecialPricesUserModal();
  }

  openSpecialPricesServiceModal(): void {
    this.uiStateService.openSpecialPricesServiceModal();
  }

  closeSpecialPricesServiceModal(): void {
    this.uiStateService.closeSpecialPricesServiceModal();
  }

  onSpecialPriceAdded(data: any): void {
    console.log('Special price added:', data);
    // In a real implementation, you would call a service to add the special price in the backend
    // For now, we'll just refresh the data
    this.loadPlatforms();
  }

  deleteCustomPrices(): void {
    console.log('Delete custom prices for selected services:', Array.from(this.selectionService.getSelectedServices()));
    this.closeBulkActionMenu();
  }

  deleteAllSelected(): void {
    console.log('Delete all selected services:', Array.from(this.selectionService.getSelectedServices()));
    this.closeBulkActionMenu();

    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      console.log('No services selected');
      return;
    }

    // Show confirmation dialog for the first service
    // We'll delete all services after confirmation
    const firstServiceId = selectedServices.values().next().value;
    const firstService = this.adminService.getServiceValue().find(s => s.id === firstServiceId);

    if (firstService) {
      this.serviceToDelete = firstService;
      this.showDeleteConfirmation = true;
    }
  }

  // Override confirmDeleteService to handle bulk deletion
  confirmDeleteService(): void {
    if (!this.serviceToDelete) {
      return;
    }

    this.isDeleting = true;

    // Check if we're deleting multiple services
    const selectedServices = this.selectionService.getSelectedServices();

    if (selectedServices.size > 1) {
      // Bulk deletion
      console.log(`Deleting ${selectedServices.size} services`);

      // Show loading indicator with message
      this.uiStateService.setBulkOperationLoading(true, `Deleting ${selectedServices.size} services...`);

      let completedCount = 0;
      let errorCount = 0;

      // Process each service
      selectedServices.forEach(serviceId => {
        this.adminService.deleteService(serviceId).subscribe({
          next: () => {
            completedCount++;

            // Check if all operations are complete
            if (completedCount + errorCount === selectedServices.size) {
              console.log(`Deleted ${completedCount} services, ${errorCount} errors`);
              this.uiStateService.setBulkOperationLoading(false);
              this.closeDeleteConfirmation();
              this.loadPlatforms(); // Refresh the services list
              this.selectionService.clearSelectedServices(); // Clear selection
            }
          },
          error: (error) => {
            console.error('Error deleting service:', error);
            errorCount++;

            // Check if all operations are complete
            if (completedCount + errorCount === selectedServices.size) {
              console.log(`Deleted ${completedCount} services, ${errorCount} errors`);
              this.uiStateService.setBulkOperationLoading(false);
              this.closeDeleteConfirmation();
              this.loadPlatforms(); // Refresh the services list
              this.selectionService.clearSelectedServices(); // Clear selection
            }
          }
        });
      });
    } else {
      // Single service deletion
      this.adminService.deleteService(this.serviceToDelete.id).subscribe({
        next: () => {
          console.log(`Service ${this.serviceToDelete?.id} deleted successfully`);
          // Refresh the services list
          this.loadPlatforms();
          this.closeDeleteConfirmation();
        },
        error: (error) => {
          console.error('Error deleting service:', error);
          this.isDeleting = false;
          // Keep the dialog open to allow retry
        }
      });
    }
  }

  // Copy text to clipboard
  copyToClipboard(text: string, event: MouseEvent): void {
    event.stopPropagation();
    const element = event.currentTarget as HTMLElement;
    this.serviceManagementService.copyToClipboard(text, element);
  }

  // Extract domain name from URL
  extractDomainName(url: string | undefined): string {
    if (!url) return '';
    return this.serviceManagementService.extractDomainName(url);
  }

  // Service Action Menu Methods
  openServiceActionMenu(event: MouseEvent, service: SuperGeneralSvRes): void {
    // Stop event propagation
    event.stopPropagation();

    // Close all other menus first
    this.uiStateService.closeBulkActionMenu();
    this.uiStateService.closeCategoryActionMenu();

    // Toggle menu visibility
    if (this.uiStateService.showServiceActionMenu && this.uiStateService.selectedServiceForAction?.id === service.id) {
      this.uiStateService.closeServiceActionMenu();
      return;
    }

    // Set the selected service
    this.uiStateService.selectedServiceForAction = service;

    // Show the menu
    this.uiStateService.showServiceActionMenu = true;
  }

  closeServiceActionMenu(): void {
    this.uiStateService.closeServiceActionMenu();
  }

  // Service action handlers
  notifyResellers(service: SuperGeneralSvRes): void {
    console.log('Notify resellers for service:', service);
    this.closeServiceActionMenu();
    // Implement notification functionality
  }

  editService(service: SuperGeneralSvRes): void {
    console.log('Edit service:', service);

    // Make a deep copy of the service to avoid reference issues
    const serviceCopy = JSON.parse(JSON.stringify(service));
    console.log('Edit service (copy):', serviceCopy);

    this.closeServiceActionMenu();

    // Store the selected service in the UI state service
    this.uiStateService.selectedServiceForAction = serviceCopy;

    // Set the service type based on the add_type property
    const serviceType = serviceCopy.add_type === AddType.Api ? 'Provider' : 'Manual';

    // Open the new service modal in edit mode with the selected service
    this.uiStateService.openNewServiceModalForEdit(serviceCopy, serviceType);
  }

  changeServiceCategory(service: SuperGeneralSvRes): void {
    console.log('Change category for service:', service);
    // Store the selected service in the UI state service
    this.uiStateService.selectedServiceForAction = service;
    this.uiStateService.openCategorySelectionModal();
    this.closeServiceActionMenu();
  }

  duplicateService(service: SuperGeneralSvRes): void {
    console.log('Duplicating service:', service);
    this.closeServiceActionMenu();

    this.serviceManagementService.duplicateService(service);
  }

  addSpecialPriceForService(service: SuperGeneralSvRes): void {
    console.log('Add special price for service:', service);

    // Create a temporary set with just this service ID
    this.selectionService.clearSelectedServices();
    this.selectionService.toggleServiceSelection(service, true);

    // First close the service action menu without clearing the selected service
    this.uiStateService.showServiceActionMenu = false;

    // Explicitly set the selected service for action
    this.uiStateService.selectedServiceForAction = service;

    // Show the popup
    this.uiStateService.openSpecialPricesServiceModal();
  }

  isServiceEnabled(service: SuperGeneralSvRes): boolean {
    return this.serviceManagementService.isServiceEnabled(service);
  }

  toggleServiceStatus(service: SuperGeneralSvRes, isChecked: boolean): void {
    this.serviceManagementService.toggleServiceStatus(service, isChecked, () => {
      this.closeServiceActionMenu();
    });
  }

  // Properties for delete confirmation
  showDeleteConfirmation = false;
  serviceToDelete: SuperGeneralSvRes | null = null;
  isDeleting = false;

  deleteService(service: SuperGeneralSvRes): void {
    console.log('Delete service:', service);
    this.closeServiceActionMenu();

    // Show confirmation dialog
    this.serviceToDelete = service;
    this.showDeleteConfirmation = true;
  }

  // Close delete confirmation dialog
  closeDeleteConfirmation(): void {
    this.showDeleteConfirmation = false;
    this.serviceToDelete = null;
    this.isDeleting = false;
  }

  // Category Action Menu Methods
  openCategoryActionMenu(event: MouseEvent, category: ExtendedCategoryRes): void {
    // Stop event propagation
    event.stopPropagation();

    // Close all other menus first
    this.uiStateService.closeBulkActionMenu();
    this.uiStateService.closeServiceActionMenu();

    // Toggle menu visibility
    if (this.uiStateService.showCategoryActionMenu && this.uiStateService.selectedCategoryForAction?.id === category.id) {
      this.uiStateService.closeCategoryActionMenu();
      return;
    }

    // Set the selected category
    this.uiStateService.selectedCategoryForAction = category;

    // Show the menu
    this.uiStateService.showCategoryActionMenu = true;
  }

  closeCategoryActionMenu(): void {
    this.uiStateService.closeCategoryActionMenu();
  }

  // Category action handlers

  editCategory(category: ExtendedCategoryRes): void {
    console.log('Edit category:', category);
    this.closeCategoryActionMenu();

    // Set the category to edit using CategoryService
    this.categoryToEdit = this.categoryService.editCategory(category);

    // Open the new category modal in edit mode
    this.uiStateService.openNewCategoryModal();
  }

  // Platform ID can be found using platformService.findPlatformIdByName

  disableCategory(category: ExtendedCategoryRes): void {
    console.log('Disable category:', category);

    if (!category || !category.services || category.services.length === 0) {
      console.log('No services in category');
      this.closeCategoryActionMenu();
      return;
    }

    // Show loading indicator
    this.uiStateService.setBulkOperationLoading(true, `Disabling all services in category "${category.name}"...`);

    // Use CategoryService to disable the category
    this.categoryService.disableCategory(category, (completedCount, errorCount) => {
      console.log(`Deactivated ${completedCount} services in category, ${errorCount} errors`);
      this.uiStateService.setBulkOperationLoading(false);
    });

    this.closeCategoryActionMenu();
  }

  enableCategory(category: ExtendedCategoryRes): void {
    console.log('Enable category:', category);

    if (!category || !category.services || category.services.length === 0) {
      console.log('No services in category');
      this.closeCategoryActionMenu();
      return;
    }

    // Show loading indicator
    this.uiStateService.setBulkOperationLoading(true, `Enabling all services in category "${category.name}"...`);

    // Use CategoryService to enable the category
    this.categoryService.enableCategory(category, (completedCount, errorCount) => {
      console.log(`Activated ${completedCount} services in category, ${errorCount} errors`);
      this.uiStateService.setBulkOperationLoading(false);
    });

    this.closeCategoryActionMenu();
  }

  sortCategoryByPriceLowToHigh(category: ExtendedCategoryRes): void {
    console.log('Sort category by price (Low to High):', category);
    this.closeCategoryActionMenu();

    // Show loading indicator
    this.uiStateService.setBulkOperationLoading(true, `Sorting services in category "${category.name}" by price (Low to High)...`);

    // Use CategoryService to sort the category by price (Low to High)
    this.categoryService.sortCategoryByPriceLowToHigh(category, () => {
      this.uiStateService.setBulkOperationLoading(false);
      // Refresh the data to show the new order
      this.loadPlatforms();
    });
  }

  sortCategoryByPriceHighToLow(category: ExtendedCategoryRes): void {
    console.log('Sort category by price (High to Low):', category);
    this.closeCategoryActionMenu();

    // Show loading indicator
    this.uiStateService.setBulkOperationLoading(true, `Sorting services in category "${category.name}" by price (High to Low)...`);

    // Use CategoryService to sort the category by price (High to Low)
    this.categoryService.sortCategoryByPriceHighToLow(category, () => {
      this.uiStateService.setBulkOperationLoading(false);
      // Refresh the data to show the new order
      this.loadPlatforms();
    });
  }

  removeCategory(category: ExtendedCategoryRes): void {
    console.log('Remove category:', category);
    this.closeCategoryActionMenu();
    // Implement remove functionality
  }

  // Category selection methods
  get selectedCategoryForMove(): SuperCategoryRes | null { return this.uiStateService.selectedCategoryForMove; }
  set selectedCategoryForMove(value: SuperCategoryRes | null) { this.uiStateService.selectedCategoryForMove = value; }

  closeCategorySelectionModal(): void {
    this.uiStateService.closeCategorySelectionModal();
  }

  openNewCategoryModal(): void {
    this.uiStateService.openNewCategoryModal();
  }

  closeNewCategoryModal(): void {
    this.uiStateService.closeNewCategoryModal();
    // Refresh platforms list after adding/editing a category
    this.loadPlatforms();
  }

  onCategoryAdded(category: SuperCategoryRes): void {
    console.log('Category added:', category);
    // Refresh platforms list after adding a new category
    this.loadPlatforms();
  }

  onCategoryUpdated(category: SuperCategoryRes): void {
    console.log('Category updated:', category);
    // Refresh platforms list after updating a category
    this.loadPlatforms();
  }

  onCategorySelectedForMove(category: SuperCategoryRes): void {
    console.log('Category selected for move:', category);
    this.selectedCategoryForMove = category;

    // If we have a single service selected for category change
    if (this.uiStateService.selectedServiceForAction) {
      // The category change is handled by the category-selection component
      // Just refresh the data after the change
      this.loadPlatforms();
      this.uiStateService.closeCategorySelectionModal();
      return;
    }

    // Move selected services to the selected category
    if (this.selectedCategoryForMove) {
      // Use DragDropService to move services to the new category
      this.dragDropService.moveServicesToCategory(
        this.selectionService.getSelectedServices(),
        this.selectedCategoryForMove,
        this.displayCategories,
        () => {
          this.applySelections();
          this.selectionService.clearSelectedServices(); // Clear selection after moving
          this.uiStateService.closeCategorySelectionModal();
        }
      );
    }
  }

  // Properties for Resources popup
  showResourcesModal = false;

  // Add service button handler
  addService(): void {
    // Open the Resources component instead of NewService component
    this.showResourcesModal = true;
    console.log('Add service clicked - opening Resources popup');
  }

  closeResourcesModal(): void {
    this.showResourcesModal = false;
  }

  openNewServiceFromResources(serviceType: string): void {
    // Close resources modal and open new service modal
    this.showResourcesModal = false;
    this.uiStateService.openNewServiceModal(serviceType);
    console.log('Opening new service modal with type:', serviceType);
  }

  closeNewServiceModal(): void {
    this.uiStateService.closeNewServiceModal();
    // No longer refreshing services list on close - only on save
  }

  openResourcesFromNewService(): void {
    // Open resources modal after closing new service modal
    this.showResourcesModal = true;
    console.log('Opening resources modal from new service component');
  }

  onServiceAdded(service: SuperGeneralSvRes): void {
    console.log('Service added:', service);
    // Refresh the services list
    this.loadPlatforms();
  }

  // Import services button handler
  importServices(): void {
    this.uiStateService.openImportServicesModal();
    console.log('Import services clicked');
  }

  closeImportServicesModal(): void {
    this.uiStateService.closeImportServicesModal();
  }

  onServicesImported(services: SuperGeneralSvRes[]): void {
    console.log('Services imported:', services);
    // Refresh the services list
    this.loadPlatforms();
  }

  // Category Platform dropdown methods
  toggleCategoryPlatformDropdown(event: MouseEvent, category: ExtendedCategoryRes): void {
    this.uiStateService.toggleCategoryPlatformDropdown(event, category, this.categoryPlatformSelections, this.allPlatforms);
  }

  isCategoryPlatformSelected(category: ExtendedCategoryRes, platformId: number): boolean {
    return this.platformService.isCategoryPlatformSelected(category.id, platformId, this.categoryPlatformSelections);
  }

  selectCategoryPlatform(category: ExtendedCategoryRes, platform: SuperPlatformRes): void {
    this.platformService.selectCategoryPlatform(category, platform, this.categoryPlatformSelections);
    this.uiStateService.closeCategoryPlatformDropdown();

    // Refresh platforms list after changing a category's platform
    setTimeout(() => {
      this.loadPlatforms();
    }, 500); // Add a small delay to ensure the API call completes
  }

  addNewPlatformFromCategory(event: MouseEvent): void {
    event.stopPropagation();
    console.log('Configure platforms clicked from category');

    // Open the platform-management modal
    this.uiStateService.openPlatformManagementModal();

    // Close the dropdown after clicking
    this.uiStateService.closeCategoryPlatformDropdown();
  }

  // Category drag and drop event handlers with CDK
  onCategoryDragStarted(event: CdkDragStart): void {
    // Get the width of the original element
    const element = event.source.element.nativeElement;
    const width = element.offsetWidth;

    // Set the CSS variable for the drag preview width
    document.documentElement.style.setProperty('--category-width', `${width}px`);
  }

  onCategoryDropCdk(event: CdkDragDrop<ExtendedCategoryRes[]>): void {
    if (event.previousIndex === event.currentIndex) {
      return; // No change in position
    }

    // Update the local array using moveItemInArray for immediate UI feedback
    moveItemInArray(this.displayCategories, event.previousIndex, event.currentIndex);

    // Get the categories to reorder
    const category1 = this.displayCategories[event.previousIndex];
    const category2 = this.displayCategories[event.currentIndex];

    console.log(`Reordering categories: ${category1.id} and ${category2.id}`);

    // Call the reorder API instead of swap API
    this.adminService.reorderCategory(category1.id, category2.id).subscribe({
      next: () => {
        console.log('Category reorder completed successfully');
        // Refresh the data to show the new order
        this.loadPlatforms();
      },
      error: (error: any) => {
        console.error('Error reordering categories:', error);
        // Revert the local change if the API call fails
        moveItemInArray(this.displayCategories, event.currentIndex, event.previousIndex);
      }
    });
  }

  // Service drag and drop event handlers with CDK
  onServiceDragStarted(event: CdkDragStart): void {
    // Get the width of the original element
    const element = event.source.element.nativeElement;
    const width = element.offsetWidth;

    // Set the CSS variable for the drag preview width
    document.documentElement.style.setProperty('--service-width', `${width}px`);
  }



  onServiceDropCdk(event: CdkDragDrop<SuperGeneralSvRes[]>, category: ExtendedCategoryRes): void {
    if (event.previousContainer === event.container) {
      // If dropping in the same category, use reorder logic instead of swap
      if (event.previousIndex === event.currentIndex) {
        return; // No change in position
      }

      // Update the local array using moveItemInArray for immediate UI feedback
      moveItemInArray(category.services, event.previousIndex, event.currentIndex);

      // Get the services to reorder
      const service1 = category.services[event.previousIndex];
      const service2 = category.services[event.currentIndex];

      console.log(`Reordering services: ${service1.id} and ${service2.id}`);

      // Call the reorder API instead of swap API
      this.adminService.reorderService(service1.id, service2.id).subscribe({
        next: () => {
          console.log('Service reorder completed successfully');
          // Re-apply selections to refresh the UI
          this.applySelections();
        },
        error: (error: any) => {
          console.error('Error reordering services:', error);
          // Revert the local change if the API call fails
          moveItemInArray(category.services, event.currentIndex, event.previousIndex);
        }
      });
    } else {
      // If dropping between different categories, handle the move
      // Get the source and target category IDs
      const sourceCategory = this.displayCategories.find(cat =>
        cat.services.includes(event.item.data)
      );

      if (!sourceCategory) {
        console.error('Source category not found');
        return;
      }

      const sourceCategoryId = sourceCategory.id.toString();
      const targetCategoryId = category.id.toString();

      // Find the index of the service in the source category
      const sourceIndex = sourceCategory.services.findIndex(s => s.id === event.item.data.id);

      // Call the service to update the backend
      this.dragDropService.handleServiceDrop(
        sourceCategoryId,
        targetCategoryId,
        sourceIndex,
        event.currentIndex,
        () => {
          // Re-apply selections to refresh the UI
          this.applySelections();
        }
      );
    }
  }

  // Original drag and drop event handlers (kept for backward compatibility)
  onCategoryDragStart(event: DragEvent, category: ExtendedCategoryRes, index: number): void {
    if (!event.dataTransfer) return;

    this.draggedCategory = category;
    this.draggedCategoryIndex = index;

    // Set data for the drag operation
    event.dataTransfer.setData('text/plain', JSON.stringify({
      categoryId: category.id,
      index: index
    }));

    // Add a class to the dragged element for styling
    const element = event.target as HTMLElement;
    const dragElement = element.closest('.category-header-row, .category-header-card');
    if (dragElement) {
      dragElement.classList.add('dragging');
    }

    // Create a custom drag preview
    const previewTemplate = element.querySelector('.category-drag-preview') as HTMLTemplateElement;
    if (previewTemplate) {
      const previewContent = document.importNode(previewTemplate.content, true);
      const previewElement = document.createElement('div');
      previewElement.appendChild(previewContent);
      previewElement.style.position = 'absolute';
      previewElement.style.top = '-1000px';
      previewElement.style.left = '-1000px';
      previewElement.style.opacity = '0.9';
      previewElement.style.zIndex = '9999';
      previewElement.style.pointerEvents = 'none';
      previewElement.style.width = `${element.offsetWidth}px`;
      previewElement.style.backgroundColor = 'white';
      previewElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
      previewElement.style.borderRadius = '8px';

      document.body.appendChild(previewElement);

      // Set the custom drag image
      event.dataTransfer.setDragImage(previewElement, 20, 20);

      // Remove the preview element after a short delay
      setTimeout(() => {
        document.body.removeChild(previewElement);
      }, 100);
    }

    // Set the drag effect
    event.dataTransfer.effectAllowed = 'move';
  }

  onCategoryDragOver(event: DragEvent, index: number): void {
    event.preventDefault();

    // Update the drop target information
    this.dropTargetCategoryIndex = index;

    // Add visual indication of drop target
    const element = event.target as HTMLElement;
    const dropZone = element.closest('.category-header-row, .category-header-card') as HTMLElement;

    if (dropZone) {
      // Remove highlight from all potential drop zones
      document.querySelectorAll('.drop-target, .highlight-drop-area').forEach(el => {
        el.classList.remove('drop-target', 'highlight-drop-area');
      });

      // Add highlight to current drop zone
      dropZone.classList.add('drop-target');
      dropZone.classList.add('highlight-drop-area');
    }
  }

  onCategoryDragEnter(event: DragEvent): void {
    event.preventDefault();
    const element = event.target as HTMLElement;
    const dropZone = element.closest('.category-header-row, .category-header-card') as HTMLElement;

    if (dropZone) {
      dropZone.classList.add('drag-enter');
      dropZone.classList.add('highlight-drop-area');
    }
  }

  onCategoryDragLeave(event: DragEvent): void {
    const element = event.target as HTMLElement;
    const dropZone = element.closest('.category-header-row, .category-header-card') as HTMLElement;

    if (dropZone) {
      dropZone.classList.remove('drag-enter');
      dropZone.classList.remove('highlight-drop-area');
    }
  }

  onCategoryDrop(event: DragEvent, index: number): void {
    event.preventDefault();

    // Remove all drag-related classes from both table rows and cards
    document.querySelectorAll('.dragging, .drop-target, .drag-enter, .highlight-drop-area').forEach(el => {
      el.classList.remove('dragging', 'drop-target', 'drag-enter', 'highlight-drop-area');
    });

    // Ensure we have a dragged category
    if (!this.draggedCategory) return;

    console.log(`Dropping category ${this.draggedCategory.id} from index ${this.draggedCategoryIndex} to index ${index}`);

    // Handle the drop
    this.dragDropService.handleCategoryDrop(
      this.draggedCategoryIndex,
      index,
      this.displayCategories,
      (updatedCategories) => {
        // Update the displayCategories array
        this.displayCategories = updatedCategories;
      }
    );

    // Reset drag state
    this.draggedCategory = null;
    this.draggedCategoryIndex = -1;
    this.dropTargetCategoryIndex = -1;
  }

  onCategoryDragEnd(_event: DragEvent): void {
    // Remove all drag-related classes from both table rows and cards
    document.querySelectorAll('.dragging, .drop-target, .drag-enter, .highlight-drop-area').forEach(el => {
      el.classList.remove('dragging', 'drop-target', 'drag-enter', 'highlight-drop-area');
    });

    // Reset drag state
    this.draggedCategory = null;
    this.draggedCategoryIndex = -1;
    this.dropTargetCategoryIndex = -1;
  }

  // Drag and drop event handlers
  onDragStart(event: DragEvent, service: SuperGeneralSvRes, index: number, categoryId: string): void {
    if (!event.dataTransfer) return;

    this.draggedService = service;
    this.draggedServiceIndex = index;
    this.draggedServiceCategoryId = categoryId;

    // Set data for the drag operation
    event.dataTransfer.setData('text/plain', JSON.stringify({
      serviceId: service.id,
      categoryId: categoryId,
      index: index
    }));

    // Add a class to the dragged element for styling
    const element = event.target as HTMLElement;
    const dragElement = element.closest('.service-row, .service-card');
    if (dragElement) {
      dragElement.classList.add('dragging');
    }

    // Create a custom drag preview
    const previewTemplate = element.querySelector('.service-drag-preview') as HTMLTemplateElement;
    if (previewTemplate) {
      const previewContent = document.importNode(previewTemplate.content, true);
      const previewElement = document.createElement('div');
      previewElement.appendChild(previewContent);
      previewElement.style.position = 'absolute';
      previewElement.style.top = '-1000px';
      previewElement.style.left = '-1000px';
      previewElement.style.opacity = '0.9';
      previewElement.style.zIndex = '9999';
      previewElement.style.pointerEvents = 'none';
      previewElement.style.width = `${element.offsetWidth}px`;
      previewElement.style.backgroundColor = 'white';
      previewElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
      previewElement.style.borderRadius = '8px';

      document.body.appendChild(previewElement);

      // Set the custom drag image
      event.dataTransfer.setDragImage(previewElement, 20, 20);

      // Remove the preview element after a short delay
      setTimeout(() => {
        document.body.removeChild(previewElement);
      }, 100);
    }

    // Set the drag effect
    event.dataTransfer.effectAllowed = 'move';
  }

  onDragOver(event: DragEvent, categoryId: string, index: number): void {
    event.preventDefault();

    // Update the drop target information
    this.dropTargetCategoryId = categoryId;
    this.dropTargetIndex = index;

    // Add visual indication of drop target
    const element = event.target as HTMLElement;
    const dropZone = element.closest('.service-row, .service-card') as HTMLElement;

    if (dropZone) {
      // Remove highlight from all potential drop zones
      document.querySelectorAll('.drop-target, .highlight-drop-area').forEach(el => {
        el.classList.remove('drop-target', 'highlight-drop-area');
      });

      // Add highlight to current drop zone
      dropZone.classList.add('drop-target');
      dropZone.classList.add('highlight-drop-area');
    }
  }

  onDragEnter(event: DragEvent): void {
    event.preventDefault();
    const element = event.target as HTMLElement;
    const dropZone = element.closest('.service-row, .service-card') as HTMLElement;

    if (dropZone) {
      dropZone.classList.add('drag-enter');
      dropZone.classList.add('highlight-drop-area');
    }
  }

  onDragLeave(event: DragEvent): void {
    const element = event.target as HTMLElement;
    const dropZone = element.closest('.service-row, .service-card') as HTMLElement;

    if (dropZone) {
      dropZone.classList.remove('drag-enter');
      dropZone.classList.remove('highlight-drop-area');
    }
  }

  onDrop(event: DragEvent, categoryId: string, index: number): void {
    event.preventDefault();

    // Remove all drag-related classes from both table rows and cards
    document.querySelectorAll('.dragging, .drop-target, .drag-enter, .highlight-drop-area').forEach(el => {
      el.classList.remove('dragging', 'drop-target', 'drag-enter', 'highlight-drop-area');
    });

    // Ensure we have a dragged service
    if (!this.draggedService) return;

    console.log(`Dropping service ${this.draggedService.id} from category ${this.draggedServiceCategoryId}, index ${this.draggedServiceIndex} to category ${categoryId}, index ${index}`);

    // Handle the drop
    this.dragDropService.handleServiceDrop(
      this.draggedServiceCategoryId,
      categoryId,
      this.draggedServiceIndex,
      index,
      () => {
        // Re-apply selections to refresh the UI
        this.applySelections();
      }
    );

    // Reset drag state
    this.draggedService = null;
    this.draggedServiceIndex = -1;
    this.draggedServiceCategoryId = '';
    this.dropTargetCategoryId = '';
    this.dropTargetIndex = -1;
  }

  onDragEnd(_event: DragEvent): void {
    // Remove all drag-related classes from both table rows and cards
    document.querySelectorAll('.dragging, .drop-target, .drag-enter, .highlight-drop-area').forEach(el => {
      el.classList.remove('dragging', 'drop-target', 'drag-enter', 'highlight-drop-area');
    });

    // Reset drag state
    this.draggedService = null;
    this.draggedServiceIndex = -1;
    this.draggedServiceCategoryId = '';
    this.dropTargetCategoryId = '';
    this.dropTargetIndex = -1;
  }


}

