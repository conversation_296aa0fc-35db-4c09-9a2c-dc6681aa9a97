<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="40780646-c849-4c6e-aae5-c7eb1e0de494" name="Changes" comment="accept html">
      <change beforePath="$PROJECT_DIR$/../smm-admin/src/app/core/services/admin-service.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../smm-admin/src/app/core/services/admin-service.service.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
    </option>
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTTP Public Environment File" />
        <option value="AnnotationType" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/.." value="a4ca2219a1d89e34760b8f30d090eddf57808fc7" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;tndung&quot;,
      &quot;fullname&quot;: &quot;Trương Như Dũng&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://gitlab.com/tndung/smm.git&quot;,
    &quot;second&quot;: &quot;76b915c2-23cb-4ff0-9e7a-f023e09fd83f&quot;
  }
}</component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="**********" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/.." />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/io/jsonwebtoken/jjwt-api/0.12.3/jjwt-api-0.12.3.jar!/io/jsonwebtoken/JwtBuilder.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="HttpClientSelectedEnvironments">
    <file url="file://$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" environment="localhost" />
    <file url="file://$PROJECT_DIR$/curl/access-requests.http" environment="localhost" />
    <file url="file://$PROJECT_DIR$/curl/user-requests.http" environment="localhost" />
    <file url="file://$PROJECT_DIR$/curl/public-requests.http" environment="Dev" />
    <file url="file://$PROJECT_DIR$/curl/service-requests.http" environment="localhost" />
    <file url="file://$PROJECT_DIR$/curl/platform-requests.http" environment="localhost" />
    <file url="file://$PROJECT_DIR$/curl/order-requests.http" environment="localhost" />
    <file url="file://$PROJECT_DIR$/curl/provider-requests.http" environment="localhost" />
    <file url="file://$PROJECT_DIR$/curl/ticket-requests.http" environment="localhost" />
    <file url="file://$PROJECT_DIR$/curl/update-log-requests.http" environment="localhost" />
  </component>
  <component name="KubernetesApiProvider">{
  &quot;contexts&quot;: [
    {
      &quot;name&quot;: &quot;k8s.local&quot;,
      &quot;originalNamespace&quot;: &quot;development&quot;
    }
  ],
  &quot;isMigrated&quot;: true
}</component>
  <component name="KubernetesSettings">
    <option name="contextName" value="k8s.local" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="PhpDebugGeneral" listening_started="true" />
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="C:\xampp\php\php.exe" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2bg4aH7JRbNvstvc06FjoolTLzB" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Docker.Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker-compose.yml: Compose Deployment.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.All in SMM.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.All in access-requests.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.SMM | add#1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.SMM | check balance by id.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.SMM | check balance.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.SMM | generate mfa (1).executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.SMM | generate mfa.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.SMM | login.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.SMM | verify.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.access-requests | login.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.access-requests | mfa disabled.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.access-requests | refresh.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.access-requests | verify.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | #11.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | #12.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | #13.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | #15.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | #18.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | #46.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | add#1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | generate mfa.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | login.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | logout.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | mfa.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | verify.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.order-requests | add.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.order-requests | me Copy.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.order-requests | me.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.order-requests | search.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.platform-requests | add#1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.platform-requests | get all#1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.platform-requests | get all#2.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.platform-requests | get all.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.provider-requests | check balance by id.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.provider-requests | check balance.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.public-requests | balance.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.public-requests | order status.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.service-requests | #9.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.service-requests | active.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.service-requests | add.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.service-requests | deactivate#1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.service-requests | deactivate#2.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.service-requests | deactivate#3.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.service-requests | deactivate#4.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.service-requests | edit.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.service-requests | get all.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.ticket-requests | add.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.ticket-requests | create replies.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.ticket-requests | get detail.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.ticket-requests | search replies.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.ticket-requests | search#1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.ticket-requests | search#2.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.update-log-requests | search.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.user-requests | #6.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.user-requests | #7.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.user-requests | add fund.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.user-requests | change pass.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.user-requests | edit.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.user-requests | generate apikey.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.user-requests | summary transactions.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.PlatformControllerTests.createData_givenValidBody_willReturnNewPost.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.PlatformServiceTests.executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SONARLINT_PRECOMMIT_ANALYSIS&quot;: &quot;false&quot;,
    &quot;Spring Boot.SMMSystemApplication.executor&quot;: &quot;Debug&quot;,
    &quot;database.data.extractors.current.export.id&quot;: &quot;SQL Inserts&quot;,
    &quot;database.data.extractors.current.id&quot;: &quot;SQL Inserts&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;corretto-11&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/DUAN_2025/smm/smm-system&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;rest.client.default.execution.environment&quot;: &quot;localhost&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\IntelliJ IDEA 2021.3\\plugins\\javascript-impl\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;,
      &quot;redis&quot;,
      &quot;postgresql&quot;
    ],
    &quot;com.intellij.ide.scratch.LRUPopupBuilder$1/&quot;: [
      &quot;PostgreSQL&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\DUAN_2022\vnfb-uplike\smm-system\curl" />
      <recent name="E:\DUAN_2022\vnfb-uplike\smm-system" />
      <recent name="E:\DUAN_2022\vnfb-uplike\smm-system\src\main\java\tndung\vnfb\smm\dto\request" />
      <recent name="C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2023.3\scratches" />
      <recent name="E:\DUAN_2022\vnfb-uplike\smm-system\src\main\java\tndung\vnfb\smm\constant\smm" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2023.3\scratches" />
      <recent name="E:\DUAN_2022\vnfb-uplike\smm-system\postman" />
      <recent name="E:\DUAN_2022\vnfb-uplike\smm-system\curl" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="tndung.vnfb.smm.mapper" />
      <recent name="tndung.vnfb.smm.aop" />
      <recent name="tndung.vnfb.smm.service" />
      <recent name="tndung.vnfb.smm.service.impl" />
      <recent name="tndung.vnfb.smm.repository.nontenant" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.SMMSystemApplication">
    <configuration name="service-requests | active" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/curl/service-requests.http" index="4" requestIdentifier="active" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="service-requests | deactivate#1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/curl/service-requests.http" index="5" requestIdentifier="deactivate#1" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="update-log-requests | search" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/curl/update-log-requests.http" requestIdentifier="search" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="user-requests | #6" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/curl/user-requests.http" index="6" requestIdentifier="#6" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="SMMSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="local" />
      <module name="smm-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tndung.vnfb.smm.SMMSystemApplication" />
      <option name="VM_PARAMETERS" value="-Duser.timezone=UTC" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Dockerfile" type="docker-deploy" factoryName="dockerfile" temporary="true" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="sourceFilePath" value="Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Docker.Dockerfile" />
      <item itemvalue="HTTP Request.user-requests | #6" />
      <item itemvalue="HTTP Request.service-requests | active" />
      <item itemvalue="HTTP Request.service-requests | deactivate#1" />
      <item itemvalue="HTTP Request.update-log-requests | search" />
      <item itemvalue="Spring Boot.SMMSystemApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP Request.update-log-requests | search" />
        <item itemvalue="HTTP Request.service-requests | deactivate#1" />
        <item itemvalue="HTTP Request.service-requests | active" />
        <item itemvalue="HTTP Request.user-requests | #6" />
        <item itemvalue="Docker.Dockerfile" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="jdk-11.0.22-corretto-11.0.22-4caba194b151-868cef46" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="40780646-c849-4c6e-aae5-c7eb1e0de494" name="Changes" comment="" />
      <created>1706623450642</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1706623450642</updated>
      <workItem from="1706623451570" duration="3084000" />
      <workItem from="1706668320277" duration="26538000" />
      <workItem from="1706787933618" duration="1580000" />
      <workItem from="1706837483398" duration="17669000" />
      <workItem from="1706931813144" duration="25628000" />
      <workItem from="1707875525407" duration="8854000" />
      <workItem from="1708246666971" duration="5806000" />
      <workItem from="1708311374991" duration="8397000" />
      <workItem from="1708393822713" duration="35676000" />
      <workItem from="1708594522049" duration="6187000" />
      <workItem from="1708659278113" duration="11304000" />
      <workItem from="1708759094154" duration="10610000" />
      <workItem from="1708829168802" duration="15893000" />
      <workItem from="1708915646515" duration="6889000" />
      <workItem from="1709001488294" duration="11764000" />
      <workItem from="1709174079171" duration="6668000" />
      <workItem from="1709277228146" duration="9812000" />
      <workItem from="1709350990030" duration="87000" />
      <workItem from="1709353868473" duration="124000" />
      <workItem from="1709354691318" duration="963000" />
      <workItem from="1709360688424" duration="6734000" />
      <workItem from="1709435656760" duration="12608000" />
      <workItem from="1709552992066" duration="2810000" />
      <workItem from="1710066173333" duration="2241000" />
      <workItem from="1710473073831" duration="1262000" />
      <workItem from="1710569325000" duration="3190000" />
      <workItem from="1711339640530" duration="838000" />
      <workItem from="1712457156548" duration="720000" />
      <workItem from="1713237083211" duration="1996000" />
      <workItem from="1713414136241" duration="2893000" />
      <workItem from="1713495877399" duration="565000" />
      <workItem from="1714377569391" duration="5154000" />
      <workItem from="1714662329253" duration="78000" />
      <workItem from="1714877127649" duration="3988000" />
      <workItem from="1714960564014" duration="9000" />
      <workItem from="1715413185473" duration="586000" />
      <workItem from="1715677974322" duration="602000" />
      <workItem from="1720969163143" duration="770000" />
      <workItem from="1722323940940" duration="1594000" />
      <workItem from="1722512883760" duration="576000" />
      <workItem from="1722757081980" duration="724000" />
      <workItem from="1722925668035" duration="4383000" />
      <workItem from="1723089967361" duration="1456000" />
      <workItem from="1727667442678" duration="765000" />
      <workItem from="1727668248794" duration="900000" />
      <workItem from="1729416113573" duration="3233000" />
      <workItem from="1729914855932" duration="6000" />
      <workItem from="1729928105575" duration="55000" />
      <workItem from="1729928263147" duration="22000" />
      <workItem from="1729928395291" duration="74000" />
      <workItem from="1729928523164" duration="290000" />
      <workItem from="1732159769656" duration="9873000" />
      <workItem from="1732240903410" duration="1200000" />
      <workItem from="1732266713540" duration="1196000" />
      <workItem from="1732279945968" duration="4222000" />
      <workItem from="1732540893380" duration="596000" />
      <workItem from="1740449745452" duration="3117000" />
      <workItem from="1740630853610" duration="582000" />
      <workItem from="1740718636692" duration="2614000" />
      <workItem from="1740797035181" duration="3576000" />
      <workItem from="1740963303486" duration="2147000" />
      <workItem from="1740995976290" duration="601000" />
      <workItem from="1741049768819" duration="1808000" />
      <workItem from="1741137456302" duration="2994000" />
      <workItem from="1741609622704" duration="722000" />
      <workItem from="1741866881820" duration="1293000" />
      <workItem from="1742700431975" duration="134000" />
      <workItem from="1742864726079" duration="1376000" />
      <workItem from="1743049605448" duration="3250000" />
      <workItem from="1743310275626" duration="6093000" />
      <workItem from="1743641312549" duration="1388000" />
      <workItem from="1743728462949" duration="1289000" />
      <workItem from="1743729792950" duration="15654000" />
      <workItem from="1743815696285" duration="17677000" />
      <workItem from="1743845586079" duration="93000" />
      <workItem from="1743845730876" duration="1177000" />
      <workItem from="1743926030419" duration="18172000" />
      <workItem from="1743989506809" duration="13613000" />
      <workItem from="1744114173919" duration="707000" />
      <workItem from="1744166161829" duration="2149000" />
      <workItem from="1744245358121" duration="3372000" />
      <workItem from="1744503665779" duration="103000" />
      <workItem from="1744504126763" duration="23000" />
      <workItem from="1744505142174" duration="127000" />
      <workItem from="1744505319807" duration="1836000" />
      <workItem from="1744599107011" duration="590000" />
      <workItem from="1744621722479" duration="37000" />
      <workItem from="1744724494629" duration="20000" />
      <workItem from="1745076144011" duration="295000" />
      <workItem from="1745110857561" duration="2099000" />
      <workItem from="1745203631830" duration="2685000" />
      <workItem from="1745216948051" duration="6511000" />
      <workItem from="1745373753340" duration="28419000" />
      <workItem from="1745461206778" duration="3784000" />
      <workItem from="1745503755550" duration="16767000" />
      <workItem from="1745636795800" duration="1252000" />
      <workItem from="1745806614778" duration="4648000" />
      <workItem from="1745892123325" duration="1709000" />
      <workItem from="1745974580142" duration="619000" />
      <workItem from="1746062389392" duration="5780000" />
      <workItem from="1746145958662" duration="187000" />
      <workItem from="1746147716864" duration="4163000" />
      <workItem from="1746152738465" duration="22899000" />
      <workItem from="1746232542735" duration="19926000" />
      <workItem from="1746321760158" duration="10107000" />
      <workItem from="1746407117787" duration="23196000" />
      <workItem from="1746528778471" duration="14440000" />
      <workItem from="1746665693771" duration="20322000" />
      <workItem from="1746746840080" duration="21817000" />
      <workItem from="1746855680710" duration="3307000" />
      <workItem from="1746926332223" duration="1310000" />
      <workItem from="1746928156015" duration="37273000" />
      <workItem from="1747010803406" duration="566000" />
      <workItem from="1747011444408" duration="29941000" />
      <workItem from="1747096269948" duration="16951000" />
      <workItem from="1747185071839" duration="26623000" />
      <workItem from="1747272617162" duration="15084000" />
      <workItem from="1747304400214" duration="4456000" />
      <workItem from="1747355185226" duration="16929000" />
      <workItem from="1747450050061" duration="121000" />
      <workItem from="1747451952961" duration="14310000" />
      <workItem from="1747536357374" duration="6988000" />
      <workItem from="1747561167492" duration="10776000" />
      <workItem from="1747620018061" duration="7163000" />
      <workItem from="1747641690860" duration="23088000" />
      <workItem from="1747668797191" duration="7990000" />
      <workItem from="1747711579698" duration="20981000" />
      <workItem from="1747755702235" duration="3078000" />
      <workItem from="1747794653766" duration="65000" />
      <workItem from="1747816204556" duration="19840000" />
      <workItem from="1747878512104" duration="7570000" />
      <workItem from="1747888515148" duration="24711000" />
      <workItem from="1747961628659" duration="25838000" />
      <workItem from="1748045649803" duration="5573000" />
      <workItem from="1748057140222" duration="26642000" />
      <workItem from="1748135306985" duration="4027000" />
      <workItem from="1748155457047" duration="24865000" />
      <workItem from="1748220802115" duration="20249000" />
    </task>
    <task id="LOCAL-00169" summary="add type">
      <option name="closed" value="true" />
      <created>1709468608782</created>
      <option name="number" value="00169" />
      <option name="presentableId" value="LOCAL-00169" />
      <option name="project" value="LOCAL" />
      <updated>1709468608782</updated>
    </task>
    <task id="LOCAL-00170" summary="fix labels">
      <option name="closed" value="true" />
      <created>1709556855092</created>
      <option name="number" value="00170" />
      <option name="presentableId" value="LOCAL-00170" />
      <option name="project" value="LOCAL" />
      <updated>1709556855092</updated>
    </task>
    <task id="LOCAL-00171" summary="change name">
      <option name="closed" value="true" />
      <created>1743049746366</created>
      <option name="number" value="00171" />
      <option name="presentableId" value="LOCAL-00171" />
      <option name="project" value="LOCAL" />
      <updated>1743049746367</updated>
    </task>
    <task id="LOCAL-00172" summary="change name">
      <option name="closed" value="true" />
      <created>1743049881618</created>
      <option name="number" value="00172" />
      <option name="presentableId" value="LOCAL-00172" />
      <option name="project" value="LOCAL" />
      <updated>1743049881619</updated>
    </task>
    <task id="LOCAL-00173" summary="[Add] api Ticket, Notification, Affiliate&#10;[Edit] type,">
      <option name="closed" value="true" />
      <created>1743843409425</created>
      <option name="number" value="00173" />
      <option name="presentableId" value="LOCAL-00173" />
      <option name="project" value="LOCAL" />
      <updated>1743843409425</updated>
    </task>
    <task id="LOCAL-00174" summary="add full api">
      <option name="closed" value="true" />
      <created>1746168834899</created>
      <option name="number" value="00174" />
      <option name="presentableId" value="LOCAL-00174" />
      <option name="project" value="LOCAL" />
      <updated>1746168834899</updated>
    </task>
    <task id="LOCAL-00175" summary="add full api">
      <option name="closed" value="true" />
      <created>1746237341885</created>
      <option name="number" value="00175" />
      <option name="presentableId" value="LOCAL-00175" />
      <option name="project" value="LOCAL" />
      <updated>1746237341886</updated>
    </task>
    <task id="LOCAL-00176" summary="transfer to offsetDateTime">
      <option name="closed" value="true" />
      <created>1746332613510</created>
      <option name="number" value="00176" />
      <option name="presentableId" value="LOCAL-00176" />
      <option name="project" value="LOCAL" />
      <updated>1746332613510</updated>
    </task>
    <task id="LOCAL-00177" summary="add custom special discount">
      <option name="closed" value="true" />
      <created>1746676664679</created>
      <option name="number" value="00177" />
      <option name="presentableId" value="LOCAL-00177" />
      <option name="project" value="LOCAL" />
      <updated>1746676664679</updated>
    </task>
    <task id="LOCAL-00178" summary="add all api">
      <option name="closed" value="true" />
      <created>1747014078033</created>
      <option name="number" value="00178" />
      <option name="presentableId" value="LOCAL-00178" />
      <option name="project" value="LOCAL" />
      <updated>1747014078033</updated>
    </task>
    <task id="LOCAL-00179" summary="add all api">
      <option name="closed" value="true" />
      <created>1747291846910</created>
      <option name="number" value="00179" />
      <option name="presentableId" value="LOCAL-00179" />
      <option name="project" value="LOCAL" />
      <updated>1747291846910</updated>
    </task>
    <task id="LOCAL-00180" summary="config master">
      <option name="closed" value="true" />
      <created>1747316791340</created>
      <option name="number" value="00180" />
      <option name="presentableId" value="LOCAL-00180" />
      <option name="project" value="LOCAL" />
      <updated>1747316791340</updated>
    </task>
    <task id="LOCAL-00181" summary="add sql">
      <option name="closed" value="true" />
      <created>1747317598284</created>
      <option name="number" value="00181" />
      <option name="presentableId" value="LOCAL-00181" />
      <option name="project" value="LOCAL" />
      <updated>1747317598284</updated>
    </task>
    <task id="LOCAL-00182" summary="update job">
      <option name="closed" value="true" />
      <created>1747358796454</created>
      <option name="number" value="00182" />
      <option name="presentableId" value="LOCAL-00182" />
      <option name="project" value="LOCAL" />
      <updated>1747358796454</updated>
    </task>
    <task id="LOCAL-00183" summary="fix enum comment">
      <option name="closed" value="true" />
      <created>1747363138727</created>
      <option name="number" value="00183" />
      <option name="presentableId" value="LOCAL-00183" />
      <option name="project" value="LOCAL" />
      <updated>1747363138727</updated>
    </task>
    <task id="LOCAL-00184" summary="fix enum comment">
      <option name="closed" value="true" />
      <created>1747366386803</created>
      <option name="number" value="00184" />
      <option name="presentableId" value="LOCAL-00184" />
      <option name="project" value="LOCAL" />
      <updated>1747366386804</updated>
    </task>
    <task id="LOCAL-00185" summary="fix search orders">
      <option name="closed" value="true" />
      <created>1747369306666</created>
      <option name="number" value="00185" />
      <option name="presentableId" value="LOCAL-00185" />
      <option name="project" value="LOCAL" />
      <updated>1747369306666</updated>
    </task>
    <task id="LOCAL-00186" summary="fix search orders">
      <option name="closed" value="true" />
      <created>1747369535249</created>
      <option name="number" value="00186" />
      <option name="presentableId" value="LOCAL-00186" />
      <option name="project" value="LOCAL" />
      <updated>1747369535249</updated>
    </task>
    <task id="LOCAL-00187" summary="add refill and cancel">
      <option name="closed" value="true" />
      <created>1747376889385</created>
      <option name="number" value="00187" />
      <option name="presentableId" value="LOCAL-00187" />
      <option name="project" value="LOCAL" />
      <updated>1747376889385</updated>
    </task>
    <task id="LOCAL-00188" summary="cancel order">
      <option name="closed" value="true" />
      <created>1747456451353</created>
      <option name="number" value="00188" />
      <option name="presentableId" value="LOCAL-00188" />
      <option name="project" value="LOCAL" />
      <updated>1747456451354</updated>
    </task>
    <task id="LOCAL-00189" summary="cancel order">
      <option name="closed" value="true" />
      <created>1747456491284</created>
      <option name="number" value="00189" />
      <option name="presentableId" value="LOCAL-00189" />
      <option name="project" value="LOCAL" />
      <updated>1747456491284</updated>
    </task>
    <task id="LOCAL-00190" summary="add message cancel order">
      <option name="closed" value="true" />
      <created>1747456602626</created>
      <option name="number" value="00190" />
      <option name="presentableId" value="LOCAL-00190" />
      <option name="project" value="LOCAL" />
      <updated>1747456602626</updated>
    </task>
    <task id="LOCAL-00191" summary="fix status order">
      <option name="closed" value="true" />
      <created>1747457106050</created>
      <option name="number" value="00191" />
      <option name="presentableId" value="LOCAL-00191" />
      <option name="project" value="LOCAL" />
      <updated>1747457106050</updated>
    </task>
    <task id="LOCAL-00192" summary="fix status order">
      <option name="closed" value="true" />
      <created>1747457503764</created>
      <option name="number" value="00192" />
      <option name="presentableId" value="LOCAL-00192" />
      <option name="project" value="LOCAL" />
      <updated>1747457503765</updated>
    </task>
    <task id="LOCAL-00193" summary="fix status order">
      <option name="closed" value="true" />
      <created>1747550581970</created>
      <option name="number" value="00193" />
      <option name="presentableId" value="LOCAL-00193" />
      <option name="project" value="LOCAL" />
      <updated>1747550581970</updated>
    </task>
    <task id="LOCAL-00194" summary="fix status order">
      <option name="closed" value="true" />
      <created>1747551752350</created>
      <option name="number" value="00194" />
      <option name="presentableId" value="LOCAL-00194" />
      <option name="project" value="LOCAL" />
      <updated>1747551752350</updated>
    </task>
    <task id="LOCAL-00195" summary="fix status order">
      <option name="closed" value="true" />
      <created>1747551867759</created>
      <option name="number" value="00195" />
      <option name="presentableId" value="LOCAL-00195" />
      <option name="project" value="LOCAL" />
      <updated>1747551867759</updated>
    </task>
    <task id="LOCAL-00196" summary="fix status order">
      <option name="closed" value="true" />
      <created>1747552291230</created>
      <option name="number" value="00196" />
      <option name="presentableId" value="LOCAL-00196" />
      <option name="project" value="LOCAL" />
      <updated>1747552291230</updated>
    </task>
    <task id="LOCAL-00197" summary="fix status order">
      <option name="closed" value="true" />
      <created>1747552789404</created>
      <option name="number" value="00197" />
      <option name="presentableId" value="LOCAL-00197" />
      <option name="project" value="LOCAL" />
      <updated>1747552789404</updated>
    </task>
    <task id="LOCAL-00198" summary="delete domain">
      <option name="closed" value="true" />
      <created>1747565956014</created>
      <option name="number" value="00198" />
      <option name="presentableId" value="LOCAL-00198" />
      <option name="project" value="LOCAL" />
      <updated>1747565956015</updated>
    </task>
    <task id="LOCAL-00199" summary="delete domain">
      <option name="closed" value="true" />
      <created>1747566687309</created>
      <option name="number" value="00199" />
      <option name="presentableId" value="LOCAL-00199" />
      <option name="project" value="LOCAL" />
      <updated>1747566687309</updated>
    </task>
    <task id="LOCAL-00200" summary="fix reset password and password length">
      <option name="closed" value="true" />
      <created>1747576857407</created>
      <option name="number" value="00200" />
      <option name="presentableId" value="LOCAL-00200" />
      <option name="project" value="LOCAL" />
      <updated>1747576857407</updated>
    </task>
    <task id="LOCAL-00201" summary="fix user req">
      <option name="closed" value="true" />
      <created>1747581674016</created>
      <option name="number" value="00201" />
      <option name="presentableId" value="LOCAL-00201" />
      <option name="project" value="LOCAL" />
      <updated>1747581674016</updated>
    </task>
    <task id="LOCAL-00202" summary="fix mapper">
      <option name="closed" value="true" />
      <created>1747623706822</created>
      <option name="number" value="00202" />
      <option name="presentableId" value="LOCAL-00202" />
      <option name="project" value="LOCAL" />
      <updated>1747623706823</updated>
    </task>
    <task id="LOCAL-00203" summary="fix mapper">
      <option name="closed" value="true" />
      <created>1747633439317</created>
      <option name="number" value="00203" />
      <option name="presentableId" value="LOCAL-00203" />
      <option name="project" value="LOCAL" />
      <updated>1747633439317</updated>
    </task>
    <task id="LOCAL-00204" summary="fix filter">
      <option name="closed" value="true" />
      <created>1747722865819</created>
      <option name="number" value="00204" />
      <option name="presentableId" value="LOCAL-00204" />
      <option name="project" value="LOCAL" />
      <updated>1747722865820</updated>
    </task>
    <task id="LOCAL-00205" summary="fix transaction">
      <option name="closed" value="true" />
      <created>1747842102008</created>
      <option name="number" value="00205" />
      <option name="presentableId" value="LOCAL-00205" />
      <option name="project" value="LOCAL" />
      <updated>1747842102008</updated>
    </task>
    <task id="LOCAL-00206" summary="fix transaction">
      <option name="closed" value="true" />
      <created>1747842138244</created>
      <option name="number" value="00206" />
      <option name="presentableId" value="LOCAL-00206" />
      <option name="project" value="LOCAL" />
      <updated>1747842138244</updated>
    </task>
    <task id="LOCAL-00207" summary="fix GServiceScheduler">
      <option name="closed" value="true" />
      <created>1747885653449</created>
      <option name="number" value="00207" />
      <option name="presentableId" value="LOCAL-00207" />
      <option name="project" value="LOCAL" />
      <updated>1747885653449</updated>
    </task>
    <task id="LOCAL-00208" summary="fix jackson">
      <option name="closed" value="true" />
      <created>1747934598313</created>
      <option name="number" value="00208" />
      <option name="presentableId" value="LOCAL-00208" />
      <option name="project" value="LOCAL" />
      <updated>1747934598314</updated>
    </task>
    <task id="LOCAL-00209" summary="fix jackson">
      <option name="closed" value="true" />
      <created>1747934665978</created>
      <option name="number" value="00209" />
      <option name="presentableId" value="LOCAL-00209" />
      <option name="project" value="LOCAL" />
      <updated>1747934665978</updated>
    </task>
    <task id="LOCAL-00210" summary="fix sync">
      <option name="closed" value="true" />
      <created>1747974839493</created>
      <option name="number" value="00210" />
      <option name="presentableId" value="LOCAL-00210" />
      <option name="project" value="LOCAL" />
      <updated>1747974839493</updated>
    </task>
    <task id="LOCAL-00211" summary="notification">
      <option name="closed" value="true" />
      <created>1748088472996</created>
      <option name="number" value="00211" />
      <option name="presentableId" value="LOCAL-00211" />
      <option name="project" value="LOCAL" />
      <updated>1748088472996</updated>
    </task>
    <task id="LOCAL-00212" summary="fix cancel api">
      <option name="closed" value="true" />
      <created>1748148719382</created>
      <option name="number" value="00212" />
      <option name="presentableId" value="LOCAL-00212" />
      <option name="project" value="LOCAL" />
      <updated>1748148719383</updated>
    </task>
    <task id="LOCAL-00213" summary="fix run job">
      <option name="closed" value="true" />
      <created>1748165248566</created>
      <option name="number" value="00213" />
      <option name="presentableId" value="LOCAL-00213" />
      <option name="project" value="LOCAL" />
      <updated>1748165248567</updated>
    </task>
    <task id="LOCAL-00214" summary="fix signup">
      <option name="closed" value="true" />
      <created>1748170044403</created>
      <option name="number" value="00214" />
      <option name="presentableId" value="LOCAL-00214" />
      <option name="project" value="LOCAL" />
      <updated>1748170044403</updated>
    </task>
    <task id="LOCAL-00215" summary="fix signup lang">
      <option name="closed" value="true" />
      <created>1748170497919</created>
      <option name="number" value="00215" />
      <option name="presentableId" value="LOCAL-00215" />
      <option name="project" value="LOCAL" />
      <updated>1748170497919</updated>
    </task>
    <task id="LOCAL-00216" summary="fix entity">
      <option name="closed" value="true" />
      <created>1748223035888</created>
      <option name="number" value="00216" />
      <option name="presentableId" value="LOCAL-00216" />
      <option name="project" value="LOCAL" />
      <updated>1748223035888</updated>
    </task>
    <task id="LOCAL-00217" summary="accept html">
      <option name="closed" value="true" />
      <created>1748237751784</created>
      <option name="number" value="00217" />
      <option name="presentableId" value="LOCAL-00217" />
      <option name="project" value="LOCAL" />
      <updated>1748237751784</updated>
    </task>
    <option name="localTasksCounter" value="218" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="a897a9e0-0ef0-4a01-be85-2d10f96cbc6d" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="a897a9e0-0ef0-4a01-be85-2d10f96cbc6d">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:E:/DUAN_2022/vnfb-uplike/smm-system" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix enum comment" />
    <MESSAGE value="fix search orders" />
    <MESSAGE value="add refill and cancel" />
    <MESSAGE value="cancel order" />
    <MESSAGE value="add message cancel order" />
    <MESSAGE value="fix status order" />
    <MESSAGE value="delete domain" />
    <MESSAGE value="fix reset password" />
    <MESSAGE value="fix reset password and password length" />
    <MESSAGE value="fix user req" />
    <MESSAGE value="fix mapper" />
    <MESSAGE value="add new panel" />
    <MESSAGE value="fix filter" />
    <MESSAGE value="fix transaction" />
    <MESSAGE value="fix GServiceScheduler" />
    <MESSAGE value="fix jackson" />
    <MESSAGE value="fix sync" />
    <MESSAGE value="notification" />
    <MESSAGE value="fix cancel api" />
    <MESSAGE value="fix run job" />
    <MESSAGE value="fix signup" />
    <MESSAGE value="fix signup lang" />
    <MESSAGE value="fix request cancel" />
    <MESSAGE value="fix entity" />
    <MESSAGE value="accept html" />
    <option name="LAST_COMMIT_MESSAGE" value="accept html" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/dto/request/ServiceReq.java</url>
          <line>14</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/SMMSystemApplication.java</url>
          <line>21</line>
          <option name="timeStamp" value="36" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/service/impl/AccessServiceImpl.java</url>
          <line>253</line>
          <option name="timeStamp" value="60" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/aop/UserActivityLoggingAspect.java</url>
          <line>38</line>
          <option name="timeStamp" value="90" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/service/impl/GUserServiceImpl.java</url>
          <line>516</line>
          <option name="timeStamp" value="91" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/service/impl/ApiProviderServiceImpl.java</url>
          <line>79</line>
          <option name="timeStamp" value="97" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/service/impl/OrderServiceImpl.java</url>
          <line>80</line>
          <option name="timeStamp" value="101" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/service/impl/OrderServiceImpl.java</url>
          <line>246</line>
          <option name="timeStamp" value="102" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/repository/tenant/NotificationRepository.java</url>
          <line>20</line>
          <properties class="tndung.vnfb.smm.repository.tenant.NotificationRepository">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="35" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/tndung/vnfb/smm/controller/DesignSettingsController.java</url>
          <line>123</line>
          <properties class="tndung.vnfb.smm.controller.DesignSettingsController" method="updateHeaderSettings">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest" memberName="request" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="authenticationFacade.getAuthentication()" language="JAVA" />
        <watch expression="entityManager" language="JAVA" />
        <watch expression="zoneHolder.get()" language="JAVA" />
        <watch expression="TenantContext.getCurrentTenant()" language="JAVA" custom="tndung.vnfb.smm.config.TenantContext" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>