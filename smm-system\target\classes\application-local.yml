#server:
#  address: ***********

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://**************:5432/smm_system
    username: postgres
    password: dung12345678a@
  jpa:
    show-sql: true
#    generate-ddl: true
#    hibernate:
#      ddl-auto: create

  redis:
    host: **************
    port: 6379
    password: dung12345678a@*
    database: 3
  mail:
    host: hash
    port: 587
    username: dummy
    password: 123456

  # Disable all scheduled tasks in local environment
#  task:
#    scheduling:
#      enabled: false


logging:
  file:
    name: ./logs/smm-system.log
  level:
    org:
      springframework:
        data:
          mongodb:
            core:
              MongoTemplate: DEBUG
    tndung.vnfb.smm.config.TenantAuthenticationFilter: debug
    tndung.vnfb.smm.config.AuthenticationFilter: debug
    tndung.vnfb.smm.config.TenantContext: debug

# Domain manager configuration
domain-manager:
  url: http://**************:3000

# File upload configuration for local development
file:
  upload:
    dir: /app/uploads
    max-size: 10MB
  cdn:
    url: http://**************/cdn