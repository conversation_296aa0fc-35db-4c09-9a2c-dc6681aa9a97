#exception
iex.permission.denied=permission denied
iex.token.invalid=invalid token or token had expired!
iex.internal.server.error=Internal Server Error
iex.malformed.json.request=Malformed JSON request
iex.read.resource.error=Read resource error
iex.mfa.required=mfa required
iex.otp.incorrect=otp incorrect
iex.invalid.request=Invalid request
iex.session.incorrect=session incorrect or session had expired!
iex.bad.credential=Bad credential
mfa.is.on=mfa is on
mfa.is.off=mfa is off


iex.user.exists=User name or email exists
iex.old.password.wrong=old password wrong
iex.category.not.exists=category not exists
iex.platform.not.exists=platform not exists
iex.service.not.exists=service not exists
iex.provider.not.exists=provider not exists
iex.action.invalid=action invalid
iex.action.not.found=action not found
iex.user.not.found=user not found
iex.order.not.found=order not found
iex.password.wrong=password wrong
iex.provider.url.exists=provider url exists
iex.category.name.exists=category name exists
iex.service.name.exists=service name exists
iex.platform.name.exists=platform name exists
iex.user.name.exists=User name exists
iex.user.email.exists=Email exists
iex.user.phone.exists=Phone exists
iex.user.account.deactivated=Account has been locked. Please contact admin.

validation.userName.NotBlank=user name cannot be blank
validation.userName.Length=user name must be between 5 and 30 characters

validation.email.NotBlank=Email cannot be blank
validation.email.Email=Invalid email format

validation.password.ValidPassword=Password must be between 8 and 30 characters, contain at least 1 uppercase letter, 1 digit, and 1 special character
validation.password.NotBlank=Password cannot be blank

validation.oldPassword.NotBlank=Old password cannot be blank

validation.role.NotBlank=Role cannot be blank
validation.status.NotBlank=Status cannot be blank

order.refund.note=You have been refunded {0} from order with id {1}
order.create.note=You have ordered {0} service with quantity {1}, order id {2}, total amount {3}


iex.user.lang.invalid=Invalid language
iex.user.referral.notfound=Referral code not found
iex.notification.notfound=Notification not found
iex.ticket.notfound=Ticket not found
iex.updatelog.notfound=Update log not found
iex.cannot.delete=Cannot delete default item
iex.discount.not.valid=Discount code is not valid
iex.special.price.not.found=Special price not found
iex.balance.not.enough=Insufficient balance

iex.currency.not.found=Currency not found
iex.base.currency.not.configured=Base currency not configured
iex.voucher.exists=Voucher already exists
iex.voucher.not.valid=Voucher is not valid
iex.voucher.not.found=Voucher not found
iex.quantity.not.valid=Quantity is not valid
date.start.after.end=Start date cannot be after end date
date.end.in.past=End date cannot be in the past
cannot.cancel.order=Cannot cancel the order.
price.special.less.than.original=Special price must not be less than original price
percentage.out.of.range=Percentage must be between 0 and 100
domain.already.exists=Domain already exists
tenant.access.denied=You don't have access to this tenant
design.not.exists=Design does not exist
tenant.not.found=Tenant not found
panel.not.configured=No panel has been configured
invalid.status.transition=Invalid order status transition
transaction.not.found=Transaction not found
order.refund.note=Refund for order #{1}: {0}
