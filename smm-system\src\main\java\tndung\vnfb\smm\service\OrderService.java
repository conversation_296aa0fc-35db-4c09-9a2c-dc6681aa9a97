package tndung.vnfb.smm.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import tndung.vnfb.smm.constant.enums.OrderStatus;
import tndung.vnfb.smm.dto.request.MyOrderSearchReq;
import tndung.vnfb.smm.dto.request.OrderReq;
import tndung.vnfb.smm.dto.request.PartialReq;
import tndung.vnfb.smm.dto.request.SearchOrderReq;
import tndung.vnfb.smm.dto.response.OrderRes;
import tndung.vnfb.smm.dto.response.SuperOrderRes;
import tndung.vnfb.smm.dto.response.smm.SMMOrderStatusRes;
import tndung.vnfb.smm.entity.GOrder;

public interface OrderService {
    SMMOrderStatusRes getSSMOrderStatus(Long id);

    Object getMultipleSSMOrderStatus(String orders);
    OrderRes add(OrderReq order);

    OrderRes cancel(Long id);

    OrderRes refill(Long id);

//    OrderRes updateOrderStatus(Long id, OrderStatus orderStatus);

    OrderRes updateOrderStatus(Long id, OrderStatus orderStatus, Integer remains);

    // Individual status update methods
    OrderRes updateToCompleted(Long id);

    OrderRes updateToPartial(Long id, Integer remains);

    OrderRes updateToCancel(Long id);

    OrderRes updateToInProgress(Long id);

    OrderRes updateToPending(Long id);

    OrderRes updateToProcessing(Long id);

    OrderRes updateToFailed(Long id);

//    OrderRes changePartialWithRefund(Long id, PartialReq req);

//    OrderRes changeCompleted(Long id);

    OrderRes changeCancel(Long id);

    OrderRes updateApiOderId(Long id, String orderId);

    OrderRes updateStartCount(Long id, Integer startCount);

    OrderRes updateLink(Long id, String link);

    OrderRes updateNote(Long id, String note);

    Page<OrderRes> getOrdersByUser(Long userId, Pageable pageable);

    Page<SuperOrderRes> search(SearchOrderReq req, Pageable pageable);

    Page<OrderRes> searchMyOrder(MyOrderSearchReq req, Pageable pageable);

    GOrder findById(Long id);

    GOrder fetchStatus(GOrder order);

    OrderRes fetchStatus(Long orderId);
}
