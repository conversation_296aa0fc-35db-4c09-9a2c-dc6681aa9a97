package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.IntegrationReq;
import tndung.vnfb.smm.dto.response.IntegrationRes;
import tndung.vnfb.smm.entity.Integration;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class IntegrationMapperImpl implements IntegrationMapper {

    @Override
    public IntegrationRes toRes(Integration entity) {
        if ( entity == null ) {
            return null;
        }

        IntegrationRes integrationRes = new IntegrationRes();

        integrationRes.setActive( entity.getActive() );
        integrationRes.setIcon( entity.getIcon() );
        integrationRes.setId( entity.getId() );
        integrationRes.setKey( entity.getKey() );
        integrationRes.setPosition( entity.getPosition() );
        integrationRes.setValue( entity.getValue() );

        return integrationRes;
    }

    @Override
    public List<IntegrationRes> toRes(List<Integration> entity) {
        if ( entity == null ) {
            return null;
        }

        List<IntegrationRes> list = new ArrayList<IntegrationRes>( entity.size() );
        for ( Integration integration : entity ) {
            list.add( toRes( integration ) );
        }

        return list;
    }

    @Override
    public Integration toEntity(IntegrationReq req) {
        if ( req == null ) {
            return null;
        }

        Integration integration = new Integration();

        integration.setKey( req.getKey() );
        integration.setPosition( req.getPosition() );
        integration.setValue( req.getValue() );

        return integration;
    }

    @Override
    public void updateEntityFromDto(IntegrationReq dto, Integration entity) {
        if ( dto == null ) {
            return;
        }

        entity.setKey( dto.getKey() );
        entity.setPosition( dto.getPosition() );
        entity.setValue( dto.getValue() );
    }
}
