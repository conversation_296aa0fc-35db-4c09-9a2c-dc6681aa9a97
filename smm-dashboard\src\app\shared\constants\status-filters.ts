/**
 * Status filters for orders
 * Used in both admin and user order components
 */
export interface StatusFilter {
  value: string;
  label: string;
  active: boolean;
}

// export const STATUS_FILTERS: StatusFilter[] = [
//   { value: 'all', label: 'All', active: true },
//   { value: 'in_progress', label: 'In Progress', active: false },
//   { value: 'failed', label: 'Failed', active: false },
//   { value: 'partial', label: 'Partial', active: false },
//   { value: 'canceled', label: 'Canceled', active: false },
//   { value: 'pending', label: 'Pending', active: false },
//   { value: 'completed', label: 'Completed', active: false }
// ];

// Admin version uses uppercase values
export const STATUS_FILTERS: StatusFilter[] = [
  { value: 'all', label: 'All', active: true },
  { value: 'IN_PROGRESS', label: 'In Progress', active: false },
  { value: 'FAILED', label: 'Failed', active: false },
  { value: 'PARTIAL', label: 'Partial', active: false },

  { value: 'CANCELED', label: 'Canceled', active: false },
  { value: 'PENDING', label: 'Pending', active: false },
  { value: 'COMPLETED', label: 'Completed', active: false }
];
