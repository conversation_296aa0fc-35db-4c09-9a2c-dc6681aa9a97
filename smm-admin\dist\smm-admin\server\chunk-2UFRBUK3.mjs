import './polyfills.server.mjs';
import{a as L,b as z,c as U,d as H,e as W,f as X}from"./chunk-MIBVTFHR.mjs";import{S as Y,T as G,c as R,d as D,e as V,i as j,j as $}from"./chunk-PXCSKUZG.mjs";import{Ea as v,Ga as r,La as a,Ma as o,Na as u,P as E,Qa as C,Sa as b,Ta as x,Tb as N,Ub as O,Vb as k,Y as h,Z as w,_ as I,_b as B,ab as d,bb as _,bc as A,cb as g,lb as T,mb as f,pb as M,qb as F,sb as p,ta as t,tb as m,ua as S,ub as P}from"./chunk-XR72HQ3V.mjs";import"./chunk-2FGBTQRU.mjs";var K=()=>["fas","lightbulb"],Q=()=>["fas","info-circle"],Z=(n,c,i)=>({"text-yellow-600 font-medium":n,"text-green-600 font-medium":c,"text-red-500":i}),q=()=>["fas","undo"],J=()=>["fas","server"],ee=(n,c,i,e)=>({"text-red-600 font-medium":n,"text-yellow-600 font-medium":c,"text-green-600":i,"text-gray-500":e}),te=()=>["fas","sync-alt"],ie=()=>["fas","ellipsis-h"];function ne(n,c){n&1&&(a(0,"span"),d(1),p(2,"translate"),o()),n&2&&(t(),_(m(2,1,"New panel")))}function ae(n,c){n&1&&(a(0,"span",4),I(),a(1,"svg",18),u(2,"circle",19)(3,"path",20),o(),d(4),p(5,"translate"),o()),n&2&&(t(4),g(" ",m(5,1,"Creating...")," "))}function oe(n,c){n&1&&u(0,"app-loading")}function re(n,c){if(n&1){let i=C();a(0,"button",37),b("click",function(){h(i);let l=x().$implicit,s=x(2);return w(s.onRestore(l))}),u(1,"fa-icon",38),d(2),p(3,"translate"),o()}n&2&&(t(),r("icon",f(4,q)),t(),g(" ",m(3,2,"Restore")," "))}function le(n,c){if(n&1){let i=C();a(0,"tr",27)(1,"td",28),d(2),o(),a(3,"td",29),d(4),p(5,"date"),o(),a(6,"td",29)(7,"span",30),d(8),o()(),a(9,"td",31)(10,"app-toggle-switch",32),b("toggled",function(l){let s=h(i).$implicit,y=x(2);return w(y.onToggleChange(l,s))}),o()(),a(11,"td",31)(12,"span",33),d(13),o()(),a(14,"td",34)(15,"div",35),v(16,re,4,5,"button",36),o()()()}if(n&2){let i=c.$implicit,e=x(2);t(2),g(" ",i.domain," "),t(2),g(" ",P(5,8,i.subscription_end_date,"short")," "),t(3),r("ngClass",M(11,Z,i.days_until_expiration!==void 0&&i.days_until_expiration<=7,i.days_until_expiration!==void 0&&i.days_until_expiration>7&&i.days_until_expiration<=30,i.days_until_expiration===void 0||i.status==="Expired")),t(),g(" ",e.getDaysLeftText(i)," "),t(2),r("isChecked",i.auto_renewal),t(2),r("ngClass",e.getStatusClass(i.status)),t(),g(" ",e.getStatusLabel(i.status)," "),t(3),r("ngIf",i.status==="Expired")}}function se(n,c){n&1&&(a(0,"tr")(1,"td",39)(2,"div",40),u(3,"fa-icon",41),a(4,"p",42),d(5),p(6,"translate"),o()()()()),n&2&&(t(3),r("icon",f(4,J)),t(2),_(m(6,2,"No panels found")))}function de(n,c){if(n&1&&(a(0,"table",21)(1,"thead",22)(2,"tr")(3,"th",23),d(4),p(5,"translate"),o(),a(6,"th",23),d(7),p(8,"translate"),o(),a(9,"th",23),d(10),p(11,"translate"),o(),a(12,"th",23),d(13),p(14,"translate"),u(15,"fa-icon",24),o(),a(16,"th",23),d(17),p(18,"translate"),o(),a(19,"th",23),d(20),p(21,"translate"),o()()(),a(22,"tbody",25),v(23,le,17,15,"tr",26)(24,se,7,5,"tr",10),o()()),n&2){let i=x();t(4),g(" ",m(5,9,"DOMAIN")," "),t(3),g(" ",m(8,11,"EXPIRY")," "),t(3),g(" ",m(11,13,"DAYS LEFT")," "),t(3),g(" ",m(14,15,"RENEW")," "),t(2),r("icon",f(21,Q)),t(2),g(" ",m(18,17,"STATUS")," "),t(3),g(" ",m(21,19,"ACTIONS")," "),t(3),r("ngForOf",i.panels),t(),r("ngIf",i.panels.length===0)}}function ce(n,c){n&1&&u(0,"app-loading")}function pe(n,c){if(n&1){let i=C();a(0,"button",37),b("click",function(){h(i);let l=x().$implicit,s=x(2);return w(s.onRestore(l))}),u(1,"fa-icon",38),d(2),p(3,"translate"),o()}n&2&&(t(),r("icon",f(4,q)),t(),g(" ",m(3,2,"Restore")," "))}function me(n,c){if(n&1){let i=C();a(0,"div",46)(1,"div",47)(2,"div")(3,"div",48),d(4),o(),a(5,"div",49),d(6),p(7,"date"),o(),a(8,"div",50),d(9),o()(),a(10,"span",33),d(11),p(12,"translate"),o()(),a(13,"div",51)(14,"div",52)(15,"div",4),u(16,"fa-icon",53),a(17,"span",54),d(18),p(19,"translate"),o()(),a(20,"app-toggle-switch",32),b("toggled",function(l){let s=h(i).$implicit,y=x(2);return w(y.onToggleChange(l,s))}),o()()(),a(21,"div",55),v(22,pe,4,5,"button",36),a(23,"button",37),b("click",function(){let l=h(i).$implicit,s=x(2);return w(s.onActions(l))}),u(24,"fa-icon",38),d(25),p(26,"translate"),o()()()}if(n&2){let i=c.$implicit,e=x(2);t(4),_(i.domain),t(2),_(P(7,12,i.subscription_end_date,"short")),t(2),r("ngClass",F(21,ee,i.days_until_expiration!==void 0&&i.days_until_expiration<=7,i.days_until_expiration!==void 0&&i.days_until_expiration>7&&i.days_until_expiration<=30,i.days_until_expiration!==void 0&&i.days_until_expiration>30,i.days_until_expiration===void 0||i.status==="Expired")),t(),g(" ",e.getDaysLeftText(i)," "),t(),r("ngClass",e.getStatusClass(i.status)),t(),g(" ",m(12,15,e.getStatusLabel(i.status))," "),t(5),r("icon",f(26,te)),t(2),_(m(19,17,"Auto Renewal")),t(2),r("isChecked",i.auto_renewal),t(2),r("ngIf",i.status==="Expired"),t(2),r("icon",f(27,ie)),t(),g(" ",m(26,19,"Actions")," ")}}function ge(n,c){n&1&&(a(0,"div",56)(1,"div",40),u(2,"fa-icon",41),a(3,"p",42),d(4),p(5,"translate"),o()()()),n&2&&(t(2),r("icon",f(4,J)),t(2),_(m(5,2,"No panels found")))}function ue(n,c){if(n&1&&(a(0,"div",43),v(1,me,27,28,"div",44)(2,ge,6,5,"div",45),o()),n&2){let i=x();t(),r("ngForOf",i.panels),t(),r("ngIf",i.panels.length===0)}}var Ne=(()=>{let c=class c{constructor(e,l,s,y){this.tenantService=e,this.toastService=l,this.modalService=s,this.panelService=y,this.panels=[],this.loading=!1,this.currentStep=0,this.domainName="",this.isCreatingPanel=!1}ngOnInit(){this.loadPanels()}loadPanels(){this.loading=!0,this.tenantService.getAccessibleTenants().subscribe({next:e=>{this.panels=e,this.loading=!1},error:e=>{console.error("Error loading panels:",e),this.toastService.showError((e==null?void 0:e.message)||"Failed to load panels"),this.loading=!1}})}onToggleChange(e,l){console.log("Toggle changed for panel:",l,"new value:",e),this.tenantService.toggleAutoRenewal(l.id,e).subscribe({next:s=>{console.log("Auto-renewal toggle success:",s),l.auto_renewal=e,this.toastService.showSuccess(e?"Auto-renewal enabled":"Auto-renewal disabled")},error:s=>{console.error("Error toggling auto-renewal:",s),l.auto_renewal=!e,this.toastService.showError((s==null?void 0:s.message)||"Failed to update auto-renewal")}})}onRestore(e){console.log("Restore panel:",e),this.toastService.showSuccess("Restore functionality not implemented yet")}onActions(e){console.log("Actions for panel:",e),this.toastService.showSuccess("Actions menu not implemented yet")}getDaysLeftText(e){return e.status==="Expired"?"Expired":e.days_until_expiration===void 0||e.days_until_expiration===null?"N/A":e.days_until_expiration<0?"Expired":e.days_until_expiration===0?"Expires today":e.days_until_expiration===1?"1 day left":`${e.days_until_expiration} days left`}getStatusClass(e){if(!e)return"bg-gray-100 text-gray-800";switch(e){case"Active":return"bg-green-100 text-green-800 border-green-200";case"Failed":return"bg-red-100 text-red-800 border-red-200";case"Expired":return"bg-red-100 text-red-800 border-red-200";case"New":return"bg-blue-100 text-blue-800 border-blue-200";case"Nginx80":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"Configured":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}}getStatusLabel(e){if(!e)return"Unknown";switch(e){case"Ready":return"Active";case"Failed":return"Error";case"Expired":return"Expired";case"New":return"New";case"Nginx80":case"Configured":return"Configuring";case"Pending":return"Pending";default:return e}}formatDate(e){if(!e)return"N/A";try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch{return"Invalid Date"}}createPanel(){this.domainName="";let e=this.modalService.open(L,{domain:this.domainName});e.instance.close.subscribe(()=>{this.modalService.close()}),e.instance.next.subscribe(l=>{this.domainName=l,this.openStep2Modal()})}openStep2Modal(){this.modalService.close();let e=this.modalService.open(z,{domain:this.domainName});e.instance.close.subscribe(()=>{this.modalService.close()}),e.instance.back.subscribe(()=>{this.modalService.close(),this.createPanel()}),e.instance.next.subscribe(()=>{this.openStep3Modal()})}openStep3Modal(){this.modalService.close();let e=this.modalService.open(U,{domain:this.domainName});e.instance.close.subscribe(()=>{this.modalService.close()}),e.instance.back.subscribe(()=>{this.modalService.close(),this.openStep2Modal()}),e.instance.createPanel.subscribe(l=>{this.createPanelSubmit()})}createPanelSubmit(){this.isCreatingPanel=!0,this.panelService.createPanel(this.domainName).subscribe({next:e=>{this.toastService.showSuccess(`Panel created successfully for domain: ${this.domainName}`),this.modalService.close(),this.isCreatingPanel=!1,this.loadPanels()},error:e=>{console.error("Error creating panel:",e);let l=(e==null?void 0:e.message)||"Failed to create panel";this.toastService.showError(l),this.isCreatingPanel=!1}})}};c.\u0275fac=function(l){return new(l||c)(S(Y),S(G),S(W),S(H))},c.\u0275cmp=E({type:c,selectors:[["app-panels-setting"]],standalone:!0,features:[T],decls:28,vars:18,consts:[[1,"panel-layout-container","md:p-6","p-2"],[1,"mb-4"],[1,"text-xl","font-bold"],[1,"mb-6","p-4","bg-blue-50","border","border-blue-200","rounded-lg"],[1,"flex","items-center"],[1,"text-blue-600","mr-3",3,"icon"],[1,"text-blue-800","font-medium"],[1,"text-blue-600","text-sm"],[1,"mb-6"],[1,"px-4","py-2","bg-[#3b82f6]","text-white","rounded-lg","hover:bg-[#2563eb]","transition-colors","duration-200",3,"click","disabled"],[4,"ngIf"],["class","flex items-center",4,"ngIf"],[1,"hidden","md:block","overflow-x-auto"],[1,"inline-block","min-w-full","align-middle"],[1,"overflow-hidden","rounded-lg","shadow-sm"],["class","min-w-full divide-y divide-gray-200",4,"ngIf"],[1,"md:hidden"],["class","space-y-4",4,"ngIf"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24",1,"animate-spin","-ml-1","mr-2","h-4","w-4","text-white"],["cx","12","cy","12","r","10","stroke","currentColor","stroke-width","4",1,"opacity-25"],["fill","currentColor","d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",1,"opacity-75"],[1,"min-w-full","divide-y","divide-gray-200"],[1,"bg-gray-50"],["scope","col",1,"px-6","py-3","text-left","text-xs","font-medium","text-gray-500","uppercase","tracking-wider"],[1,"ml-1","text-gray-400",3,"icon"],[1,"bg-white","divide-y","divide-gray-200"],["class","hover:bg-[#f0f7ff] transition-colors duration-150",4,"ngFor","ngForOf"],[1,"hover:bg-[#f0f7ff]","transition-colors","duration-150"],[1,"px-6","py-4","whitespace-nowrap","text-sm","font-medium","text-gray-900"],[1,"px-6","py-4","whitespace-nowrap","text-sm","text-gray-700"],[3,"ngClass"],[1,"px-6","py-4","whitespace-nowrap"],["circleColor","#FFF","toggledBgColor","#3B82F6",3,"toggled","isChecked"],[1,"px-2","py-1","text-xs","font-medium","rounded-full",3,"ngClass"],[1,"px-6","py-4","whitespace-nowrap","text-sm","font-medium"],[1,"flex","space-x-2"],["class","inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200",3,"click",4,"ngIf"],[1,"inline-flex","items-center","px-3","py-1","border","border-gray-300","rounded-md","text-xs","text-gray-700","bg-white","hover:bg-gray-50","transition-colors","duration-200",3,"click"],[1,"mr-1",3,"icon"],["colspan","6",1,"px-6","py-12","text-center"],[1,"flex","flex-col","items-center","justify-center"],[1,"text-gray-300","text-4xl","mb-3",3,"icon"],[1,"text-gray-500"],[1,"space-y-4"],["class","bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:border-[#3b82f6] transition-colors duration-150",4,"ngFor","ngForOf"],["class","text-center py-12",4,"ngIf"],[1,"bg-white","rounded-lg","shadow-sm","border","border-gray-200","overflow-hidden","hover:border-[#3b82f6]","transition-colors","duration-150"],[1,"flex","justify-between","items-center","p-4","border-b","border-gray-100"],[1,"font-medium","text-gray-900"],[1,"text-sm","text-gray-500"],[1,"text-xs","mt-1",3,"ngClass"],[1,"p-4","space-y-3"],[1,"flex","items-center","justify-between"],[1,"text-gray-400","w-5",3,"icon"],[1,"ml-2","text-sm","text-gray-700"],[1,"px-4","py-3","bg-gray-50","flex","justify-end","space-x-2"],[1,"text-center","py-12"]],template:function(l,s){l&1&&(a(0,"div",0)(1,"div",1)(2,"h2",2),d(3),p(4,"translate"),o()(),a(5,"div",3)(6,"div",4),u(7,"fa-icon",5),a(8,"div")(9,"div",6),d(10),p(11,"translate"),o(),a(12,"div",7),d(13),p(14,"translate"),o()()()(),a(15,"div",8)(16,"button",9),b("click",function(){return s.createPanel()}),v(17,ne,3,3,"span",10)(18,ae,6,3,"span",11),o()(),a(19,"div")(20,"div",12)(21,"div",13)(22,"div",14),v(23,oe,1,0,"app-loading",10)(24,de,25,22,"table",15),o()()(),a(25,"div",16),v(26,ce,1,0,"app-loading",10)(27,ue,3,2,"div",17),o()()()),l&2&&(t(3),_(m(4,11,"Panels")),t(4),r("icon",f(17,K)),t(3),_(m(11,13,"Start your business with smart panel.")),t(3),_(m(14,15,"Price: $7.7 per month")),t(3),r("disabled",s.isCreatingPanel),t(),r("ngIf",!s.isCreatingPanel),t(),r("ngIf",s.isCreatingPanel),t(5),r("ngIf",s.loading),t(),r("ngIf",!s.loading),t(2),r("ngIf",s.loading),t(),r("ngIf",!s.loading))},dependencies:[A,N,O,k,B,$,j,D,R,X,V],styles:[".panel-layout-container[_ngcontent-%COMP%]{margin-left:auto;margin-right:auto;max-width:100%}table[_ngcontent-%COMP%]{width:100%;text-align:left;font-size:.875rem;--tw-text-opacity: 1;color:rgb(33 33 33 / var(--tw-text-opacity, 1))}thead[_ngcontent-%COMP%]{font-size:.75rem;--tw-text-opacity: 1;color:rgb(158 158 158 / var(--tw-text-opacity, 1))}th[_ngcontent-%COMP%], td[_ngcontent-%COMP%]{padding:.75rem 1.5rem}tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(238 238 238 / var(--tw-border-opacity, 1))}.status-badge[_ngcontent-%COMP%]{border-radius:9999px;padding:.25rem .5rem;font-size:.75rem;font-weight:500}.mobile-card[_ngcontent-%COMP%]{overflow:hidden;border-radius:.5rem;border-width:1px;--tw-border-opacity: 1;border-color:rgb(238 238 238 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.mobile-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(245 245 245 / var(--tw-border-opacity, 1));padding:1rem}.mobile-card-body[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.75rem * var(--tw-space-y-reverse))}.mobile-card-body[_ngcontent-%COMP%]{padding:1rem}.mobile-card-footer[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;--tw-bg-opacity: 1;background-color:rgb(250 250 250 / var(--tw-bg-opacity, 1));padding:.75rem 1rem}.mobile-card-item[_ngcontent-%COMP%]{display:flex;align-items:center}.mobile-card-icon[_ngcontent-%COMP%]{width:1.25rem;--tw-text-opacity: 1;color:rgb(189 189 189 / var(--tw-text-opacity, 1))}.mobile-card-text[_ngcontent-%COMP%]{margin-left:.5rem;font-size:.875rem;--tw-text-opacity: 1;color:rgb(97 97 97 / var(--tw-text-opacity, 1))}@media (max-width: 768px){.layout-container[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}}"]});let n=c;return n})();export{Ne as PanelsSettingComponent};
