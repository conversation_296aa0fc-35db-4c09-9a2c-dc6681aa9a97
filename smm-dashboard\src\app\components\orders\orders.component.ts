import { Component, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';

import { IconDropdownComponent } from "../common/icon-dropdown/icon-dropdown.component";
import { FormsModule } from '@angular/forms';
import { Clipboard } from '@angular/cdk/clipboard';
import { TranslateModule } from '@ngx-translate/core';
import { DateRangePickerComponent } from '../common/date-range-picker/date-range-picker.component';
import { SearchBoxComponent } from '../common/search-box/search-box.component';
import { ServiceDropdownComponent } from "../common/service-dropdown/service-dropdown.component";
import { CategoriesService } from '../../core/services/categories.service';
import { SuperPlatformRes } from '../../model/response/super-platform.model';
import { IconBaseModel } from '../../model/base-model';

import { OrderRes } from '../../model/response/order-res.model';
import { OrderService, OrderSearchReq } from '../../core/services/order.service';
import { Subscription } from 'rxjs';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';
import { ToastService } from '../../core/services/toast.service';
import { CurrencyService } from '../../core/services/currency.service';
import { CurrencyConvertPipe } from '../../core/pipes/currency-convert.pipe';
import { LoadingComponent } from '../common/loading/loading.component';
import { STATUS_FILTERS, StatusFilter } from '../../shared/constants/status-filters';



@Component({
  selector: 'app-orders',
  standalone: true,
  imports: [CommonModule, IconDropdownComponent, FormsModule, TranslateModule, SearchBoxComponent, DateRangePickerComponent, ServiceDropdownComponent, CurrencyConvertPipe, LoadingComponent],
  templateUrl: './orders.component.html',
  styleUrl: './orders.component.css',
  providers: [Clipboard, TranslateModule], // Removed OrderService to use the root-provided instance
})
export class OrdersComponent implements OnInit, OnDestroy {


  // orders = orders;
  // isAllChecked = false;
  // selected: { startDate: Date; endDate: Date } = {
  //   startDate: new Date(),
  //   endDate: new Date(),
  // };

  // private clipboard = inject(Clipboard);

  // toggleAllCheckboxes() {
  //   this.orders.forEach(item => item.isChecked = this.isAllChecked);
  // }

  // updateCheckAll() {
  //   this.isAllChecked = this.orders.every(item => item.isChecked);
  // }

  CopyID() {
    if (this.orders.length === 0) {
      console.log('No orders to copy');
      return;
    }

    // If there are selected orders, copy only those, otherwise copy all orders
    const ordersToCopy = this.selectedOrders.length > 0
      ? this.orders.filter(order => this.selectedOrders.includes(order.id))
      : this.orders;

    const formattedOrders = ordersToCopy.map(order => this.formatOrderForCopy(order)).join('\n');

    navigator.clipboard.writeText(formattedOrders)
    .then(() => {
      console.log('Orders copied to clipboard');
      this.toastService.showSuccess('Orders copied to clipboard successfully');
    })
    .catch(err => {
      console.error('Failed to copy orders: ', err);
      this.toastService.showError('Failed to copy orders: ' + err);
    });
  }

  /**
   * Format a single order for copying
   * Format: ID: 20 | 5/26/25, 1:11 PM
   * 1019 - Facebook post like vietnam | cheap | speed 1-2k / day | cancel button - 0.76922 $
   * https://www.facebook.com/DuySexy/posts/pfbid02DCvPD3aZFw6Z3KPgGgYD9CirK2r2UVTa866shbAgQtoyZi8zAcP265cf3BH66Bobl
   */
  formatOrderForCopy(order: OrderRes): string {
    const formattedDate = new Date(order.created_at).toLocaleString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: '2-digit',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    const price = this.currencyService.formatPrice(order.actual_charge || order.charge);

    return `ID: ${order.id} | ${formattedDate}\n${order.service.id} - ${order.service.name} - ${price}\n${order.link}`;
  }

  /**
   * Copy a single order with the same format
   */
  copySingleOrder(order: OrderRes): void {
    const formattedOrder = this.formatOrderForCopy(order);

    navigator.clipboard.writeText(formattedOrder)
    .then(() => {
      console.log('Order copied to clipboard');
      this.toastService.showSuccess('Order copied to clipboard successfully');
    })
    .catch(err => {
      console.error('Failed to copy order: ', err);
      this.toastService.showError('Failed to copy order: ' + err);
    });
  }

  // Refill() {
  //   console.log('Refill');
  // }


  searchTerm: string = '';
  selectedService: string = 'All';
  selectedServiceId: number | null = null;
  dateRange: { startDate: Date | null, endDate: Date | null } = {
    startDate: null,
    endDate: null
  };
  showFilters: boolean = false;
  viewMode: 'table' | 'card' = 'table'; // Default to table view, toggle to card view for mobile

  statusFilters: StatusFilter[] = [...STATUS_FILTERS];
  services: SuperGeneralSvRes[] = [];
  categories: IconBaseModel[] = [];
  allServices: SuperGeneralSvRes[] = [];
  platforms: SuperPlatformRes[] = [];
  selectAll: boolean = false;

  orders: OrderRes[] = [];
  selectedOrders: number[] = [];

  // Pagination
  pagination = {
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  };
  isLoading: boolean = false;
  private subscriptions: Subscription[] = [];

  constructor(
    private categoriesService: CategoriesService,
    private clipboard: Clipboard,
    private orderService: OrderService,
    private toastService: ToastService,
    private currencyService: CurrencyService
  ) { }

  // Format price with currency conversion
  formatPrice(price: number): string {
    return this.currencyService.formatPrice(price);
  }

  ngOnInit(): void {
    this.loadPlatforms();
    this.setupSubscriptions();
    this.loadOrders();
    this.detectMobileDevice();

    // Listen for window resize events to update view mode
    window.addEventListener('resize', () => {
      this.detectMobileDevice();
    });
  }

  /**
   * Detect if the device is mobile and set the view mode accordingly
   */
  detectMobileDevice(): void {
    // Check if screen width is less than 768px (typical mobile breakpoint)
    if (window.innerWidth < 768) {
      this.viewMode = 'card';
    } else {
      this.viewMode = 'table';
    }
  }

  setupSubscriptions() {
    // Subscribe to orders data
    this.subscriptions.push(
      this.orderService.orders$.subscribe(orders => {
        this.orders = orders;
        // Reset selection state when orders change
        this.selectedOrders = [];
        this.selectAll = false;
      })
    );

    // Subscribe to pagination data
    this.subscriptions.push(
      this.orderService.pagination$.subscribe(pagination => {
        this.pagination = pagination;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.orderService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());

    // Remove resize event listener
    window.removeEventListener('resize', () => {
      this.detectMobileDevice();
    });
  }

  /**
   * Load orders with current filters
   * @param page The page number (0-based)
   */
  loadOrders(page: number = 0): void {
    const filter: OrderSearchReq = {
      page: page,
      size: this.pagination.pageSize
    };

    if (this.searchTerm) {
      filter.keyword = this.searchTerm;
    }

    // Get active status filter
    const activeStatusFilter = this.statusFilters.find(f => f.active);
    if (activeStatusFilter && activeStatusFilter.value !== 'all') {
      filter.status = activeStatusFilter.value;
    }

    // Add date range filter if set
    if (this.dateRange) {
      console.log('Processing date range for API request:', this.dateRange);
      if (this.dateRange.startDate) {
        const year = this.dateRange.startDate.getFullYear();
        const month = (this.dateRange.startDate.getMonth() + 1).toString().padStart(2, '0');
        const day = this.dateRange.startDate.getDate().toString().padStart(2, '0');
        filter.from = `${year}-${month}-${day}`;
        console.log('Setting from date:', filter.from);
      }

      if (this.dateRange.endDate) {
        const year = this.dateRange.endDate.getFullYear();
        const month = (this.dateRange.endDate.getMonth() + 1).toString().padStart(2, '0');
        const day = this.dateRange.endDate.getDate().toString().padStart(2, '0');
        filter.to = `${year}-${month}-${day}`;
        console.log('Setting to date:', filter.to);
      }
    }

    // Add service ID filter if selected
    if (this.selectedServiceId) {
      filter.serviceId = this.selectedServiceId;
    }

    this.orderService.search(filter);
  }

  /**
   * Load platforms and categories from the API
   */
  loadPlatforms(): void {
    this.categoriesService.getPlatforms().subscribe({
      next: (platforms) => {
        this.platforms = platforms
          .filter(platform => !platform.hide)
          .sort((a, b) => a.sort - b.sort);

        // Create categories array from platforms data
        this.categories = [
          // Add an "All Categories" option at the top without an icon
          {
            id: 'all',
            label: 'filter.all_categories',
            sort: 0,
            icon: '' // No icon
          }
        ];
        this.allServices = [];

        platforms.forEach(platform => {
          // Sort categories by sort field before processing them
          const sortedCategories = [...platform.categories]
            .filter(category => !category.hide)
            .sort((a, b) => a.sort - b.sort);

          sortedCategories.forEach(category => {
            this.categories.push({
              id: category.id.toString(),
              sort: category.sort,
              label: category.name,
              icon: platform.icon
            });
            this.allServices.push(...category.services);
            // Add all services to allServices array


          });
        });

        // Select first category by default if available
        if (this.categories.length > 0) {
          this.onCategorySelected(this.categories[0]);
        }
      },
      error: (error) => {
        console.error('Error loading platforms:', error);
      }
    });
  }

  /**
   * Handle category selection and update services list
   */
  onCategorySelected(category: IconBaseModel): void {
    console.log('Category selected:', category);

    // Handle "All Categories" option
    if (category.id === 'all') {
      // For "All Categories", display all services from all categories
      this.services = [
        // Add an "All Services" option at the top using the factory method
        SuperGeneralSvRes.create({
          id: -1,
          name: 'filter.all_services',
          description: '',
          price: 0
        }),
        ...this.allServices
      ];
      console.log('All services loaded, count:', this.services.length);
      return;
    }

    // Find the selected category in platforms data and get its services
    for (const platform of this.platforms) {
      const foundCategory = platform.categories.find(c => c.id.toString() === category.id);
      if (foundCategory) {
        // Map SuperGeneralSvRes to GService
        this.services = [
          // Add an "All Services" option at the top using the factory method
          SuperGeneralSvRes.create({
            id: -1,
            name: 'filter.all_services',
            description: '',
            price: 0
          }),
          // Add category services
          ...foundCategory.services
        ];
        console.log('Services loaded for category:', category.label, 'Services count:', this.services.length);
        break;
      }
    }
  }

  toggleFilter(filter: any): void {
    this.statusFilters.forEach(f => f.active = false);
    filter.active = true;
    this.loadOrders();
  }

  /**
   * Handle service selection
   */
  onServiceSelected(service: SuperGeneralSvRes): void {
    console.log('Service selected:', service);

    // Handle "All Services" option
    if (service.id === -1) {
      console.log('All services selected');
      this.selectedServiceId = null;
    } else {
      // Handle specific service selection
      this.selectedServiceId = service.id;
      console.log('Selected service ID:', this.selectedServiceId);
    }

    // Load orders with the selected service
    this.loadOrders();
  }

  toggleAllOrders(): void {
    if (this.selectAll) {
      this.selectedOrders = this.orders.map(order => order.id);
    } else {
      this.selectedOrders = [];
    }
  }

  toggleOrderSelection(orderId: number): void {
    const index = this.selectedOrders.indexOf(orderId);
    if (index === -1) {
      this.selectedOrders.push(orderId);
    } else {
      this.selectedOrders.splice(index, 1);
    }

    // Update selectAll checkbox state
    this.selectAll = this.selectedOrders.length === this.orders.length;
  }

  isOrderSelected(orderId: number): boolean {
    return this.selectedOrders.includes(orderId);
  }


  applyFilters(): void {
    console.log('Applying filters with date range:', this.dateRange);
    console.log('Applying filters with service ID:', this.selectedServiceId);
    // Reset to first page when applying filters
    this.loadOrders(0);
  }

  /**
   * Handle date range changes from the date picker
   */
  onDateRangeChanged(dateRange: { startDate: Date | null, endDate: Date | null }): void {
    console.log('Date range changed:', dateRange);
    this.dateRange = dateRange;
    // Don't automatically load orders here as the user might want to set other filters first
  }

  resetFilters(): void {
    if (this.categories.length > 0) {
      this.onCategorySelected(this.categories[0]);
    }
    this.dateRange = {
      startDate: null,
      endDate: null
    };

    // Reset status filters
    this.statusFilters.forEach(f => f.active = false);
    this.statusFilters[0].active = true; // Set 'All' as active

    this.searchTerm = '';
    this.selectedServiceId = null;

    // Reload orders with reset filters
    this.loadOrders();
  }


 onSearch(searchTerm: string): void {
    console.log('Searching for:', searchTerm);
    this.searchTerm = searchTerm;
    this.loadOrders();
  }

  copyId(): void {
    const selectedOrderIds = this.selectedOrders.map(id => id.toString()).join('\n');
    if (selectedOrderIds) {
      this.clipboard.copy(selectedOrderIds);
      console.log('Order IDs copied to clipboard');
      this.toastService.showSuccess('Order IDs copied to clipboard successfully');
    }
  }

  reorder(order: OrderRes): void {
    if (order && order.service && order.service.id) {
      console.log('Reordering service with ID:', order.service.id);
      // Navigate to the new order page with the service ID as a parameter
      // Use window.location.href for direct navigation
      window.location.href = `/dashboard/new?serviceId=${order.service.id}`;
    }
  }

  /**
   * Check if refill is available for an order
   * @param order The order to check
   * @returns true if refill is available, false otherwise
   */
  isRefillAvailable(order: OrderRes): boolean {
    // Check if service supports refill
    if (!order.service.refill) {
      return false;
    }

    // Check if order status is COMPLETED or PARTIAL
    const validStatuses = ['completed', 'partial'];
    if (!validStatuses.includes(order.status.toLowerCase())) {
      return false;
    }

    // Check refill time window (order creation date + refill_days)
    if (order.service.refill_days && order.service.refill_days > 0) {
      const orderCreatedAt = new Date(order.created_at);
      const refillDeadline = new Date(orderCreatedAt.getTime() + (order.service.refill_days * 24 * 60 * 60 * 1000));
      const now = new Date();

      if (now > refillDeadline) {
        return false;
      }
    }

    return true;
  }

  /**
   * Refill an order
   * @param order The order to refill
   */
  refillOrder(order: OrderRes): void {
    if (!this.isRefillAvailable(order)) {
      this.toastService.showError('Refill is not available for this order');
      return;
    }

    // Set loading state for this specific order
    order.loading = true;

    this.orderService.refillOrder(order.id).subscribe({
      next: (updatedOrder) => {
        console.log('Order refilled successfully:', updatedOrder);
        this.toastService.showSuccess('Order refilled successfully');

        // Update the order in the list
        const index = this.orders.findIndex(o => o.id === order.id);
        if (index !== -1) {
          this.orders[index] = { ...updatedOrder, loading: false };
        }
      },
      error: (error) => {
        console.error('Error refilling order:', error);
        const errorMessage = error?.message || 'Failed to refill order';
        this.toastService.showError(errorMessage);
        order.loading = false;
      }
    });
  }

  /**
   * Bulk refill orders - filter eligible orders and refill them
   */
  bulkRefillOrders(): void {
    // Filter orders that are eligible for refill
    const eligibleOrders = this.orders.filter(order => this.isRefillAvailable(order));

    if (eligibleOrders.length === 0) {
      this.toastService.showError('No orders are eligible for refill');
      return;
    }

    // Confirm with user
    const confirmMessage = `Are you sure you want to refill ${eligibleOrders.length} eligible order(s)?`;
    if (!confirm(confirmMessage)) {
      return;
    }

    // Set loading state for eligible orders
    eligibleOrders.forEach(order => order.loading = true);

    let successCount = 0;
    let errorCount = 0;
    let completedCount = 0;

    // Process each eligible order
    eligibleOrders.forEach(order => {
      this.orderService.refillOrder(order.id).subscribe({
        next: (updatedOrder) => {
          console.log('Order refilled successfully:', updatedOrder);
          successCount++;

          // Update the order in the list
          const index = this.orders.findIndex(o => o.id === order.id);
          if (index !== -1) {
            this.orders[index] = { ...updatedOrder, loading: false };
          }

          completedCount++;
          this.checkBulkRefillCompletion(eligibleOrders.length, completedCount, successCount, errorCount);
        },
        error: (error) => {
          console.error('Error refilling order:', error);
          errorCount++;
          order.loading = false;

          completedCount++;
          this.checkBulkRefillCompletion(eligibleOrders.length, completedCount, successCount, errorCount);
        }
      });
    });
  }

  /**
   * Check if bulk refill operation is completed and show summary
   */
  private checkBulkRefillCompletion(totalCount: number, completedCount: number, successCount: number, errorCount: number): void {
    if (completedCount === totalCount) {
      if (successCount > 0 && errorCount === 0) {
        this.toastService.showSuccess(`Successfully refilled ${successCount} order(s)`);
      } else if (successCount > 0 && errorCount > 0) {
        this.toastService.showWarning(`Refilled ${successCount} order(s), ${errorCount} failed`);
      } else {
        this.toastService.showError(`Failed to refill all ${errorCount} order(s)`);
      }
    }
  }

  /**
   * Cancel an order
   * @param order The order to cancel
   */
  cancelOrder(order: OrderRes): void {
    // Set loading state for this specific order
    order.loading = true;

    this.orderService.cancelOrder(order.id).subscribe({
      next: (updatedOrder) => {
        console.log('Order cancelled successfully:', updatedOrder);
        this.toastService.showSuccess('Order cancelled successfully');

        // Update the order in the list
        const index = this.orders.findIndex(o => o.id === order.id);
        if (index !== -1) {
          this.orders[index] = { ...updatedOrder, loading: false };
        }
      },
      error: (error) => {
        console.error('Error cancelling order:', error);
        const errorMessage = error?.message || 'Failed to cancel order';
        this.toastService.showError(errorMessage);
        order.loading = false;
      }
    });
  }

  /**
   * Update order note
   * @param order The order to update
   * @param note The new note content
   */
  updateNote(order: OrderRes, note: string): void {
    this.orderService.updateNote(order.id, note).subscribe({
      next: (updatedOrder) => {
        console.log('Order note updated successfully:', updatedOrder);
        this.toastService.showSuccess('Note updated successfully');

        // Update the order in the list
        const index = this.orders.findIndex(o => o.id === order.id);
        if (index !== -1) {
          this.orders[index] = { ...updatedOrder };
        }
      },
      error: (error) => {
        console.error('Error updating order note:', error);
        const errorMessage = error?.message || 'Failed to update note';
        this.toastService.showError(errorMessage);
      }
    });
  }

  /**
   * Handle note blur event
   * @param order The order to update
   * @param event The blur event
   */
  onNoteBlur(order: OrderRes, event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    if (target && target.value !== (order.note || '')) {
      this.updateNote(order, target.value);
    }
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'bg-gray-100 text-gray-800';
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'PARTIAL':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'CANCELED':
        return 'bg-red-100 text-red-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  isShowCancelButton(order: OrderRes): boolean {
    return order.service.cancel_button && order.status.toLowerCase() !== 'canceled' && order.status.toLowerCase() !== 'cancelled' && order.status.toLowerCase() !== 'canceled_without_refund';
  }



  /**
   * Navigate to a specific page
   * @param page The page number (0-based)
   */
  goToPage(page: number): void {
    if (page < 0 || page >= this.pagination.totalPages) {
      return;
    }

    // Update the current page in the service
    this.loadOrders(page);
  }

  /**
   * Change the page size and reload orders
   */
  changePageSize(): void {
    // Reset to first page when changing page size
    this.loadOrders(0);
  }

  /**
   * Get the range of pages to display in pagination
   * Shows a window of pages around the current page
   */
  getPageRange(): number[] {
    const totalPages = this.pagination.totalPages;
    const currentPage = this.pagination.pageNumber;

    // If we have 7 or fewer pages, show all pages
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i);
    }

    // Otherwise, show a window around the current page
    let startPage = Math.max(1, currentPage - 1);
    let endPage = Math.min(totalPages - 2, currentPage + 1);

    // Adjust the window if we're near the beginning or end
    if (currentPage <= 2) {
      endPage = 3;
    } else if (currentPage >= totalPages - 3) {
      startPage = totalPages - 4;
    }

    return Array.from(
      { length: endPage - startPage + 1 },
      (_, i) => startPage + i
    );
  }
}
