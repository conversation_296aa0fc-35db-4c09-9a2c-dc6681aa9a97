package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.PlatformReq;
import tndung.vnfb.smm.dto.response.category.CategoryRes;
import tndung.vnfb.smm.dto.response.category.SuperCategoryRes;
import tndung.vnfb.smm.dto.response.platform.PlatformRes;
import tndung.vnfb.smm.dto.response.platform.SuperPlatformRes;
import tndung.vnfb.smm.entity.Category;
import tndung.vnfb.smm.entity.Platform;
import tndung.vnfb.smm.entity.Platform.PlatformBuilder;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class PlatformMapperImpl implements PlatformMapper {

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public Platform toEntity(PlatformReq req) {
        if ( req == null ) {
            return null;
        }

        PlatformBuilder platform = Platform.builder();

        platform.icon( req.getIcon() );
        platform.name( req.getName() );

        return platform.build();
    }

    @Override
    public PlatformRes toRes(Platform entity) {
        if ( entity == null ) {
            return null;
        }

        PlatformRes platformRes = new PlatformRes();

        platformRes.setCategories( categoryListToCategoryResList( entity.getCategories() ) );
        platformRes.setIcon( entity.getIcon() );
        if ( entity.getId() != null ) {
            platformRes.setId( entity.getId().intValue() );
        }
        platformRes.setName( entity.getName() );
        platformRes.setSort( entity.getSort() );

        return platformRes;
    }

    @Override
    public List<PlatformRes> toRes(List<Platform> entity) {
        if ( entity == null ) {
            return null;
        }

        List<PlatformRes> list = new ArrayList<PlatformRes>( entity.size() );
        for ( Platform platform : entity ) {
            list.add( toRes( platform ) );
        }

        return list;
    }

    @Override
    public SuperPlatformRes toSuperRes(Platform entity) {
        if ( entity == null ) {
            return null;
        }

        SuperPlatformRes superPlatformRes = new SuperPlatformRes();

        superPlatformRes.setCategories( categoryListToSuperCategoryResList( entity.getCategories() ) );
        superPlatformRes.setIcon( entity.getIcon() );
        if ( entity.getId() != null ) {
            superPlatformRes.setId( entity.getId().intValue() );
        }
        superPlatformRes.setName( entity.getName() );
        superPlatformRes.setSort( entity.getSort() );

        return superPlatformRes;
    }

    @Override
    public List<SuperPlatformRes> toSuperRes(List<Platform> entity) {
        if ( entity == null ) {
            return null;
        }

        List<SuperPlatformRes> list = new ArrayList<SuperPlatformRes>( entity.size() );
        for ( Platform platform : entity ) {
            list.add( toSuperRes( platform ) );
        }

        return list;
    }

    protected List<CategoryRes> categoryListToCategoryResList(List<Category> list) {
        if ( list == null ) {
            return null;
        }

        List<CategoryRes> list1 = new ArrayList<CategoryRes>( list.size() );
        for ( Category category : list ) {
            list1.add( categoryMapper.toRes( category ) );
        }

        return list1;
    }

    protected List<SuperCategoryRes> categoryListToSuperCategoryResList(List<Category> list) {
        if ( list == null ) {
            return null;
        }

        List<SuperCategoryRes> list1 = new ArrayList<SuperCategoryRes>( list.size() );
        for ( Category category : list ) {
            list1.add( categoryMapper.toSuperRes( category ) );
        }

        return list1;
    }
}
